// 加载环境变量
require('dotenv').config({ path: '.env.local' });

const { neon } = require("@neondatabase/serverless");

// 创建数据库连接
const sql = neon(process.env.DATABASE_URL);

async function initializeConsultationTable() {
  try {
    console.log("🔧 开始初始化Consultation表...");

    // 检查环境变量
    if (!process.env.DATABASE_URL) {
      console.error("❌ DATABASE_URL 环境变量未设置");
      console.log("请在项目根目录创建 .env.local 文件并添加:");
      console.log("DATABASE_URL=\"your-neon-database-url\"");
      return;
    }

    // 测试数据库连接
    const timeResult = await sql`SELECT NOW() as current_time`;
    console.log("✅ 数据库连接成功，当前时间:", timeResult[0].current_time);

    // 创建客户咨询表
    await sql`
      CREATE TABLE IF NOT EXISTS consultation (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(255) NOT NULL,
        company VARCHAR(200) NOT NULL,
        phone VARCHAR(50) NOT NULL,
        part_details TEXT NOT NULL,
        attachment_urls JSONB DEFAULT '[]'::jsonb,
        attachment_metadata JSONB DEFAULT '[]'::jsonb,
        source VARCHAR(100) NOT NULL DEFAULT 'website',
        product_page_path VARCHAR(500),
        product_name VARCHAR(200),
        status VARCHAR(20) DEFAULT 'new' CHECK (status IN ('new', 'processing', 'completed', 'closed')),
        priority VARCHAR(10) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
        assigned_to VARCHAR(100),
        notes TEXT,
        follow_up_date DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    console.log("✅ Consultation表创建成功");

    // 创建索引
    await sql`CREATE INDEX IF NOT EXISTS idx_consultation_email ON consultation(email)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_consultation_company ON consultation(company)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_consultation_status ON consultation(status)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_consultation_priority ON consultation(priority)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_consultation_created_at ON consultation(created_at)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_consultation_source ON consultation(source)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_consultation_assigned_to ON consultation(assigned_to)`;
    console.log("✅ 索引创建成功");

    // 创建更新时间戳的触发器函数
    await sql`
      CREATE OR REPLACE FUNCTION update_consultation_updated_at()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.updated_at = CURRENT_TIMESTAMP;
          RETURN NEW;
      END;
      $$ LANGUAGE plpgsql
    `;
    console.log("✅ 触发器函数创建成功");

    // 创建触发器
    await sql`
      DROP TRIGGER IF EXISTS trigger_consultation_updated_at ON consultation
    `;
    await sql`
      CREATE TRIGGER trigger_consultation_updated_at
          BEFORE UPDATE ON consultation
          FOR EACH ROW
          EXECUTE FUNCTION update_consultation_updated_at()
    `;
    console.log("✅ 触发器创建成功");

    // 添加表注释
    await sql`COMMENT ON TABLE consultation IS '客户技术咨询表'`;
    await sql`COMMENT ON COLUMN consultation.id IS '主键ID'`;
    await sql`COMMENT ON COLUMN consultation.name IS '客户姓名'`;
    await sql`COMMENT ON COLUMN consultation.email IS '客户邮箱'`;
    await sql`COMMENT ON COLUMN consultation.company IS '公司名称'`;
    await sql`COMMENT ON COLUMN consultation.phone IS '联系电话'`;
    await sql`COMMENT ON COLUMN consultation.part_details IS '详细需求描述'`;
    await sql`COMMENT ON COLUMN consultation.attachment_urls IS '附件URL数组(JSON)'`;
    await sql`COMMENT ON COLUMN consultation.attachment_metadata IS '附件元信息(JSON)'`;
    await sql`COMMENT ON COLUMN consultation.source IS '信息来源'`;
    await sql`COMMENT ON COLUMN consultation.product_page_path IS '产品页面路径'`;
    await sql`COMMENT ON COLUMN consultation.product_name IS '产品名称'`;
    await sql`COMMENT ON COLUMN consultation.status IS '状态: new, processing, completed, closed'`;
    await sql`COMMENT ON COLUMN consultation.priority IS '优先级: low, normal, high, urgent'`;
    await sql`COMMENT ON COLUMN consultation.assigned_to IS '负责人'`;
    await sql`COMMENT ON COLUMN consultation.notes IS '管理员备注'`;
    await sql`COMMENT ON COLUMN consultation.follow_up_date IS '跟进日期'`;
    await sql`COMMENT ON COLUMN consultation.created_at IS '创建时间'`;
    await sql`COMMENT ON COLUMN consultation.updated_at IS '最后更新时间'`;
    console.log("✅ 表注释添加成功");

    // 验证表结构
    const tableExists = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'consultation'
      ) as exists
    `;

    if (tableExists[0].exists) {
      console.log("✅ 表验证成功 - Consultation表已存在");
      
      // 检查表结构
      const columns = await sql`
        SELECT 
          column_name, 
          data_type, 
          is_nullable, 
          column_default
        FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = 'consultation'
        ORDER BY ordinal_position
      `;

      console.log("\n📋 表结构详情:");
      columns.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'YES' ? '(可空)' : '(非空)'} ${col.column_default ? `默认: ${col.column_default}` : ''}`);
      });

      // 检查现有数据
      const rowCount = await sql`SELECT COUNT(*) as count FROM consultation`;
      console.log(`\n📊 当前记录数: ${rowCount[0].count}`);
    } else {
      console.error("❌ 表验证失败 - Consultation表创建失败");
    }

    console.log("\n🎉 Consultation表初始化完成！");
    console.log("\n📌 下一步:");
    console.log("1. 表单提交API: /api/consultation");
    console.log("2. 管理后台: /admin-mzg/quotes");
    console.log("3. 测试表单功能");

  } catch (error) {
    console.error("❌ Consultation表初始化失败:", error);
    console.error("错误详情:", error.message);
  }
}

// 运行初始化
initializeConsultationTable(); 