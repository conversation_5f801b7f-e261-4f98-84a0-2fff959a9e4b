const { neon } = require('@neondatabase/serverless');
const sql = neon(process.env.DATABASE_URL);

async function addContentImages() {
  try {
    console.log('Adding content images to boring-machining post...');
    
    // 为 boring-machining 文章添加相关的内容图片
    const contentImages = [
      {
        id: 'img1',
        alt: 'Boring machining system components and tools',
        url: '/counterbore-tool.png',
        position: 'left'
      },
      {
        id: 'img2', 
        alt: 'Precision boring operation in progress',
        url: '/micro-counterbore.png',
        position: 'center'
      },
      {
        id: 'img3',
        alt: 'Advanced boring machining setup',
        url: '/interchangeable-pilot-counterbore.png',
        position: 'right'
      },
      {
        id: 'img4',
        alt: 'Boring machining quality results',
        url: '/gun-drill-anatomy.png',
        position: 'center'
      }
    ];
    
    const result = await sql`
      UPDATE blog_posts 
      SET 
        content_images = ${JSON.stringify(contentImages)},
        content_images_count = ${contentImages.length}
      WHERE slug = 'boring-machining'
      RETURNING title, content_images_count
    `;
    
    if (result.length > 0) {
      console.log('✅ Successfully added content images:');
      console.log('   Post:', result[0].title);
      console.log('   Images:', result[0].content_images_count);
      contentImages.forEach((img, index) => {
        console.log(`   ${index + 1}. ${img.alt} (${img.position})`);
      });
    } else {
      console.log('❌ Post not found or update failed');
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

addContentImages();
