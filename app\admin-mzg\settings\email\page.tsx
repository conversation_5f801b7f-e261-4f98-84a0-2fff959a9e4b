"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { Mail, Send, CheckCircle, XCircle, Loader2, ArrowLeft } from "lucide-react"
import Link from "next/link"

interface EmailSettings {
  apiToken: string
  fromEmail: string
  stream: string
  recipients: {
    to: string
    cc: string
    bcc: string
  }
  testEmail: string
}

export default function EmailSettingsPage() {
  const [settings, setSettings] = useState<EmailSettings>({
    apiToken: "da872e1f-a512-4f29-8936-c66aac191e2b",
    fromEmail: "<EMAIL>",
    stream: "outbound",
    recipients: {
      to: "<EMAIL>",
      cc: "",
      bcc: ""
    },
    testEmail: "<EMAIL>"
  })
  const [isLoading, setIsLoading] = useState(false)
  const [isTesting, setIsTesting] = useState(false)
  const [isConnected, setIsConnected] = useState<boolean | null>(null)
  const { toast } = useToast()

  // 页面加载时获取现有设置
  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      const response = await fetch("/api/admin-mzg/settings/email")
      if (response.ok) {
        const data = await response.json()
        setSettings(data)
        // 如果API token被隐藏，重置连接状态
        if (data.apiToken === "••••••••••••••••••••••••••••••••") {
          setIsConnected(null)
        }
      }
    } catch (error) {
      console.error("加载邮箱设置失败:", error)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.')
      setSettings(prev => ({
        ...prev,
        [parent]: {
          ...(prev[parent as keyof EmailSettings] as object),
          [child]: value
        }
      }))
    } else {
      setSettings(prev => ({
        ...prev,
        [field]: value
      }))
    }
  }

  const saveSettings = async () => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/admin-mzg/settings/email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(settings)
      })

      if (response.ok) {
        toast({
          title: "保存成功",
          description: "邮箱设置已保存",
        })
      } else {
        throw new Error("保存失败")
      }
    } catch (error) {
      toast({
        title: "保存失败",
        description: "请检查输入信息后重试",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const testConnection = async () => {
    if (!settings.apiToken) {
      toast({
        title: "测试失败",
        description: "请先输入API令牌",
        variant: "destructive"
      })
      return
    }

    setIsTesting(true)
    try {
      const response = await fetch("/api/admin-mzg/settings/email/test", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ apiToken: settings.apiToken })
      })

      const result = await response.json()
      
      if (response.ok) {
        setIsConnected(true)
        toast({
          title: "连接成功",
          description: "Postmark连接测试通过",
        })
      } else {
        setIsConnected(false)
        toast({
          title: "连接失败",
          description: result.error || "请检查API令牌是否正确",
          variant: "destructive"
        })
      }
    } catch (error) {
      setIsConnected(false)
      toast({
        title: "连接失败",
        description: "网络错误，请稍后重试",
        variant: "destructive"
      })
    } finally {
      setIsTesting(false)
    }
  }

  const sendTestEmail = async () => {
    if (!settings.testEmail) {
      toast({
        title: "发送失败",
        description: "请输入测试邮箱地址",
        variant: "destructive"
      })
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch("/api/admin-mzg/settings/email/send-test", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          ...settings,
          testEmail: settings.testEmail
        })
      })

      if (response.ok) {
        toast({
          title: "发送成功",
          description: "测试邮件已发送，请检查收件箱",
        })
      } else {
        const result = await response.json()
        throw new Error(result.error || "发送失败")
      }
    } catch (error) {
      toast({
        title: "发送失败",
        description: error instanceof Error ? error.message : "请检查邮箱设置",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="p-6">
      {/* 页面头部 */}
      <div className="mb-8">
        <div className="flex items-center space-x-4 mb-4">
          <Link href="/admin-mzg/settings">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">邮箱设置</h1>
            <p className="text-gray-600 mt-2">配置Postmark邮件服务和收发件人信息</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Postmark 系统配置 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Mail className="h-5 w-5 text-blue-600" />
              <span>Postmark 邮件系统配置</span>
            </CardTitle>
            <CardDescription>
              配置Postmark邮件服务的基本信息
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="apiToken">API 令牌</Label>
              <div className="flex space-x-2">
                <Input
                  id="apiToken"
                  type="password"
                  placeholder="da872e1f-a512-4f29-8936-c66aac191e2b"
                  value={settings.apiToken}
                  onChange={(e) => handleInputChange("apiToken", e.target.value)}
                />
                <Button 
                  onClick={testConnection} 
                  disabled={isTesting || !settings.apiToken}
                  size="sm"
                >
                  {isTesting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    "测试连接"
                  )}
                </Button>
              </div>
              <p className="text-xs text-gray-500">
                从Postmark控制台获取的Server API tokens
              </p>
              {isConnected !== null && (
                <div className={`flex items-center space-x-2 text-sm ${
                  isConnected ? "text-green-600" : "text-red-600"
                }`}>
                  {isConnected ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <XCircle className="h-4 w-4" />
                  )}
                  <span>{isConnected ? "连接成功" : "连接失败"}</span>
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="fromEmail">发件人邮箱</Label>
              <Input
                id="fromEmail"
                type="email"
                placeholder="<EMAIL>"
                value={settings.fromEmail}
                onChange={(e) => handleInputChange("fromEmail", e.target.value)}
              />
              <p className="text-xs text-gray-500">
                邮件的发送地址，需要在Postmark中验证
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="stream">消息流</Label>
              <Input
                id="stream"
                placeholder="outbound"
                value={settings.stream}
                onChange={(e) => handleInputChange("stream", e.target.value)}
              />
              <p className="text-xs text-gray-500">
                Postmark消息流设置
              </p>
            </div>
          </CardContent>
        </Card>

        {/* 邮件接收者配置 */}
        <Card>
          <CardHeader>
            <CardTitle>邮件接收者配置</CardTitle>
            <CardDescription>
              设置默认的收件人、抄送和密送地址
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="recipients.to">收件箱</Label>
              <Input
                id="recipients.to"
                type="email"
                placeholder="<EMAIL>"
                value={settings.recipients.to}
                onChange={(e) => handleInputChange("recipients.to", e.target.value)}
              />
              <p className="text-xs text-gray-500">
                主要收件人邮箱地址
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="recipients.cc">抄送邮箱 (CC)</Label>
              <Input
                id="recipients.cc"
                type="email"
                placeholder="<EMAIL>"
                value={settings.recipients.cc}
                onChange={(e) => handleInputChange("recipients.cc", e.target.value)}
              />
              <p className="text-xs text-gray-500">
                抄送收件人邮箱地址（可选）
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="recipients.bcc">密抄邮箱 (BCC)</Label>
              <Input
                id="recipients.bcc"
                type="email"
                placeholder="<EMAIL>"
                value={settings.recipients.bcc}
                onChange={(e) => handleInputChange("recipients.bcc", e.target.value)}
              />
              <p className="text-xs text-gray-500">
                密抄收件人邮箱地址（可选）
              </p>
            </div>
          </CardContent>
        </Card>

        {/* 测试邮件发送 */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Send className="h-5 w-5 text-green-600" />
              <span>测试邮件发送</span>
            </CardTitle>
            <CardDescription>
              发送测试邮件验证配置是否正确
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="testEmail">测试邮件地址</Label>
              <Input
                id="testEmail"
                type="email"
                placeholder="<EMAIL>"
                value={settings.testEmail}
                onChange={(e) => handleInputChange("testEmail", e.target.value)}
              />
              <p className="text-xs text-gray-500">
                输入您的邮箱地址以接收测试邮件
              </p>
            </div>

            <div className="flex space-x-4">
              <Button 
                onClick={saveSettings} 
                disabled={isLoading}
                className="flex-1"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : null}
                保存配置
              </Button>
              <Button 
                onClick={sendTestEmail} 
                disabled={isLoading || !settings.testEmail}
                variant="outline"
                className="flex-1"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Send className="h-4 w-4 mr-2" />
                )}
                发送测试邮件
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 