"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function BallEndMillingCuttersPage() {
  // Ball End Milling Cutters相关的默认图片
  const defaultBallEndImages = [
    "/images/D46-1.png",
    "/images/D45-1.png", 
    "/images/D50-1.png",
    "/images/D51-1.png",
    "/images/D43-1.png",
    "/images/D44-1.png",
    "/images/16mm-ball-end-mill.png",
    "/images/20mm-ball-end-mill.png",
    "/images/25mm-ball-end-mill.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/clamp-type-milling/ball-end-milling-cutters");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认图片
          setGalleryImages(defaultBallEndImages);
        }
      } else {
        // API请求失败，使用默认图片
        setGalleryImages(defaultBallEndImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认图片
      setGalleryImages(defaultBallEndImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认图片，避免显示无关图片
    setGalleryImages(defaultBallEndImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data (保持原有产品内容)
  const products = [
    {
      id: "ball-end-001",
      name: "ARPF Spherical/Right Angle Shoulder End Mill Shank",
      image: "/images/D46-1.png",
      description: "High precision profiling and shoulder finishing",
      insertType: "ZPFG.., ZCFW..",
      application: "高精度仿形及台肩精加工",
      pageNumber: "D46-D49",
      url: "/standard-tools/clamp-type-milling/ball-end-milling-cutters/ARPF",
    },
    {
      id: "ball-end-002",
      name: "ABPF Spherical End Mill/Cutter Head",
      image: "/images/D45-1.png",
      description: "Ball nose profiling precision milling",
      insertType: "ZPFG..",
      application: "球头仿形精铣加工",
      pageNumber: "D45",
      url: "/standard-tools/clamp-type-milling/ball-end-milling-cutters/ABPF",
    },
    {
      id: "ball-end-003",
      name: "BNM Spherical End Mill/Cutter Head",
      image: "/images/D50-1.png",
      description: "High precision profiling machining",
      insertType: "BNM..",
      application: "高精度仿形加工",
      pageNumber: "D50",
      url: "/standard-tools/clamp-type-milling/ball-end-milling-cutters/BNM",
    },
    {
      id: "ball-end-004",
      name: "T2139 Ball Precision Cutters/Cutter Head",
      image: "/images/D51-1.png",
      description: "High performance, high precision, capable of machining turbine blades",
      insertType: "P3200-D..",
      application: "高性能，高精度，可加工汽轮机叶片",
      pageNumber: "D51",
      url: "/standard-tools/clamp-type-milling/ball-end-milling-cutters/T2139",
    },
    {
      id: "ball-end-005",
      name: "BCF/BCFL Spherical End Mill",
      image: "/images/D43-1.png",
      description: "Ball nose profiling rough milling",
      insertType: "ZCET/ZCEW.., CPMT..",
      application: "球头仿形粗铣加工",
      pageNumber: "D43",
      url: "/standard-tools/clamp-type-milling/ball-end-milling-cutters/BCF-BCFL",
    },
    {
      id: "ball-end-006",
      name: "SWB Spherical End Mill",
      image: "/images/D44-1.png",
      description: "Ball nose profiling rough milling",
      insertType: "SWB, SPGA, SPMA, ZCMT..",
      application: "球头仿形粗铣加工",
      pageNumber: "D44",
      url: "/standard-tools/clamp-type-milling/ball-end-milling-cutters/SWB",
    },
    
  ]

  // Performance features
  const performanceFeatures = [
    {
      icon: "Shield",
      title: "Ultra-High Precision Finishing Performance",
      description: "ABPF, BNM, and T2139 series deliver unparalleled performance with innovative 'R' spiral blade design ensuring fast, smooth shearing action for superior surface roughness and 'V' type mounting for rock-solid insert security.",
    },
    {
      icon: "Zap", 
      title: "High-Performance Versatility and Profiling",
      description: "ARPF series offers versatile profiling solution performing Finishing, Planing, Side Cutting, Profiling, and Helical Milling with single toolholder system, achieving exceptional precision with amplitude below 0.01mm.",
    },
    {
      icon: "Target",
      title: "Heavy-Duty Roughing and Vibration-Dampening",
      description: "SWB series engineered for bulk material removal in unstable cutting environments, while BCF/BCFL series features convex blade surface creating sharp cutting edge while reducing cutting vibration and noise.",
    },
  ]

  // Helper function to render icons
  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case "Shield":
        return <Shield className="h-8 w-8 text-red-600" />
      case "Zap":
        return <Zap className="h-8 w-8 text-red-600" />
      case "Target":
        return <Target className="h-8 w-8 text-red-600" />
      default:
        return <Tool className="h-8 w-8 text-red-600" />
    }
  }

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Insert Systems & Technical Parameters",
      description: "Portfolio supports highly specialized range of ball nose inserts: ZCF/ZCFW for versatile ARPF series, ZPFG for high-precision finishing ABPF series, BNM for high-performance BNM series, P3200 for T2139 precision finishing, ZC for BCF vibration-dampening design, and SPGA/ZCMT/ZPMT for robust SWB roughing series.",
    },
    {
      title: "Cutter Body & Shank Design",
      description: "Bodies machined from high-performance alloy structural steel (BNM, T2139) for maximum rigidity and stability at high speeds. ARPF series offers Type A (cone neck) and Type B (straight neck) profiles, available in both steel and solid carbide for ultimate rigidity.",
    },
    {
      title: "Advanced Design Features",
      description: "High-precision V-grooves and unique clamping mechanisms ensure secure insert seating. R-Spiral blade design provides smooth cutting entry. Convex blade surface creates sharp, free-cutting edge while actively dampening vibration. Multi-edge capability with two-edge (BCF) and four-edge (BCFL) options.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Ball End Milling Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG Ball End Milling System
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  I am proud to present a detailed introduction to our comprehensive portfolio of Machine Clamped Ball End Mills. These tools represent the pinnacle of engineering for 3D contouring and high-precision surface finishing. The core of this system is the indexable ball nose insert, which allows for the creation of complex, free-form surfaces with exceptional accuracy and economic efficiency. Our portfolio is meticulously designed to provide a targeted solution for every stage of the machining process, from aggressive material removal in roughing to achieving mirror-like finishes in high-speed finishing operations.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/millingcutter1.png"
                    alt="MZG Professional Ball End Milling System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Performance</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of our Ball End Mill system is stratified to address specific and demanding manufacturing challenges, demonstrating our commitment to providing the right tool for every job.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Ultra-High Precision Finishing Performance:</strong> For the most critical finishing operations, our <strong>ABPF, BNM, and T2139 series</strong> deliver unparalleled performance. The <strong>ABPF</strong> series is a finishing specialist, featuring an innovative <strong>"R" spiral blade design</strong> that ensures a fast, smooth shearing action for superior surface roughness. It also incorporates a "V" type mounting for rock-solid insert security. The <strong>BNM and T2139</strong> series are built on a foundation of <strong>high-performance alloy structural steel</strong>, providing a rigid and stable platform for high-speed cutting.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>High-Performance Versatility and Profiling:</strong> The <strong>ARPF series</strong> is our most versatile profiling solution. Its key performance characteristic is its ability to perform a wide range of operations—including <strong>Finishing, Planing, Side Cutting, Profiling, and Helical Milling</strong>—with a single toolholder system. This versatility is enhanced by the option of both steel and high-rigidity carbide shanks, as well as straight and tapered neck designs to optimize reach and clearance.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Heavy-Duty Roughing and Vibration-Dampening:</strong> When the primary goal is bulk material removal, the <strong>SWB series</strong> is the designated workhorse. The <strong>BCF/BCFL series</strong> delivers a unique performance advantage through its innovative insert design. The <strong>convex surface of the blade</strong> creates a sharp cutting edge while simultaneously helping to <strong>reduce cutting vibration and noise</strong>.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3 text-sm text-gray-700">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Insert Systems:</strong> ZCF/ZCFW, ZPFG, BNM, P3200, ZC, and SPGA/ZCMT/ZPMT systems for comprehensive application coverage</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Precision Performance:</strong> ARPF achieves exceptional precision with amplitude below 0.01mm</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Versatile Operations:</strong> Single toolholder system for Finishing, Planing, Side Cutting, Profiling, and Helical Milling</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Advanced Features:</strong> R-Spiral blade design, convex blade surface, and multi-edge capability</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Material Construction:</strong> High-performance alloy structural steel for maximum rigidity and stability</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.insertType && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Insert:</span>
                          <span className="text-gray-900 text-right">{product.insertType}</span>
                        </div>
                      )}
                      {product.pageNumber && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Page:</span>
                          <span className="text-gray-900 text-right">{product.pageNumber}</span>
                        </div>
                      )}
                      {product.description && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.description}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultBallEndImages[0]}
                    alt="Ball End Milling Cutter Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                  </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultBallEndImages[imageIndex % defaultBallEndImages.length]
                  : defaultBallEndImages[index % defaultBallEndImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Ball End Milling Cutter Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                  </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Specifications</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Insert Systems & Technical Parameters":
                      return <Layers className="h-6 w-6 text-blue-600 mr-3" />
                    case "Cutter Body & Shank Design":
                      return <Shield className="h-6 w-6 text-green-600 mr-3" />
                    case "Advanced Design Features":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                    </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios & Processing</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Mold & Die Finishing:</strong> Primary application for creating final, highly polished surfaces of complex injection molds, die-casting molds, and forging dies</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Aerospace Component Manufacturing:</strong> Extensive use for machining complex 3D profiles on gas turbine blades, impellers, and structural airframe parts from titanium and superalloys</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Medical Implant Machining:</strong> Precision and ability to create organic shapes make these tools ideal for manufacturing prosthetic joints and custom medical implants</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Complex 3D Surfacing and Profiling:</strong> Core application for 3-axis, 4-axis, or 5-axis contouring work on complex parts</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Helical Interpolation:</strong> Used to create large, high-precision holes and circular features through helical milling paths</span>
                  </li>
                </ul>
              </div>

              {/* Machining Operations */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Machining Operations
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Machine Complex 3D Contours:</strong> Fundamental purpose is to accurately machine free-form surfaces, complex curves, and intricate profiles</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Achieve Superior Surface Finish:</strong> Produce parts with exceptional surface quality, minimizing or eliminating need for secondary polishing operations</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Stable High-Speed Solution:</strong> Enable high-speed and high-feed machining strategies by providing rigid, balanced, and secure tool system</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Primary Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Machine Complex 3D Contours:</strong> Fundamental purpose is to accurately machine free-form surfaces, complex curves, and intricate profiles</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Achieve Superior Surface Finish:</strong> Produce parts with exceptional surface quality, minimizing or eliminating need for secondary polishing operations</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Stable High-Speed Solution:</strong> Enable high-speed and high-feed machining strategies by providing rigid, balanced, and secure tool system</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  Performance Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Comprehensive Roughing-to-Finishing System:</strong> Complete portfolio of tools handling every stage of 3D machining, from initial roughing to final finishing</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Maximize Economic Efficiency:</strong> Reduce overall machining costs using durable, replaceable indexable inserts, lowering cost-per-part and improving productivity</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Indexable Ball Nose Insert System:</strong> Allows creation of complex, free-form surfaces with exceptional accuracy and economic efficiency</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/clamp-type-milling/ball-end-milling-cutters" />
          </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional Ball End Milling Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal ball end milling cutters for specific 3D contouring, aerospace component manufacturing, medical implant machining, and complex surface finishing applications. From ultra-high precision finishing to heavy-duty roughing, we provide comprehensive ball nose cutting solutions.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same clamp-type-milling directory
                const allClampTypeCategories = [
                  {
                    title: "Face Milling Cutters",
                    image: "/images/D04-1.png",
                    description: "High-performance indexable face milling solutions",
                    url: "/standard-tools/clamp-type-milling/face-milling-cutters",
                  },
                  {
                    title: "Right Angle Face Milling Cutters",
                    image: "/images/D03-1.png",
                    description: "Precise 90-degree shoulder machining solutions",
                    url: "/standard-tools/clamp-type-milling/right-angle-square-shoulder",
                  },
                  {
                    title: "High Feed Milling Cutter",
                    image: "/images/D07-2.png",
                    description: "Maximum productivity milling solutions",
                    url: "/standard-tools/clamp-type-milling/high-feed-milling-cutter",
                  },
                  {
                    title: "Chamfering Cutters",
                    image: "/images/2F45C.png",
                    description: "Precision chamfering and edge preparation",
                    url: "/standard-tools/clamp-type-milling/chamfering-cutters",
                  },
                  {
                    title: "Fillet Corner Rounding",
                    image: "/images/D34-1.png",
                    description: "Round nose milling for corner rounding",
                    url: "/standard-tools/clamp-type-milling/fillet-corner-rounding",
                  },
                ];
                
                return allClampTypeCategories.map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Clamp Type Milling" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
} 