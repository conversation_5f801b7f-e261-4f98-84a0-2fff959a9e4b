"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function GroovingSlottingPage() {
  // Grooving & Slotting相关的默认图片
  const defaultGroovingImages = [
    "/images/D79-1.png",
    "/images/D78-1.png", 
    "/images/D76-1.png",
    "/images/D75-1.png",
    "/images/D77-1.png",
    "/images/D73-1.png",
    "/images/D74-1.png",
    "/images/D81-2.png",
    "/images/D81-1.png",
    "/images/D84-1.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/clamp-type-milling/grooving-slotting");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认图片
          setGalleryImages(defaultGroovingImages);
        }
      } else {
        // API请求失败，使用默认图片
        setGalleryImages(defaultGroovingImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认图片
      setGalleryImages(defaultGroovingImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认图片，避免显示无关图片
    setGalleryImages(defaultGroovingImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data based on the Grooving & Slotting Milling Cutter system (保持原有产品数据)
  const products = [
    {
      id: "gs-001",
      name: "SMP01 three-sided edge milling cutter (bayonet type)",
      image: "/images/D79-1.png",
      description: "Bayonet type three-sided edge milling cutter for slot machining",
      series: "SMP01 Series",
      insertType: "XSEQ..",
      application: "槽加工",
      pageNumber: "D79",
    },
    {
      id: "gs-002",
      name: "SMP Three sided edge side groove cutter (cutter type)",
      image: "/images/D78-1.png",
      description: "Cutter type three-sided edge side groove cutter for milling operations",
      series: "SMP Series",
      insertType: "SPMG..",
      application: "铣槽加工",
      pageNumber: "D78",
    },
    {
      id: "gs-003",
      name: "DMP Three-sided Edge Milling Cutter (cutter Type)",
      image: "/images/D76-1.png", 
      description: "Cutter type three-sided edge milling cutter for slot and step machining",
      series: "DMP Series",
      insertType: "MPHT060304, MPHT080305, MPHT120408",
      application: "槽/切入加工, 台阶面加工",
      pageNumber: "D76",
    },
    {
      id: "gs-004",
      name: "DMP three-sided edge milling cutter (bayonet type)",
      image: "/images/D75-1.png",
      description: "Bayonet type three-sided edge milling cutter for slot machining",
      series: "DMP Series", 
      insertType: "MPHT060304, MPHT080305, MPHT120408",
      application: "槽加工",
      pageNumber: "D75",
    },
    {
      id: "gs-005",
      name: "SMP three-sided edge milling cutter (bayonet type)",
      image: "/images/D77-1.png",
      description: "Bayonet type three-sided edge milling cutter for drilling and slot machining",
      series: "SMP Series",
      insertType: "SPMG..",
      application: "钻孔/槽加工", 
      pageNumber: "D77",
    },
    {
      id: "gs-006",
      name: "HTS T-Slot Side Mill",
      image: "/images/D73-1.png",
      description: "T-slot side mill for slot and step machining operations",
      series: "HTS Series",
      insertType: "MPHT060304 或 CCMT060204",
      application: "槽/切入加工, 台阶面加工",
      pageNumber: "D73",
    },
    {
      id: "gs-007",
      name: "TMR T-Slot Side Mill", 
      image: "/images/D74-1.png",
      description: "T-slot side mill with round insert system for versatile machining",
      series: "TMR Series",
      insertType: "RPMT10T3, RPMT0802, RPMT1204, RCMT0602",
      application: "槽/切入加工, 台阶面加工",
      pageNumber: "D74",
    },
    {
      id: "gs-008",
      name: "ATS T-Slot Side Mill",
      image: "/images/D81-2.png",
      description: "Advanced T-slot side mill for precision slot and step machining",
      series: "ATS Series",
      insertType: "CCMT060204, CCMT080304, CCMT09T308, CCMT120408",
      application: "槽/切入加工, 台阶面加工", 
      pageNumber: "D81",
    },
    {
      id: "gs-009",
      name: "890 Integrated high power T cutter",
      image: "/images/D81-1.png",
      description: "高刚性一体式结构，使型腔加工更加轻松！",
      series: "890 Series",
      insertType: "N331.1A-054508",
      application: "高刚性一体式结构，使型腔加工更加轻松！",
      pageNumber: "D81",
    },
    {
      id: "gs-010",
      name: "SMT T-Slot Side Mill",
      image: "/images/D84-1.png",
      description: "Specialized T-slot side mill for T-slot machining operations",
      series: "SMT Series",
      insertType: "TGF32L, TT43L",
      application: "T型槽加工",
      pageNumber: "D84",
    },
  ]

  // Performance features
  const performanceFeatures = [
    {
      icon: "Shield",
      title: "High-Efficiency & Precision Slotting Performance",
      description: "SMP and DMP series feature stable arbor-mounted or bayonet-type disc design providing rigid platform for multi-tooth cutting, allowing high feed rates and excellent surface finishes on groove bottom and side walls for production environments requiring accurate slots in large components.",
    },
    {
      icon: "Zap", 
      title: "Dedicated T-Slot Roughing & Finishing Performance",
      description: "Complete T-slot solution stratified for different needs: HTS series with CCMT/MPHT inserts and TMR series with RPMT round inserts for economical roughing; SMT series with dedicated TGF/TT43L grooving inserts for precision finishing with superior accuracy on final T-slot dimensions.",
    },
    {
      icon: "Target",
      title: "Ultimate Power & High-Rigidity Performance",
      description: "890 Integrated High Power T Cutter delivers uncompromising performance with high-rigidity integral structure eliminating potential weak points, offering unwavering stability and vibration dampening for heavy cutting forces, ideal for deep T-slots and aggressive side milling.",
    },
  ]

  // Helper function to render icons
  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case "Shield":
        return <Shield className="h-8 w-8 text-red-600" />
      case "Zap":
        return <Zap className="h-8 w-8 text-red-600" />
      case "Target":
        return <Target className="h-8 w-8 text-red-600" />
      default:
        return <Tool className="h-8 w-8 text-red-600" />
    }
  }

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Cutter Body Formats & Insert Systems",
      description: "System available in Arbor-Mounted Disc Cutters (SMP, DMP series, 80-200mm diameter), Integral Shank T-Slot Cutters (HTS, TMR, ATS, SMT, 14-50mm diameter), and Integrated High-Power Cutters (890 Series, 96-115mm diameter). Supports wide array of inserts: Square (XSEQ, SPMG, MPHT), Rhombic (CCMT), Round (RCMT/RPMT), Grooving (TGF32L/TT43L), and Specialty (N331.1A) inserts.",
    },
    {
      title: "Secure Clamping & Multi-Tooth Design",
      description: "Robust screw-down clamping (M4x7, M5x13 with T15/T20 wrenches) ensures inserts held rigidly under heavy cutting loads. Disc cutters available with high number of teeth (up to 16) to maximize feed rates and productivity. Precision-machined insert pockets maintain tight tolerances for accurate cutting edge positioning.",
    },
    {
      title: "Advanced Design Features",
      description: "Cutting widths from 4mm to 20mm for arbor-mounted discs, with comprehensive size range from 50mm to over 300mm diameter. Standard metric and imperial bore sizes ensure universal compatibility. Through-coolant capabilities and optimized chip evacuation design for enhanced performance.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Grooving & Slotting Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG Three-Sided Edge and Side Groove Milling System
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  MZG Tool Machine Company, I am proud to present a detailed introduction to our comprehensive portfolio of Three-Sided Edge and Side Groove Milling Cutters. This tooling system is the pinnacle of efficiency for all grooving, slotting, and side-milling applications. The system's design is masterfully simple yet incredibly effective: multiple indexable inserts are secured around the periphery of a robust cutter body, allowing for the simultaneous machining of three faces in a single pass. This method guarantees superior parallelism, precise slot widths, and excellent surface finishes. Our portfolio is engineered into distinct platforms—large-diameter arbor-mounted discs for heavy-duty slotting, versatile shank-type cutters for T-slotting, and high-rigidity integrated cutters for the most demanding power applications.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/grooving-slotting-hero.png"
                    alt="MZG Professional Three-Sided Edge and Side Groove Milling System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
              <div
                key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
              </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Performance</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of our Three-Sided Milling system is best understood through its application-specific strengths, which demonstrate a deep commitment to solving distinct manufacturing challenges with precision and power.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>High-Efficiency & Precision Slotting Performance:</strong> For creating wide, open slots and grooves with high accuracy and efficiency, our <strong>SMP and DMP series</strong> are the premier choice. Their performance is rooted in the stable <strong>arbor-mounted or bayonet-type disc design</strong>, which provides a rigid platform for multi-tooth cutting. This allows for high feed rates and produces excellent surface finishes on the bottom and side walls of the groove.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Dedicated T-Slot Roughing & Finishing Performance:</strong> Machining T-slots requires a specialized tool, and our portfolio provides a complete solution. The <strong>HTS series</strong>, utilizing standard <strong>CCMT/MPHT</strong> inserts, and the <strong>TMR series</strong>, using robust <strong>RPMT</strong> round inserts, deliver outstanding performance for roughing out T-slots. The <strong>SMT series</strong> is a finishing specialist using dedicated <strong>TGF/TT43L</strong> grooving inserts for superior accuracy on final T-slot dimensions.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Ultimate Power & High-Rigidity Performance:</strong> For the most demanding T-slot and side milling operations, our <strong>890 Integrated High Power T Cutter</strong> delivers uncompromising performance. Its key advantage is the <strong>high-rigidity integral structure</strong>. Unlike cutters assembled from multiple parts, this single-piece design eliminates potential weak points, offering unwavering stability and vibration dampening for heavy cutting forces.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3 text-sm text-gray-700">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Diameter Range:</strong> 50-300+ mm for comprehensive applications</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Slot Width:</strong> 2-25+ mm with precise tolerance control</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Multi-Tooth Design:</strong> Up to 16 teeth for maximum productivity</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Insert Systems:</strong> Square, Rhombic, Round, Grooving, and Specialty inserts</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Three-Face Action:</strong> Simultaneous machining of three faces in single pass</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                          <span className="text-gray-900 text-right">{product.series}</span>
                        </div>
                      )}
                      {(product as any).diameter && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Diameter:</span>
                          <span className="text-gray-900 text-right">{(product as any).diameter}</span>
                        </div>
                      )}
                      {(product as any).slotWidth && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Slot Width:</span>
                          <span className="text-gray-900 text-right">{(product as any).slotWidth}</span>
                        </div>
                      )}
                      {product.insertType && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Insert:</span>
                          <span className="text-gray-900 text-right">{product.insertType}</span>
                        </div>
                      )}
                      {(product as any).coating && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Coating:</span>
                          <span className="text-gray-900 text-right">{(product as any).coating}</span>
                        </div>
                      )}
                      {(product as any).features && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Features:</span>
                          <span className="text-gray-900 text-right">{(product as any).features}</span>
                        </div>
                      )}
                      {product.description && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.description}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultGroovingImages[0]}
                    alt="Grooving & Slotting Milling Cutter Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                    </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultGroovingImages[imageIndex % defaultGroovingImages.length]
                  : defaultGroovingImages[index % defaultGroovingImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Grooving & Slotting Milling Cutter Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                    </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Cutter Body Formats & Insert Systems":
                      return <Layers className="h-6 w-6 text-blue-600 mr-3" />
                    case "Secure Clamping & Multi-Tooth Design":
                      return <Shield className="h-6 w-6 text-green-600 mr-3" />
                    case "Advanced Design Features":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                    </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios & Processing</h2>
            </div>
              <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>General Grooving and Slotting:</strong> Primary application involving creating open-ended or closed slots in workpieces, such as keyways or clearance grooves</span>
                  </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>T-Slot Milling:</strong> Critical application for manufacturing machine tool tables, fixture plates, and components requiring standardized T-slots for clamping</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Straddle Milling:</strong> Highly efficient production method using two disc cutters with spacer for simultaneous milling of parallel surfaces</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Side Milling and Shoulder Milling:</strong> Using cutter side to machine vertical walls or shoulders on parts</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Cutting Off / Slitting:</strong> Using thin three-sided cutter to part-off material or cut narrow slits</span>
                    </li>
                  </ul>
                </div>

              {/* Main Functions */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Main Functions
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Create Precise Grooves and Slots:</strong> Machine slots of specific width and depth with high accuracy and excellent parallelism between side walls</span>
                  </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Machine T-Slots to Standard Dimensions:</strong> Provide specialized tool for creating complex T-slot profile in roughing and finishing sequence</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Perform Efficient Side and Straddle Milling:</strong> Machine one or two parallel vertical surfaces with high productivity and dimensional control</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Robust and Stable Platform:</strong> Offer rigid cutter body and secure insert clamping system for heavy grooving and side milling operations</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Deliver Economical and Versatile Solution:</strong> Reduce machining costs through durable, multi-edge indexable inserts and flexible system</span>
                    </li>
                  </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/clamp-type-milling/grooving-slotting" />
            </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional Grooving & Slotting Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal three-sided edge and side groove milling cutters for precise grooving, T-slot machining, straddle milling, and side milling applications. From arbor-mounted disc cutters to integrated high-power solutions, we provide comprehensive cutting systems for superior parallelism and excellent surface finishes.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same clamp-type-milling directory
                const allClampTypeCategories = [
                  {
                    title: "Face Milling Cutters",
                    image: "/images/D04-1.png",
                    description: "High-performance indexable face milling solutions",
                    url: "/standard-tools/clamp-type-milling/face-milling-cutters",
                  },
                  {
                    title: "Right Angle Face Milling Cutters",
                    image: "/images/D03-1.png",
                    description: "Precise 90-degree shoulder machining solutions",
                    url: "/standard-tools/clamp-type-milling/right-angle-square-shoulder",
                  },
                  {
                    title: "Ball End Milling Cutters",
                    image: "/images/D46-1.png",
                    description: "3D contouring and surface finishing solutions",
                    url: "/standard-tools/clamp-type-milling/ball-end-milling-cutters",
                  },
                  {
                    title: "High Feed Milling Cutter",
                    image: "/images/D07-2.png",
                    description: "Maximum productivity milling solutions",
                    url: "/standard-tools/clamp-type-milling/high-feed-milling-cutter",
                  },
                  {
                    title: "Chamfering Cutters",
                    image: "/images/2F45C.png",
                    description: "Precision chamfering and edge preparation",
                    url: "/standard-tools/clamp-type-milling/chamfering-cutters",
                  },
                ];
                
                return allClampTypeCategories.map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Clamp Type Milling" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
}