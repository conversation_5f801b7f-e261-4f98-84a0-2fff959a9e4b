import { NextRequest, NextResponse } from 'next/server'
import { sql } from '@/lib/db'

// Email format validation function
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Get client IP address
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP.trim()
  }
  
  return 'unknown'
}

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json()
    
    // Validate email format
    if (!email || !isValidEmail(email)) {
      return NextResponse.json({ 
        success: false, 
        error: 'Please enter a valid email address' 
      }, { status: 400 })
    }

    const clientIP = getClientIP(request)

    // Check IP submission count with time-based restrictions
    const ipTracking = await sql`
      SELECT submission_count, last_submission, first_submission_in_window 
      FROM newsletter_ip_tracking 
      WHERE ip_address = ${clientIP}
    `

    if (ipTracking.length > 0) {
      const { submission_count, last_submission, first_submission_in_window } = ipTracking[0]
      const now = new Date()
      const lastSubmissionTime = new Date(last_submission)
      const firstSubmissionTime = new Date(first_submission_in_window)
      
      // Check if still in 12-hour cooldown period after reaching limit
      if (submission_count >= 3) {
        const timeSinceLastSubmission = now.getTime() - lastSubmissionTime.getTime()
        const twelveHoursInMs = 12 * 60 * 60 * 1000 // 12 hours in milliseconds
        
        if (timeSinceLastSubmission < twelveHoursInMs) {
          const remainingHours = Math.ceil((twelveHoursInMs - timeSinceLastSubmission) / (60 * 60 * 1000))
          return NextResponse.json({ 
            success: false, 
            error: `You have reached the submission limit. Please wait ${remainingHours} more hours before trying again.` 
          }, { status: 429 })
        } else {
          // Reset the counter after 12-hour cooldown
          await sql`
            UPDATE newsletter_ip_tracking 
            SET submission_count = 0, first_submission_in_window = NOW()
            WHERE ip_address = ${clientIP}
          `
        }
      } else {
        // Check if we're still within the 10-minute window
        const timeSinceFirstInWindow = now.getTime() - firstSubmissionTime.getTime()
        const tenMinutesInMs = 10 * 60 * 1000 // 10 minutes in milliseconds
        
        if (timeSinceFirstInWindow > tenMinutesInMs) {
          // Reset the window if more than 10 minutes have passed
          await sql`
            UPDATE newsletter_ip_tracking 
            SET submission_count = 0, first_submission_in_window = NOW()
            WHERE ip_address = ${clientIP}
          `
        } else if (submission_count >= 3) {
          // Within 10-minute window and reached limit
          return NextResponse.json({ 
            success: false, 
            error: 'You have submitted 3 times within 10 minutes. Please wait 12 hours before trying again.' 
          }, { status: 429 })
        }
      }
    }

    // Check if email is already subscribed
    const existingEmail = await sql`
      SELECT id FROM newsletter WHERE subscribe_mail = ${email}
    `

    if (existingEmail.length > 0) {
      return NextResponse.json({ 
        success: false, 
        error: 'This email has already subscribed to our Newsletter' 
      }, { status: 409 })
    }

    // Insert new subscription record
    const insertResult = await sql`
      INSERT INTO newsletter (subscribe_mail, ip_address) 
      VALUES (${email}, ${clientIP})
      RETURNING id
    `

    // Update or insert IP tracking record
    if (ipTracking.length > 0) {
      // Check if we need to update first_submission_in_window
      const { first_submission_in_window } = ipTracking[0]
      const now = new Date()
      const firstSubmissionTime = new Date(first_submission_in_window)
      const timeSinceFirstInWindow = now.getTime() - firstSubmissionTime.getTime()
      const tenMinutesInMs = 10 * 60 * 1000
      
      if (timeSinceFirstInWindow > tenMinutesInMs || !first_submission_in_window) {
        // Start a new 10-minute window
        await sql`
          UPDATE newsletter_ip_tracking 
          SET submission_count = 1, last_submission = NOW(), first_submission_in_window = NOW()
          WHERE ip_address = ${clientIP}
        `
      } else {
        // Increment within the current window
        await sql`
          UPDATE newsletter_ip_tracking 
          SET submission_count = submission_count + 1, last_submission = NOW() 
          WHERE ip_address = ${clientIP}
        `
      }
    } else {
      await sql`
        INSERT INTO newsletter_ip_tracking (ip_address, submission_count, first_submission_in_window, last_submission) 
        VALUES (${clientIP}, 1, NOW(), NOW())
      `
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Subscription successful! Thank you for subscribing to our Newsletter',
      id: insertResult[0].id
    })

  } catch (error) {
    console.error('Newsletter subscription failed:', error)
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Subscription failed, please try again later' 
    }, { status: 500 })
  }
}

// GET method for admin backend to get subscription list
export async function GET(request: NextRequest) {
  try {
    const newsletters = await sql`
      SELECT id, subscribe_mail, ip_address, created_at, is_active 
      FROM newsletter 
      ORDER BY created_at DESC
    `

    return NextResponse.json({ 
      success: true, 
      data: newsletters,
      total: newsletters.length
    })

  } catch (error) {
    console.error('Failed to get Newsletter list:', error)
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to get data' 
    }, { status: 500 })
  }
} 