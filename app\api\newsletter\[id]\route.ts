import { NextRequest, NextResponse } from 'next/server'
import { sql } from '@/lib/db'

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    
    if (isNaN(id)) {
      return NextResponse.json({ 
        success: false, 
        error: '无效的ID参数' 
      }, { status: 400 })
    }

    // 检查记录是否存在
    const existingRecord = await sql`
      SELECT id, subscribe_mail FROM newsletter WHERE id = ${id}
    `

    if (existingRecord.length === 0) {
      return NextResponse.json({ 
        success: false, 
        error: '记录不存在' 
      }, { status: 404 })
    }

    // 删除记录
    const deleteResult = await sql`
      DELETE FROM newsletter WHERE id = ${id}
    `

    return NextResponse.json({ 
      success: true, 
      message: '删除成功',
      deletedId: id
    })

  } catch (error) {
    console.error('删除Newsletter记录失败:', error)
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : '删除失败' 
    }, { status: 500 })
  }
} 