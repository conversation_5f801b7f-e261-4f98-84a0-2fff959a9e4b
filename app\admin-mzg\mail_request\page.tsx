"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Mail, MessageSquare, Users, TrendingUp, Calendar, Bell } from "lucide-react"

interface Stats {
  newsletterCount: number
  quoteCount: number
  todayNewsletter: number
  todayQuotes: number
}

export default function MailRequestPage() {
  const [stats, setStats] = useState<Stats>({
    newsletterCount: 0,
    quoteCount: 0,
    todayNewsletter: 0,
    todayQuotes: 0
  })
  const [loading, setLoading] = useState(true)

  // 获取统计数据
  const fetchStats = async () => {
    try {
      setLoading(true)
      
      // 获取Newsletter统计
      const newsletterResponse = await fetch('/api/newsletter')
      if (newsletterResponse.ok) {
        const newsletterData = await newsletterResponse.json()
        if (newsletterData.success) {
          const newsletters = newsletterData.data
          const today = new Date().toDateString()
          const todayCount = newsletters.filter((item: any) => {
            const itemDate = new Date(item.created_at).toDateString()
            return itemDate === today
          }).length

          setStats(prev => ({
            ...prev,
            newsletterCount: newsletters.length,
            todayNewsletter: todayCount
          }))
        }
      }

      // TODO: 获取报价请求统计（当API准备好时）
      // const quoteResponse = await fetch('/api/quotes')
      // if (quoteResponse.ok) {
      //   const quoteData = await quoteResponse.json()
      //   // 处理报价数据...
      // }

    } catch (error) {
      console.error('获取统计数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchStats()
  }, [])

  return (
    <div className="w-full px-6 py-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">邮件询价管理</h1>
        <p className="text-gray-600 mt-2">管理客户邮件、Newsletter订阅和产品询价请求</p>
      </div>

      {/* 统计概览 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Mail className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Newsletter订阅</p>
                <p className="text-2xl font-bold">{loading ? '...' : stats.newsletterCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <MessageSquare className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">报价请求</p>
                <p className="text-2xl font-bold">{loading ? '...' : stats.quoteCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Calendar className="h-6 w-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">今日Newsletter</p>
                <p className="text-2xl font-bold">{loading ? '...' : stats.todayNewsletter}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Bell className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">今日询价</p>
                <p className="text-2xl font-bold">{loading ? '...' : stats.todayQuotes}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 功能模块 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Newsletter管理 */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-medium">Newsletter管理</CardTitle>
              <Mail className="h-6 w-6 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">管理用户Newsletter订阅信息</p>
            <div className="space-y-3">
              <Link 
                href="/admin-mzg/mail_request/newsletter" 
                className="block w-full p-3 bg-blue-50 hover:bg-blue-100 rounded-lg border border-blue-200 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <span className="font-medium text-blue-700">📧 订阅管理</span>
                  <Badge variant="secondary">{stats.newsletterCount}</Badge>
                </div>
                <p className="text-sm text-blue-600 mt-1">查看、搜索、删除订阅记录</p>
              </Link>
              <div className="flex gap-2 text-sm">
                <span className="text-gray-500">今日新增:</span>
                <Badge variant="outline">{stats.todayNewsletter}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 报价请求管理 */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-medium">报价请求</CardTitle>
              <MessageSquare className="h-6 w-6 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">处理客户产品询价和报价请求</p>
            <div className="space-y-3">
              <Link 
                href="/admin-mzg/quotes" 
                className="block w-full p-3 bg-green-50 hover:bg-green-100 rounded-lg border border-green-200 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <span className="font-medium text-green-700">📋 报价管理</span>
                  <Badge variant="secondary">{stats.quoteCount}</Badge>
                </div>
                <p className="text-sm text-green-600 mt-1">查看和处理客户询价</p>
              </Link>
              <Link 
                href="/admin-mzg/quotes?status=new" 
                className="block text-sm text-green-600 hover:underline"
              >
                🔔 待处理请求
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* 邮件设置 */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-medium">邮件设置</CardTitle>
              <TrendingUp className="h-6 w-6 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">配置邮件系统和通知设置</p>
            <div className="space-y-3">
              <Link 
                href="/admin-mzg/settings/email" 
                className="block w-full p-3 bg-purple-50 hover:bg-purple-100 rounded-lg border border-purple-200 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <span className="font-medium text-purple-700">⚙️ 邮件配置</span>
                  <Badge variant="outline">已配置</Badge>
                </div>
                <p className="text-sm text-purple-600 mt-1">SMTP设置和邮件模板</p>
              </Link>
              <div className="text-sm text-gray-500">
                <div className="flex justify-between">
                  <span>邮件服务状态:</span>
                  <Badge className="bg-green-100 text-green-700">✅ 正常</Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 快速操作 */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>快速操作</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link 
              href="/admin-mzg/mail_request/newsletter"
              className="p-4 border rounded-lg hover:bg-gray-50 transition-colors text-center"
            >
              <Mail className="h-8 w-8 mx-auto mb-2 text-blue-600" />
              <p className="font-medium">查看Newsletter</p>
              <p className="text-sm text-gray-600">管理订阅用户</p>
            </Link>
            <Link 
              href="/admin-mzg/quotes"
              className="p-4 border rounded-lg hover:bg-gray-50 transition-colors text-center"
            >
              <MessageSquare className="h-8 w-8 mx-auto mb-2 text-green-600" />
              <p className="font-medium">处理询价</p>
              <p className="text-sm text-gray-600">回复客户请求</p>
            </Link>
            <Link 
              href="/admin-mzg/settings/email"
              className="p-4 border rounded-lg hover:bg-gray-50 transition-colors text-center"
            >
              <TrendingUp className="h-8 w-8 mx-auto mb-2 text-purple-600" />
              <p className="font-medium">邮件设置</p>
              <p className="text-sm text-gray-600">配置SMTP</p>
            </Link>
            <button 
              onClick={fetchStats}
              className="p-4 border rounded-lg hover:bg-gray-50 transition-colors text-center"
            >
              <Users className="h-8 w-8 mx-auto mb-2 text-orange-600" />
              <p className="font-medium">刷新数据</p>
              <p className="text-sm text-gray-600">更新统计</p>
            </button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 