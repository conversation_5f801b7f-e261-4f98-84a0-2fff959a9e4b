"use client"

import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function ChamferingCuttersPage() {
  // Chamfering Cutters相关的默认图片
  const defaultChamferingImages = [
    "/images/D55-1.png",
    "/images/D56-1.png",
    "/images/D57-1.png",
    "/images/D58-1.png",
    "/images/D59-1.png",
    "/images/D59-2.png",
    "/images/D60-1.png",
    "/images/D61-1.png",
    "/images/D61-2.png",
    "/images/D62-2.png",
    "/images/D62-1.png",
    "/images/D63-1.png",
    "/images/D65-1.png",
    "/images/D66-1.png",
    "/images/D67-1.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);
  
  // State to track hydration completion
  const [isHydrated, setIsHydrated] = useState(false);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/clamp-type-milling/chamfering-cutters");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认图片
          setGalleryImages(defaultChamferingImages);
        }
      } else {
        // API请求失败，使用默认图片
        setGalleryImages(defaultChamferingImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认图片
      setGalleryImages(defaultChamferingImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Hydration effect
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认图片，避免显示无关图片
    setGalleryImages(defaultChamferingImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data based on the MZG Clamp Type Chamfering Cutters system (保持原有产品数据)
  const products = [
    {
      id: "cc-001",
      name: "SSP 45° Positioning Chamfering",
      image: "/images/D55-1.png",
      description: "Multi-functional positioning chamfering cutter for center positioning, drilling and chamfering",
      series: "SSP Series",
      insertType: "TCMX..",
      application: "Center positioning, drilling and chamfering",
      pageNumber: "D55",
    },
    {
      id: "cc-002",
      name: "SSP07-60° Positioning Chamfering/Chamfering Cutter",
      image: "/images/D56-1.png",
      description: "Versatile positioning and chamfering cutter for multiple operations",
      series: "SSP Series",
      insertType: "DCEX0702..",
      application: "Positioning, chamfering, internal hole chamfering, grooving, engraving",
      pageNumber: "D56",
    },
    {
      id: "cc-003",
      name: "B45 Internal And External Chamfering",
      image: "/images/D57-1.png",
      description: "Dedicated cutter for internal and external chamfering operations",
      series: "B45 Series",
      insertType: "SPMT04, SPMG05/06/07/09, SCMT09",
      application: "Internal and external chamfering",
      pageNumber: "D57",
    },
    {
      id: "cc-004",
      name: "SSP-90°/120°/142° Chamfering Cutter",
      image: "/images/D58-1.png",
      description: "Multi-angle positioning drill and chamfering cutter system",
      series: "SSP Series",
      insertType: "SPGX/SPMG (05 to 09)",
      application: "Positioning drilling and multi-angle chamfering",
      pageNumber: "D58",
    },
    {
      id: "cc-005",
      name: "SB Internal And External Chamfering",
      image: "/images/D59-1.png",
      description: "Specialized for internal and external chamfering and end face grooving",
      series: "SB Series",
      insertType: "SPMW0903, SDMB26152",
      application: "Internal and external chamfering, end face grooving",
      pageNumber: "D59",
    },
    {
      id: "cc-006",
      name: "SD Internal And External Chamfering",
      image: "/images/D59-2.png",
      description: "Advanced internal and external chamfering with end face grooving capability",
      series: "SD Series",
      insertType: "SPMW0903, SDMB26152",
      application: "Internal and external chamfering, end face grooving",
      pageNumber: "D59",
    },
    {
      id: "cc-007",
      name: "TP30°/45°/60° Chamfering",
      image: "/images/D60-1.png",
      description: "Multi-angle cutting capability (30°/45°/60°) for chamfering applications. Suitable for machining centers and general milling machines.",
      series: "TP Series",
      insertType: "TPMN1603.., TPMN2204..",
      application: "30°/45°/60° cutting and chamfering for machining centers and general milling machines",
      pageNumber: "D60",
    },
    {
      id: "cc-008",
      name: "SSO-15° Chamfering",
      image: "/images/D61-1.png",
      description: "Suitable for general milling machines, CNC milling machines, CNC lathes, and drilling chamfering applications",
      series: "SSO Series",
      insertType: "TCMT1102..",
      application: "General milling machines, CNC milling machines, CNC lathes, drilling chamfering",
      pageNumber: "D61",
    },
    {
      id: "cc-009",
      name: "SSY-30° Chamfering",
      image: "/images/D61-2.png",
      description: "Suitable for general milling machines, CNC milling machines, CNC lathes, and drilling chamfering applications",
      series: "SSY Series",
      insertType: "TCMT0902.., TCMT1102.., TCMT16T3..",
      application: "General milling machines, CNC milling machines, CNC lathes, drilling chamfering",
      pageNumber: "D61",
    },
    {
      id: "cc-010",
      name: "SSH-60° Chamfering",
      image: "/images/D62-2.png",
      description: "Suitable for general milling machines, CNC milling machines, CNC lathes, and drilling chamfering applications",
      series: "SSH Series",
      insertType: "TCMT0902.., TCMT1102.., TCMT16T3..",
      application: "General milling machines, CNC milling machines, CNC lathes, drilling chamfering",
      pageNumber: "D62",
    },
    {
      id: "cc-011",
      name: "TCM-45° Chamfering",
      image: "/images/D62-1.png",
      description: "Suitable for general milling machines, CNC milling machines, CNC lathes, and drilling chamfering applications",
      series: "TCM Series",
      insertType: "TCMT16T3..",
      application: "General milling machines, CNC milling machines, CNC lathes, drilling chamfering",
      pageNumber: "D62",
    },
    {
      id: "cc-012",
      name: "SSK45° Discarding Chamfering",
      image: "/images/D63-1.png",
      description: "45° cutting capability for chamfering and V-groove cutting. Suitable for general milling machines, CNC milling machines, CNC lathes, and drilling applications.",
      series: "SSK Series",
      insertType: "ADNT160308SR",
      application: "45° cutting, chamfering, V-groove cutting for general milling machines, CNC milling machines, CNC lathes, drilling machines",
      pageNumber: "D63",
    },
    {
      id: "cc-013",
      name: "BMC/MC",
      image: "/images/D65-1.png",
      description: "Multiple angle capabilities for hole back and edge chamfering, reduces secondary clamping for time-efficient operations",
      series: "BMC/MC Series",
      insertType: "SDMT090308",
      application: "Multiple angle hole back and edge chamfering, reduces secondary clamping, time-efficient operations",
      pageNumber: "D65",
    },
    {
      id: "cc-014",
      name: "CA Inverted Countersunk Head",
      image: "/images/D66-1.png",
      description: "Specialized inverted countersunk head for double-sided chamfering of mold frames",
      series: "CA Series",
      insertType: "CCMT0602, CCMT09T3",
      application: "Double-sided chamfering of mold frames",
      pageNumber: "D66",
    },
    {
      id: "cc-015",
      name: "ECC-45° Discarding Chamfering (XCET31005R-30)",
      image: "/images/D67-1.png",
      description: "45° cutting capability for chamfering and V-groove cutting. Suitable for general milling machines, CNC milling machines, CNC lathes, and drilling applications.",
      series: "ECC Series",
      insertType: "XCET 310404ER",
      application: "45° cutting, chamfering, V-groove cutting for general milling machines, CNC milling machines, CNC lathes, drilling machines",
      pageNumber: "D67",
    },
  ]

  // Performance features
  const performanceFeatures = [
    {
      icon: "Shield",
      title: "Multi-Functional Positioning and V-Grooving Performance",
      description: "SSP series champions versatility with ability to execute multiple operations with single tool. Beyond standard chamfering, excels at central positioning drilling (spot drilling) creating precise starting point for subsequent drilling operations, and highly effective for creating V-grooves for weld preparation and decorative machining.",
    },
    {
      icon: "Zap",
      title: "Dedicated Internal and External Chamfering Performance",
      description: "B45, SB, and SD series deliver robust and economical performance for precise chamfering of both external edges and internal holes. Key characteristic is square-shaped insert providing four indexable cutting edges, maximizing insert life and delivering exceptional economy with consistent 45° chamfers.",
    },
    {
      icon: "Target",
      title: "Comprehensive Angle and Application Flexibility",
      description: "Portfolio designed to provide solution for any required angle. TP (30°/45°/60°), SSO (15°), SSY (30°), SSH (60°), TCM (45°), and SSK (45°) series offer dedicated angle geometry allowing engineers to select exact tool needed to meet specific drawing requirements with cost-effective flexibility.",
    },
  ]

  // Helper function to render icons
  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case "Shield":
        return <Shield className="h-8 w-8 text-red-600" />
      case "Zap":
        return <Zap className="h-8 w-8 text-red-600" />
      case "Target":
        return <Target className="h-8 w-8 text-red-600" />
      default:
        return <Tool className="h-8 w-8 text-red-600" />
    }
  }

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Insert Systems & Cutting Edge Technology",
      description: "Portfolio supports vast array of industry-standard inserts including Square Inserts (SPGX, SPMW, SDMB, AD.., AP..) prized for multiple cutting edges and economic value, Triangular Inserts (TCMX, TCMT, TPMN) for strong corner geometry, Rhombic Inserts (DCEX, CCMT/CCGT) for specialized positioning, and Specialty Inserts (XCET) for dedicated high-performance chamfering.",
    },
    {
      title: "Chamfering Angle Range & Precision",
      description: "System offers exceptionally broad range of fixed angles including 15°, 30°, 45°, 60°, 90°, 120°, and 142° to meet nearly any design specification. Tools available in solid integral shank design for direct clamping in standard toolholders with numerous standard shank diameters and lengths optimized for rigidity and reach.",
    },
    {
      title: "Advanced Mechanism & Operational Range",
      description: "Each model designed with specific operational diameter range covering micro-chamfering (Ø0~Ø5mm) for delicate work to large-scale edge breaking (Ø40~Ø50mm). BMC/MC series incorporates fine-tuning mechanism for precise adjustment of inverted chamfer size and robust internal structure designed for actuation without requiring spindle reversal.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Chamfering Solutions Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG Machine Clip Chamfering Cutters
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  These tools are an essential component of modern manufacturing, engineered to perform the critical tasks of edge breaking, chamfering, and countersinking with precision and economic efficiency. The core of our system is the indexable insert, which allows for multiple cutting edges to be utilized on a single insert and for easy replacement, drastically reducing tool cost and machine downtime. Our portfolio offers an extensive range of solutions, from multi-functional positioning cutters and versatile angle tools to highly specialized inverted countersinks, ensuring the perfect tool for every application across milling machines, machining centers, and lathes.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/D-CC-ALL.JPG"
                    alt="MZG Professional Chamfering Cutters System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
              <div
                key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
              </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Performance</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of our Chamfering Cutter system is stratified to address a wide spectrum of manufacturing needs, showcasing the depth and ingenuity of our engineering.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Multi-Functional Positioning and V-Grooving Performance:</strong> The <strong>SSP series</strong> is the champion of versatility. Its performance is defined by its ability to execute multiple operations with a single tool. Beyond standard chamfering, it excels at <strong>central positioning drilling (spot drilling)</strong>, creating a precise starting point for subsequent drilling operations. It is also highly effective for creating <strong>V-grooves</strong>, making it an invaluable tool for weld preparation and decorative machining.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Dedicated Internal and External Chamfering Performance:</strong> For applications requiring the precise chamfering of both external edges and internal holes, our <strong>B45, SB, and SD series</strong> deliver robust and economical performance. Their key performance characteristic is the use of a <strong>square-shaped insert</strong>, which provides <strong>four indexable cutting edges</strong>. This design maximizes the life of each insert, delivering exceptional economy.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Comprehensive Angle and Application Flexibility:</strong> Our portfolio is designed to provide a solution for any required angle. The performance of series like <strong>TP (30°/45°/60°), SSO (15°), SSY (30°), SSH (60°), TCM (45°), and SSK (45°)</strong> lies in their dedicated angle geometry. The <strong>TP series</strong>, in particular, offers a highly flexible system with interchangeable bodies and inserts to achieve multiple common angles.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3 text-sm text-gray-700">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Angle Range:</strong> 15°, 30°, 45°, 60°, 90°, 120°, 142° coverage</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Diameter Range:</strong> Ø0~Ø50mm comprehensive coverage</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Multi-Edge Design:</strong> Up to 8 cutting edges per insert</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Insert Systems:</strong> Square, Triangular, Rhombic, Specialty types</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Special Features:</strong> Inverted countersinking capabilities</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {!isHydrated ? (
                // Server-side rendering placeholder
                <div className="col-span-full flex justify-center items-center h-64">
                  <div className="animate-pulse text-gray-400">Loading products...</div>
                </div>
              ) : (
                products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                    <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                        <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                      <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                            <span className="text-gray-900 text-right">{product.series}</span>
                        </div>
                      )}
                        {(product as any).diameter && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Diameter:</span>
                            <span className="text-gray-900 text-right">{(product as any).diameter}</span>
                        </div>
                      )}
                        {(product as any).angle && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Angle:</span>
                            <span className="text-gray-900 text-right">{(product as any).angle}</span>
                        </div>
                      )}
                        {(product as any).insertType && (
                        <div className="flex justify-between">
                            <span className="font-medium text-gray-700">Insert:</span>
                            <span className="text-gray-900 text-right">{(product as any).insertType}</span>
                        </div>
                      )}
                        {(product as any).coating && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Coating:</span>
                            <span className="text-gray-900 text-right">{(product as any).coating}</span>
                        </div>
                      )}
                        {(product as any).features && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Features:</span>
                            <span className="text-gray-900 text-right">{(product as any).features}</span>
                        </div>
                      )}
                        {product.description && (
                        <div className="pt-2 border-t border-gray-100">
                            <p className="text-xs text-gray-600">{product.description}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                ))
              )}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultChamferingImages[0]}
                    alt="Chamfering Cutters Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                    </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultChamferingImages[imageIndex % defaultChamferingImages.length]
                  : defaultChamferingImages[index % defaultChamferingImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Chamfering Cutters Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                    </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Insert Systems & Cutting Edge Technology":
                      return <Layers className="h-6 w-6 text-blue-600 mr-3" />
                    case "Chamfering Angle Range & Precision":
                      return <Shield className="h-6 w-6 text-green-600 mr-3" />
                    case "Advanced Mechanism & Operational Range":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                    </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios & Processing</h2>
            </div>
              <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>General Edge Breaking:</strong> Most common application, used to remove sharp, hazardous burrs from milled or turned components for safety and improved part handling</span>
                  </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Hole Chamfering & Countersinking:</strong> Creating beveled entry on hole to guide pins, screws, or other components during assembly</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Weld Preparation:</strong> Using 45°, 60°, or V-groove cutters to prepare edges of plates for strong, full-penetration welds</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Positioning and Spot Drilling:</strong> Utilizing SSP series to create precise conical location for subsequent drill, preventing drill from "walking"</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Inverted or Back Chamfering:</strong> Critical process in aerospace and automotive industries for chamfering inaccessible back side of hole in single setup</span>
                    </li>
                  </ul>
                </div>

              {/* Main Functions */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Main Functions
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Create Precise and Consistent Chamfers:</strong> Fundamental purpose is to machine clean, accurate chamfers and countersinks at wide variety of specified angles</span>
                  </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Enhance Manufacturing Economy:</strong> Significantly lower tooling costs and reduce machine downtime through use of durable, multi-corner indexable inserts</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Multi-Functional Capability:</strong> Combine several operations, such as spot drilling, V-grooving, and chamfering, into single tool, increasing process efficiency</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Solve Complex Machining Challenges:</strong> Offer specialized solutions, like inverted countersinking, that reduce setups, simplify programming, and improve productivity</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Deliver Universal Machine Compatibility:</strong> Provide versatile system of tools effectively utilized on wide range of machine tools including CNC milling machines, machining centers, CNC lathes, and standard drilling machines</span>
                    </li>
                  </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/clamp-type-milling/chamfering-cutters" />
            </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional Chamfering Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal chamfering cutters for edge breaking, countersinking, and specialized chamfering applications. From multi-functional positioning cutters to inverted countersinking solutions, we provide comprehensive cutting systems for precision chamfering across all angles and materials.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same clamp-type-milling directory
                const allClampTypeCategories = [
                  {
                    title: "Face Milling Cutters",
                    image: "/images/D04-1.png",
                    description: "High-performance indexable face milling solutions",
                    url: "/standard-tools/clamp-type-milling/face-milling-cutters",
                  },
                  {
                    title: "Right Angle Face Milling Cutters",
                    image: "/images/D03-1.png",
                    description: "Precise 90-degree shoulder machining solutions",
                    url: "/standard-tools/clamp-type-milling/right-angle-square-shoulder",
                  },
                  {
                    title: "Ball End Milling Cutters",
                    image: "/images/D46-1.png",
                    description: "3D contouring and surface finishing solutions",
                    url: "/standard-tools/clamp-type-milling/ball-end-milling-cutters",
                  },
                  {
                    title: "High Feed Milling Cutter",
                    image: "/images/D07-2.png",
                    description: "Maximum productivity milling solutions",
                  url: "/standard-tools/clamp-type-milling/high-feed-milling-cutter",
                },
                {
                    title: "Grooving & Slotting",
                    image: "/images/D79-1.png",
                    description: "Three-sided edge and side groove milling",
                    url: "/standard-tools/clamp-type-milling/grooving-slotting",
                  },
                ];
                
                return allClampTypeCategories.map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Clamp Type Milling" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
}