"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair, Gauge } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function ThreadingInsertsPage() {
  // Threading Inserts相关的默认图片
  const defaultThreadingImages = [
    "/images/a61-1.png",
    "/images/a63-1.png",
    "/images/a65-1.png",
    "/images/a66-1.png",
    "/images/a66-2.png",
    "/images/a67-1.png",
    "/images/a67-2.png",
    "/images/a68-1.png",
    "/images/a68-2.png",
    "/images/a69-1.png",
    "/images/a69-2.png",
    "/images/a70-1.png",
    "/images/a71-1.png",
    "/images/a71-2.png",
    "/images/a72-1.png",
    "/images/a73-1.png",
    "/images/a74-1.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/lathe-turning-inserts/threading-inserts");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认Threading Inserts图片
          setGalleryImages(defaultThreadingImages);
        }
      } else {
        // API请求失败，使用默认Threading Inserts图片
        setGalleryImages(defaultThreadingImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认Threading Inserts图片
      setGalleryImages(defaultThreadingImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认Threading Inserts图片，避免显示无关图片
    setGalleryImages(defaultThreadingImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data based on various threading insert series
  const products = [
    {
      id: "ti-001",
      name: "60° Metric Threading Insert",
      image: "/images/a61-1.png",
      description: "Precision threading insert for metric standard threads",
      series: "Metric Series",
      standards: "ISO Metric",
      angle: "60°",
      application: "General purpose metric fastening threads",
      materials: "Steel, Stainless Steel, Cast Iron",
      pageNumber: "A61",
    },
    {
      id: "ti-002",
      name: "55° Whitworth Thread Insert",
      image: "/images/a63-1.png",
      description: "British standard Whitworth threading insert",
      series: "Whitworth Series",
      standards: "BSW/BSF",
      angle: "55°",
      application: "British standard fastening applications",
      materials: "Steel, Cast Iron, Brass",
      pageNumber: "A63",
    },
    {
      id: "ti-003",
      name: "60° Unified Thread Insert",
      image: "/images/a65-1.png",
      description: "American unified thread standard insert",
      series: "UN Series",
      standards: "UN/UNC/UNF",
      angle: "60°",
      application: "American standard fastening threads",
      materials: "Steel, Aluminum, Stainless Steel",
      pageNumber: "A65",
    },
    {
      id: "ti-004",
      name: "60° UNJ Aviation Industry Thread Insert",
      image: "/images/a66-1.png",
      description: "Aerospace industry UNJ threading insert with controlled root radius",
      series: "UNJ Series",
      standards: "UNJ",
      angle: "60°",
      application: "Aerospace fasteners, high fatigue resistance",
      materials: "Titanium, Superalloys, High-strength Steel",
      pageNumber: "A66",
    },
    {
      id: "ti-005",
      name: "80° PG Conduit Thread Insert",
      image: "/images/a66-2.png",
      description: "Steel conduit threading insert for electrical applications",
      series: "PG Series",
      standards: "PG (DIN405)",
      angle: "80°",
      application: "Electrical conduit and cable gland threads",
      materials: "Steel, Aluminum, Brass",
      pageNumber: "A66",
    },
    {
      id: "ti-006",
      name: "BSPT Thread Insert",
      image: "/images/a67-1.png",
      description: "British standard taper pipe threading insert",
      series: "BSPT Series",
      standards: "BSPT",
      angle: "55°",
      application: "British standard pipe threads, plumbing fittings",
      materials: "Steel, Cast Iron, Brass",
      pageNumber: "A67",
    },
    {
      id: "ti-007",
      name: "RND Thread Insert",
      image: "/images/a67-2.png",
      description: "Round thread pipe threading insert for special applications",
      series: "RND Series",
      standards: "RND (DIN405)",
      angle: "Round",
      application: "Fire fighting equipment, food industry",
      materials: "Steel, Stainless Steel",
      pageNumber: "A67",
    },
    {
      id: "ti-008",
      name: "NPT Thread Insert",
      image: "/images/a68-1.png",
      description: "American standard taper pipe threading insert",
      series: "NPT Series",
      standards: "NPT",
      angle: "60°",
      application: "American standard pipe threads, pressure fittings",
      materials: "Steel, Stainless Steel",
      pageNumber: "A68",
    },
    {
      id: "ti-009",
      name: "NPTF Thread Insert",
      image: "/images/a68-2.png",
      description: "American standard dryseal taper pipe threading insert",
      series: "NPTF Series",
      standards: "NPTF",
      angle: "60°",
      application: "Dryseal pipe connections, hydraulic fittings",
      materials: "Steel, Stainless Steel",
      pageNumber: "A68",
    },
    {
      id: "ti-010",
      name: "29° ACME Thread Insert",
      image: "/images/a69-1.png",
      description: "American standard trapezoidal threading insert",
      series: "ACME Series",
      standards: "ACME",
      angle: "29°",
      application: "Power transmission, lead screws, jacks",
      materials: "Steel, Bronze, Cast Iron",
      pageNumber: "A69",
    },
    {
      id: "ti-011",
      name: "29° STUB ACME Thread Insert",
      image: "/images/a69-2.png",
      description: "American standard stub trapezoidal threading insert",
      series: "STUB ACME Series",
      standards: "STUB ACME",
      angle: "29°",
      application: "Short engagement power transmission",
      materials: "Steel, Bronze, Cast Iron",
      pageNumber: "A69",
    },
    {
      id: "ti-012",
      name: "30° Trapezoidal Thread Insert",
      image: "/images/a70-1.png",
      description: "Metric trapezoidal threading insert",
      series: "TR Series",
      standards: "TR (Trapezoidal)",
      angle: "30°",
      application: "Machine tool slides, valve stems, linear motion",
      materials: "Steel, Cast Iron, Bronze",
      pageNumber: "A70",
    },
    {
      id: "ti-013",
      name: "ABUT 45°+7° Buttress Thread Insert",
      image: "/images/a71-1.png",
      description: "Asymmetric buttress threading insert for high axial loads",
      series: "ABUT Series",
      standards: "ABUT Buttress",
      angle: "45°+7°",
      application: "High unidirectional axial force applications",
      materials: "Steel, Hardened Steel",
      pageNumber: "A71",
    },
    {
      id: "ti-014",
      name: "BBUT 45°+7° Buttress Thread Insert",
      image: "/images/a71-2.png",
      description: "Heavy-duty buttress threading insert",
      series: "BBUT Series",
      standards: "BBUT Buttress",
      angle: "45°+7°",
      application: "Heavy-duty axial load transmission",
      materials: "Steel, Hardened Steel",
      pageNumber: "A71",
    },
    {
      id: "ti-015",
      name: "30°+3° SAGE Buttress Thread Insert",
      image: "/images/a72-1.png",
      description: "SAGE buttress threading insert for extreme loads",
      series: "SAGE Series",
      standards: "SAGE Buttress",
      angle: "30°+3°",
      application: "Extreme axial load applications, heavy machinery",
      materials: "Steel, Hardened Steel, Tool Steel",
      pageNumber: "A72",
    },
    {
      id: "ti-016",
      name: "API Petroleum Pipe Thread Insert",
      image: "/images/a73-1.png",
      description: "API standard petroleum pipe threading insert",
      series: "API Series",
      standards: "API",
      angle: "60°",
      application: "Oil & gas drilling, petroleum equipment",
      materials: "Steel, Alloy Steel",
      pageNumber: "A73",
    },
    {
      id: "ti-017",
      name: "BUT Casing Thread Insert",
      image: "/images/a74-1.png",
      description: "Buttress casing thread insert for oil & gas industry",
      series: "BUT Series",
      standards: "BUT Casing",
      angle: "Variable",
      application: "Oil well casing, downhole equipment",
      materials: "Steel, Alloy Steel",
      pageNumber: "A74",
    },
    {
      id: "ti-018",
      name: "EL Casing Thread Insert",
      image: "/images/a74-1.png",
      description: "External left-hand casing thread insert",
      series: "EL Series",
      standards: "EL Casing",
      angle: "Variable",
      application: "Oil well casing connections, tubing",
      materials: "Steel, Alloy Steel",
      pageNumber: "A74",
    },
  ]

  // Performance features
  const performanceFeatures = [
    {
      icon: "Gauge",
      title: "General Fastening Thread Performance",
      description: "For creating the world's most common mechanical connections, our inserts for 60° Metric (ISO) and 60° Unified (UN, UNC, UNF) threads deliver superior performance with precision-ground full profile ensuring perfect thread form.",
    },
    {
      icon: "Target",
      title: "High-Integrity Sealing Thread Performance",
      description: "Our pipe thread inserts provide exceptional sealing performance for tapered threads like NPT, NPTF (American Standard) and BSPT (British Standard) with exacting precision on both flank angles and taper angle.",
    },
    {
      icon: "Shield",
      title: "Robust Power Transmission Thread Performance",
      description: "For applications that convert rotary motion into linear motion, our trapezoidal and buttress thread inserts deliver outstanding performance in strength and efficiency with strong, wide profiles capable of handling high loads.",
    },
  ]

  // Helper function to render icons
  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case "Gauge":
        return <Gauge className="h-8 w-8 text-red-600" />
      case "Target":
        return <Target className="h-8 w-8 text-red-600" />
      case "Shield":
        return <Shield className="h-8 w-8 text-red-600" />
      default:
        return <Tool className="h-8 w-8 text-red-600" />
    }
  }

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Thread Profile",
      description: "Each insert is ground to a specific profile defined by its flank angle, crest and root specifications. Key profiles include V-Threads: 60° (Metric, UN, NPT), 55° (Whitworth, BSPT), Trapezoidal Threads: 29° (ACME), 30° (TR), Buttress Threads: Asymmetrical profiles like 45°+7° (ABUT, BBUT) and 30°+3° (SAGE), Round Threads (RND), and Specialized Profiles: API, BUT, EL, UNJ with specific root radii and tapers.",
    },
    {
      title: "Insert Type & Size",
      description: "Our system uses the standard ISO designation: ER (External Right-hand), EL (External Left-hand), IR (Internal Right-hand), and IL (Internal Left-hand). Inserts are available in standard ISO sizes (e.g., 11, 16, 22, 27), which correspond to the inscribed circle (IC) diameter and thickness, determining which standard tool holder they fit.",
    },
    {
      title: "Grade and Coating",
      description: "Our inserts are available in a range of advanced coated carbide grades allowing a single insert profile to be effectively used on a wide range of materials, including Steel (P), Stainless Steel (M), Cast Iron (K), and even Titanium/Superalloys (S), simply by selecting the appropriate material-specific grade.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Threading Inserts Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG Threading Inserts System
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  Threading Inserts. The screw thread is a fundamental mechanical feature, and its precise and reliable creation is paramount to the function and safety of countless products. Our threading insert system is built on a foundation of precision-ground profiles and advanced material grades, designed to provide a complete solution for any threading challenge. Each insert is a product of meticulous engineering, designed to cut a specific, standard-compliant thread profile with exceptional accuracy and surface finish. Our portfolio covers the full spectrum of applications, from general-purpose fastening to high-pressure sealing and mission-critical power transmission.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/A-INS-ALL.JPG"
                    alt="MZG Professional Threading Inserts System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Gauge":
                    return <Gauge className="h-6 w-6 text-blue-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-green-600 mr-3" />
                  case "Shield":
                    return <Shield className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
              <div
                key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
              </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Performance</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of our threading inserts is best categorized by their primary application, as each profile is engineered to fulfill a distinct functional requirement.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>General Fastening Thread Performance:</strong> For creating the world's most common mechanical connections, our inserts for <strong>60° Metric (ISO)</strong> and <strong>60° Unified (UN, UNC, UNF)</strong> threads deliver superior performance. The precision-ground full profile ensures a perfect thread form, guaranteeing interchangeability and optimal load distribution for maximum fastening strength. Similarly, our <strong>55° Whitworth (BSW, BSF)</strong> inserts provide reliable performance for British standard fastening applications. The performance benchmark here is consistency, reliability, and adherence to global standards.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>High-Integrity Sealing Thread Performance:</strong> When a leak-proof connection is required, our pipe thread inserts provide exceptional sealing performance. Inserts for tapered threads like <strong>NPT, NPTF (American Standard)</strong> and <strong>BSPT (British Standard)</strong> are ground with exacting precision on both the flank angles and the taper angle. The performance of <strong>NPTF (Dryseal)</strong> inserts is particularly noteworthy, as their profile is designed to create a metal-to-metal seal without the need for chemical sealants.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Robust Power Transmission Thread Performance:</strong> For applications that convert rotary motion into linear motion, our trapezoidal and buttress thread inserts deliver outstanding performance in strength and efficiency. Our <strong>30° Trapezoidal (TR)</strong> and <strong>29° ACME / STUB ACME</strong> inserts feature a strong, wide profile capable of handling high loads, making them ideal for lead screws, jacks, and machine tool components.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3 text-sm text-gray-700">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Full Profile Design:</strong> Complete thread form machined simultaneously for perfect compliance</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Standard Compliance:</strong> ISO, UN, NPT, API and other global standards</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Material Versatility:</strong> Steel, stainless steel, titanium, superalloys coverage</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Advanced Coatings:</strong> CVD/PVD coated grades for extended tool life</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Process Security:</strong> Robust design ensures reliable, repeatable performance</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                          <span className="text-gray-900 text-right">{product.series}</span>
                        </div>
                      )}
                      {product.standards && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Standards:</span>
                          <span className="text-gray-900 text-right">{product.standards}</span>
                        </div>
                      )}
                      {product.angle && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Angle:</span>
                          <span className="text-gray-900 text-right">{product.angle}</span>
                        </div>
                      )}
                      {product.description && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.description}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultThreadingImages[0]}
                    alt="Threading Inserts Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
          </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultThreadingImages[imageIndex % defaultThreadingImages.length]
                  : defaultThreadingImages[index % defaultThreadingImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Threading Inserts Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                    </div>
                );
              })}
            </div>
          </div>

          {/* Technical Parameters */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Thread Profile":
                      return <Layers className="h-6 w-6 text-blue-600 mr-3" />
                    case "Insert Type & Size":
                      return <Settings className="h-6 w-6 text-green-600 mr-3" />
                    case "Grade and Coating":
                      return <Shield className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                    </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                      </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios & Applicable Machining</h2>
            </div>
              <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>General Engineering & Machine Building:</strong> Creating standard metric and unified threads for bolts, studs, and all forms of mechanical fastening</span>
                  </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Automotive Industry:</strong> Machining threads for engine components, drivetrain parts, and chassis fasteners</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Aerospace Industry:</strong> Producing high-strength UNJ threads with superior fatigue resistance for critical airframe and engine components</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Oil & Gas Industry:</strong> Machining robust and reliable API, Casing, and Tubing threads for downhole equipment, drill pipes, and wellhead connections</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Hydraulics & Pneumatics:</strong> Cutting tapered NPT, NPTF, and BSPT pipe threads for leak-proof fittings, valves, and cylinders</span>
                    </li>
                  </ul>
                </div>

              {/* Machining Operations */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Machining Operations
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>External Threading:</strong> Creating external threads on shafts, bolts, and cylindrical components using ER and EL series inserts</span>
                  </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Internal Threading:</strong> Machining internal threads in holes, nuts, and threaded bores using IR and IL series inserts</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Tapered Pipe Threading:</strong> Creating NPT, NPTF, and BSPT tapered threads for pressure-tight connections</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Power Transmission Threading:</strong> Machining ACME and trapezoidal threads for lead screws and motion control applications</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Specialized Threading:</strong> Creating API, UNJ, buttress, and other mission-critical thread profiles for industry-specific applications</span>
                    </li>
                  </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Primary Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Produce Standard-Compliant Threads:</strong> Accurately replicate internationally recognized thread standards (ISO, UN, BSW, NPT, API, etc.)</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Guarantee Precision and Interchangeability:</strong> Create threads with such high accuracy that they ensure perfect fit and function with their mating parts</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Universal Threading Coverage:</strong> Offer a comprehensive system that provides a solution for virtually every known thread type</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  Performance Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Enable High-Performance Machining Across All Materials:</strong> Allow efficient threading of the full spectrum of industrial materials with material-specific carbide grades</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Ensure Process Security and Economic Efficiency:</strong> Provide robust, reliable, and indexable tooling solution that delivers high-quality threads, long tool life, and excellent cost-per-thread</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Full Profile Design Advantage:</strong> Machine the complete thread form simultaneously, ensuring burr-free crests and eliminating secondary operations</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/lathe-turning-inserts/threading-inserts" />
          </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional Threading Insert Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal threading inserts for specific thread standards and material requirements. From general fastening to specialized industry applications, we provide comprehensive threading solutions for all materials and thread types.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same lathe-turning-inserts directory
                const allTurningCategories = [
                {
                  title: "Turning Inserts",
                    image: "/images/turning-insert.png",
                    description: "Comprehensive turning insert systems",
                  url: "/standard-tools/lathe-turning-inserts/turning-inserts",
                },
                {
                  title: "Back Turning Inserts",
                    image: "/images/back-turning-insert.png",
                  description: "Specialized inserts for back turning operations",
                  url: "/standard-tools/lathe-turning-inserts/back-turning-inserts",
                },
                {
                    title: "Drilling Inserts",
                    image: "/images/drilling-insert.png",
                    description: "Indexable drilling insert systems",
                    url: "/standard-tools/lathe-turning-inserts/drilling-inserts",
                  },
                  {
                    title: "Grooving Cut-off Inserts",
                    image: "/images/grooving-insert.png",
                    description: "Grooving and cut-off insert solutions",
                    url: "/standard-tools/lathe-turning-inserts/grooving-cut-off-turning-inserts",
                  },
                  {
                    title: "Milling Inserts",
                    image: "/images/milling-insert.png",
                    description: "Milling inserts for turning centers",
                    url: "/standard-tools/lathe-turning-inserts/milling-inserts",
                  },
                ]
                
                return allTurningCategories.slice(0, 5).map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Lathe Turning Inserts" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
}