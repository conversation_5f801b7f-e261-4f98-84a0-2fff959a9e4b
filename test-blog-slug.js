// 测试特定博客 slug 是否存在
require('dotenv').config({ path: '.env.local' });
const { neon } = require('@neondatabase/serverless');

async function testBlogSlug() {
  try {
    console.log('🔍 检查博客 slug: wwwwwwwwwwwwwwwwwwwwwwwww');
    
    const sql = neon(process.env.DATABASE_URL);
    
    // 测试数据库连接
    console.log('📡 测试数据库连接...');
    const connectionTest = await sql`SELECT NOW() as current_time`;
    console.log('✅ 数据库连接成功:', connectionTest[0].current_time);
    
    // 检查博客表是否存在
    console.log('\n📋 检查博客表是否存在...');
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('blog_posts', 'blog_categories', 'blog_tags', 'blog_post_tags')
      ORDER BY table_name
    `;
    
    const existingTables = tables.map(t => t.table_name);
    console.log('📊 现有表:', existingTables);
    
    if (!existingTables.includes('blog_posts')) {
      console.log('❌ blog_posts 表不存在！');
      console.log('💡 请先运行博客数据库初始化：');
      console.log('   访问: http://localhost:3000/api/admin-mzg/init-blog-db');
      return;
    }
    
    // 查询所有博客文章
    console.log('\n📝 查询所有博客文章...');
    const allPosts = await sql`
      SELECT id, title, slug, published, created_at 
      FROM blog_posts 
      ORDER BY created_at DESC
    `;
    
    console.log(`📊 总共有 ${allPosts.length} 篇文章:`);
    allPosts.forEach((post, index) => {
      console.log(`  ${index + 1}. ${post.title} (slug: ${post.slug}, published: ${post.published})`);
    });
    
    // 查询特定的 slug
    console.log('\n🔍 查询特定 slug: wwwwwwwwwwwwwwwwwwwwwwwww');
    const specificPost = await sql`
      SELECT * FROM blog_posts
      WHERE slug = 'wwwwwwwwwwwwwwwwwwwwwwwww'
    `;
    
    if (specificPost.length > 0) {
      console.log('✅ 找到匹配的文章:');
      console.log(JSON.stringify(specificPost[0], null, 2));
    } else {
      console.log('❌ 没有找到 slug 为 "wwwwwwwwwwwwwwwwwwwwwwwww" 的文章');
      
      // 查找相似的 slug
      console.log('\n🔍 查找包含 "w" 的 slug...');
      const similarPosts = await sql`
        SELECT id, title, slug, published 
        FROM blog_posts 
        WHERE slug LIKE '%w%'
        ORDER BY LENGTH(slug) DESC
      `;
      
      if (similarPosts.length > 0) {
        console.log('📋 找到包含 "w" 的文章:');
        similarPosts.forEach(post => {
          console.log(`  - ${post.title} (slug: ${post.slug})`);
        });
      } else {
        console.log('❌ 没有找到包含 "w" 的文章');
      }
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    console.error('详细错误:', error.message);
  }
}

testBlogSlug();
