import { NextRequest, NextResponse } from 'next/server'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

export async function POST(request: NextRequest) {
  console.log('🚀 Simple Upload API called')
  
  try {
    console.log('📥 Parsing form data...')
    const formData = await request.formData()
    const file = formData.get('file') as File
    const type = formData.get('type') as string || 'general'
    
    console.log('📁 File info:', {
      name: file?.name,
      size: file?.size,
      type: file?.type
    })
    
    if (!file) {
      console.log('❌ No file uploaded')
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 })
    }

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      console.log('❌ Invalid file type:', file.type)
      return NextResponse.json({ error: 'File must be an image' }, { status: 400 })
    }

    // 验证文件大小 (5MB)
    if (file.size > 5 * 1024 * 1024) {
      console.log('❌ File too large:', file.size)
      return NextResponse.json({ error: 'File size must be less than 5MB' }, { status: 400 })
    }

    console.log('📦 Converting file to buffer...')
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // 创建文件名
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 15)
    const extension = file.name.split('.').pop()
    const fileName = `${type}_${timestamp}_${randomString}.${extension}`
    
    console.log('📝 Generated filename:', fileName)

    // 创建上传目录
    const uploadDir = join(process.cwd(), 'public', 'uploads', type)
    console.log('📁 Upload directory:', uploadDir)
    
    if (!existsSync(uploadDir)) {
      console.log('📁 Creating upload directory...')
      await mkdir(uploadDir, { recursive: true })
    }

    // 保存文件
    const filePath = join(uploadDir, fileName)
    console.log('💾 Saving file to:', filePath)
    await writeFile(filePath, buffer)
    console.log('✅ File saved successfully')

    // 返回文件URL
    const fileUrl = `/uploads/${type}/${fileName}`
    console.log('🔗 File URL:', fileUrl)
    
    const response = { 
      url: fileUrl,
      fileName,
      size: file.size,
      type: file.type
    }
    
    console.log('📤 Returning response:', response)
    return NextResponse.json(response)

  } catch (error) {
    console.error('❌ Upload error:', error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    console.error('❌ Error details:', errorMessage)
    
    return NextResponse.json(
      { error: `Failed to upload file: ${errorMessage}` },
      { status: 500 }
    )
  }
}
