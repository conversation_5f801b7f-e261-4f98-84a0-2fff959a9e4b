"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function ADSPullBackPage() {
  // ADS Pull-Back相关的默认图片
  const defaultADSImages = [
    "/images/C29-1.png",
    "/images/C30-1.png", 
    "/images/C31-1.png",
    "/images/C32-1.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/milling-tool-holder/ads-pull-back");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认ADS图片
          setGalleryImages(defaultADSImages);
        }
      } else {
        // API请求失败，使用默认ADS图片
        setGalleryImages(defaultADSImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认ADS图片
      setGalleryImages(defaultADSImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认ADS图片，避免显示无关图片
    setGalleryImages(defaultADSImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data for ADS Pull-Back Tool Holders
  const products = [
    {
      id: "ads-001",
      name: "BT-ADS Collect Chuck",
      image: "/images/C29-1.png",
      description: "Features convenient side locking without the need to remove pull studs. Ideal for high-speed precision machining with a 0.003mm runout accuracy. Can be paired with HSK dual-contact for enhanced lateral stability.",
      series: "BT-ADS",
      pageNumber: "C29",
    },
    {
      id: "ads-002",
      name: "EXTENSION ROD AFTER PULL",
      image: "/images/C30-1.png",
      description: "A side-locking pull-back tool holder suitable for high-speed precision machining.",
      series: "Extension Rod",
      pageNumber: "C30",
    },
    {
      id: "ads-003",
      name: "HSK-ADS Milling Machine Tool Holder",
      image: "/images/C31-1.png",
      description: "A side-locking pull-back tool holder designed for high-speed precision machining, featuring an HSK interface for enhanced stability.",
      series: "HSK-ADS",
      pageNumber: "C31",
    },
    {
      id: "ads-004",
      name: "SK-ADS Milling Machine Tool Holder",
      image: "/images/C32-1.png",
      description: "A side-locking pull-back tool holder suitable for high-speed precision machining.",
      series: "SK-ADS",
      pageNumber: "C32",
    },
  ]

  // Performance features based on the provided content
  const performanceFeatures = [
    {
      icon: "Target",
      title: "Exceptional Concentricity and Runout Accuracy",
      description: "Incredible runout accuracy consistently achieving ≤0.003mm through unique pull-back clamping mechanism that draws the collet backwards into precision-ground internal taper for perfect tool alignment.",
    },
    {
      icon: "Zap",
      title: "Elite High-Speed Capability",
      description: "Dynamically balanced to elite standard of G2.5 at up to 30,000 RPM, minimizing centrifugal forces and harmonic vibrations for remarkably stable cutting process and mirror-like surface finishes.",
    },
    {
      icon: "Shield",
      title: "Enhanced System Rigidity and Stability",
      description: "Pull-back action creates firm, positive lock on tool shank with enhanced rigidity. HSK-ADS models leverage dual-contact interface for simultaneous taper and face contact, dramatically increasing lateral stability.",
    },
  ]

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Clamping Principle & Precision",
      description: "High-precision pull-back collet system where collet is drawn into precision internal taper ensuring optimal seating and concentricity. Runout accuracy ≤0.003mm with critical operating procedure requiring tool shank insertion beyond effective clamping length.",
    },
    {
      title: "Dynamic Balance & Speed Performance",
      description: "G2.5 dynamic balance at maximum 30,000 RPM for elite high-speed capability. Available in BT-ADS (side-locking), HSK-ADS (dual-contact rigidity), and SK-ADS (standard steep taper) interfaces for broad machine compatibility.",
    },
    {
      title: "System Components & Extensions",
      description: "Complete ADS ecosystem includes dedicated Pull-Back Extension Rods designed with same precision principles. Side-locking mechanism in BT-ADS allows tool changes without removing pull stud, streamlining operations in busy workshop environments.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Pull-Back Tool Holder Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG ADS Pull-Back Tool Holder System
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  MZG present a comprehensive and detailed introduction to the Pull-Back Tool Holder, also known as the ADS series. This advanced tool holding system is specifically engineered for a niche yet critical segment of manufacturing: ultra-high-speed and high-precision machining. Its unique clamping principle sets it apart from conventional collet systems, delivering superior performance where accuracy and surface finish are paramount.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/millingcutter1.png"
                    alt="MZG Professional ADS Pull-Back Tool Holder System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Target":
                    return <Target className="h-6 w-6 text-blue-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  case "Shield":
                    return <Shield className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">System Performance Analysis</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of a Pull-Back Tool Holder is defined by its exceptional precision, high-speed stability, and the rigidity derived from its unique clamping design. The defining feature of the ADS system is its incredible runout accuracy, consistently achieving ≤0.003mm. This outstanding precision is a direct result of the "pull-back" clamping mechanism.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      Instead of a nut pushing the collet forward into a taper, the ADS design actively draws the collet backwards into the holder's precision-ground internal taper. This action ensures the collet is perfectly and evenly seated every time, eliminating the minor inconsistencies that can occur in standard systems and resulting in near-perfect tool alignment.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      To complement its precision, the ADS tool holder is dynamically balanced to an elite standard of G2.5 at up to 30,000 RPM. This high level of balance is essential for high-speed machining (HSM), as it minimizes centrifugal forces and harmonic vibrations, resulting in remarkably stable cutting process.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The pull-back action creates a firm, positive lock on the tool shank. The HSK-ADS models leverage the HSK dual-contact interface, providing simultaneous contact on both the taper and the face of the spindle, dramatically increasing rigidity against lateral forces for exceptional stability.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3 text-sm text-gray-700">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Runout Accuracy: ≤0.003mm</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Dynamic Balance: G2.5 grade</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Maximum Speed: 30,000 RPM</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Interface Types: BT-ADS, HSK-ADS, SK-ADS</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Special Feature: Side-locking mechanism</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                          <span className="text-gray-900 text-right">{product.series}</span>
                        </div>
                      )}
                      {product.description && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.description}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultADSImages[0]}
                    alt="ADS Pull-Back Tool Holder Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                  </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultADSImages[imageIndex % defaultADSImages.length]
                  : defaultADSImages[index % defaultADSImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`ADS Pull-Back Tool Holder Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                  </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Clamping Principle & Precision":
                      return <Target className="h-6 w-6 text-blue-600 mr-3" />
                    case "Dynamic Balance & Speed Performance":
                      return <Zap className="h-6 w-6 text-green-600 mr-3" />
                    case "System Components & Extensions":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                    </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                    </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios &amp; Applicable Machining</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Speed Finishing:</strong> Primary application ideal for creating flawless, mirror-like surfaces on molds, dies, and high-value components</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Precision Milling and Engraving:</strong> Perfect for machining intricate details, fine features, and complex 3D contours where dimensional and positional accuracy is critical</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Mold & Die Manufacturing:</strong> Essential for finishing hardened mold cavities and cores, manufacturing high-precision graphite or copper electrodes for EDM</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Aerospace and Medical Industries:</strong> Widely used for machining complex components from aluminum, titanium, and medical-grade plastics with tight tolerances</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Graphite Machining:</strong> High RPM capability and low runout ideal for machining brittle graphite electrodes, minimizing chipping and ensuring sharp features</span>
                    </li>
                </ul>
              </div>

              {/* Machining Operations */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Machining Operations
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Achieve Ultimate Rotational Precision:</strong> Provides near-perfect concentricity (≤0.003mm runout) through unique pull-back clamping mechanism</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Enable Ultra-High-Speed Machining:</strong> Designed and balanced to operate smoothly at speeds up to 30,000 RPM, unlocking modern high-speed spindle potential</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Secure and Stable Clamping:</strong> Pull-back design delivers rigid and reliable grip that minimizes tool deflection, crucial for maintaining accuracy under load</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Extend Precision Machining Reach:</strong> Compatible extension rods allow high-precision operations deep inside cavities and hard-to-reach areas</span>
                    </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Operational Convenience:</strong> Side-locking mechanism allows tool changes without removing pull stud, streamlining workshop operations</span>
                    </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Primary Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Achieve Ultimate Rotational Precision:</strong> Core function to provide near-perfect concentricity (≤0.003mm runout) through unique pull-back clamping mechanism</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Enable Ultra-High-Speed Machining:</strong> Designed and balanced to operate smoothly and stably at speeds up to 30,000 RPM</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Secure and Highly Stable Clamping Platform:</strong> Pull-back design delivers rigid and reliable grip that minimizes tool deflection</span>
                  </li>
                </ul>
                </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  Performance Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Extend Precision Machining Reach:</strong> System of compatible extension rods allows high-precision operations deep inside cavities without compromising accuracy</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Operational Convenience and Practicality:</strong> Side-locking mechanism eliminates need to remove pull stud, streamlining tool changing process</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Superior Surface Quality:</strong> Elite balance standard enables mirror-like surface finishes free of chatter marks for high-value components</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/milling-tool-holder/ads-pull-back" />
            </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional ADS Pull-Back Tool Holder Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal ADS pull-back tool holders for ultra-high-speed and high-precision machining applications. From ≤0.003mm runout accuracy to 30,000 RPM performance, we provide comprehensive precision tooling solutions.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same milling-tool-holder directory
                const allToolHolderCategories = [
                  {
                    title: "SK High-Speed Tool Holders",
                    image: "/images/C05-1.png",
                    description: "High-speed precision tool holders",
                    url: "/standard-tools/milling-tool-holder/sk-high-speed",
                  },
                  {
                    title: "HM Hydraulic Tool Holders",
                    image: "/images/C09-1.png",
                    description: "Precision hydraulic clamping systems",
                    url: "/standard-tools/milling-tool-holder/hm-hydraulic",
                  },
                  {
                    title: "SR Shrink Fit Tool Holders",
                    image: "/images/C13-1.png",
                    description: "Thermal expansion precision clamping",
                    url: "/standard-tools/milling-tool-holder/sr-shrink-fit",
                  },
                  {
                    title: "ER Tool Holders",
                    image: "/images/C01-1.png",
                    description: "Versatile collet chuck systems",
                    url: "/standard-tools/milling-tool-holder/er-tool-holder",
                  },
                  {
                    title: "Power Tool Holders",
                    image: "/images/c34-1.png",
                    description: "Heavy-duty strong clamping systems",
                    url: "/standard-tools/milling-tool-holder/power-tool-holder",
                  },
                ];
                
                return allToolHolderCategories.slice(0, 5).map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Tool Holders" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
} 