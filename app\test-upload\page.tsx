"use client"

import { useState } from "react"

export default function TestUploadPage() {
  const [result, setResult] = useState<string>("")
  const [uploading, setUploading] = useState(false)

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setUploading(true)
    setResult("正在上传...")

    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('type', 'blog-content')

      console.log('发送上传请求...')
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })

      console.log('响应状态:', response.status)
      
      if (response.ok) {
        const data = await response.json()
        console.log('上传成功:', data)
        setResult(`上传成功！URL: ${data.url}`)
      } else {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
        console.error('上传失败:', errorData)
        setResult(`上传失败: ${errorData.error}`)
      }
    } catch (error) {
      console.error('上传错误:', error)
      setResult(`上传错误: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setUploading(false)
    }
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">测试图片上传</h1>
      
      <div className="mb-4">
        <input
          type="file"
          accept="image/*"
          onChange={handleFileUpload}
          disabled={uploading}
          className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
        />
      </div>
      
      {result && (
        <div className={`p-4 rounded ${result.includes('成功') ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
          {result}
        </div>
      )}
    </div>
  )
}
