const { neon } = require('@neondatabase/serverless');
const sql = neon(process.env.DATABASE_URL);

async function listPosts() {
  try {
    console.log('Listing all blog posts...');
    
    const result = await sql`
      SELECT slug, title, content_images_count, published
      FROM blog_posts 
      ORDER BY created_at DESC
    `;
    
    console.log(`Found ${result.length} posts:`);
    result.forEach((post, index) => {
      console.log(`${index + 1}. ${post.title}`);
      console.log(`   Slug: ${post.slug}`);
      console.log(`   Images: ${post.content_images_count || 0}`);
      console.log(`   Published: ${post.published ? 'Yes' : 'No'}`);
      console.log('');
    });
    
  } catch (error) {
    console.error('Error:', error);
  }
}

listPosts();
