"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function PowerToolHolderPage() {
  // Power Tool Holder相关的默认图片
  const defaultPowerImages = [
    "/images/c34-1.png",
    "/images/c35-1.png", 
    "/images/c36-1.png",
    "/images/c37-1.png",
    "/images/c37-2.png",
    "/images/c38-1.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/milling-tool-holder/power-tool-holder");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认Power图片
          setGalleryImages(defaultPowerImages);
        }
      } else {
        // API请求失败，使用默认Power图片
        setGalleryImages(defaultPowerImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认Power图片
      setGalleryImages(defaultPowerImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认Power图片，避免显示无关图片
    setGalleryImages(defaultPowerImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data for Strong Tool Holders
  const products = [
    {
      id: "power-001",
      name: "DAT-C Milling Machine Tool Holder",
      image: "/images/c34-1.png",
      description: "Incorporates a slotted inner bore to prevent slippage and enhance clamping force, along with roller bearings to minimize friction and detachment. It is waterproof and dustproof with end face contact clamping, preventing overtightening.",
      series: "DAT-C Series",
      pageNumber: "C34",
    },
    {
      id: "power-002",
      name: "BT-MLT Milling Machine Tool Holder",
      image: "/images/c35-1.png",
      description: "Offers waterproof and dustproof capabilities with end face contact clamping. Its design prevents overtightening damage, and a sealed structure eliminates dirt and debris harm.",
      series: "BT-MLT Series",
      pageNumber: "C35",
    },
    {
      id: "power-003",
      name: "DAT-MLT Milling Machine Tool Holder",
      image: "/images/c36-1.png",
      description: "Provides waterproof and dustproof features with end face contact clamping. Its design prevents overtightening, and a sealed structure protects against contamination.",
      series: "DAT-MLT Series",
      pageNumber: "C36",
    },
    {
      id: "power-004",
      name: "HSK-MLT Milling Machine Tool Holder",
      image: "/images/c37-1.png",
      description: "Features waterproof and dustproof design with end face contact clamping and a sealed structure to prevent damage. Capable of dynamic balance up to G6.3, 8000RPM.",
      series: "HSK-MLT Series",
      pageNumber: "C37",
    },
    {
      id: "power-005",
      name: "NT-MLC Milling Machine Tool Holder",
      image: "/images/c37-2.png",
      description: "Utilizes a high-strength brass retainer to guide roller bearings for perfect sliding motion, and employs special hydraulic oil that prevents solidification.",
      series: "NT-MLC Series",
      pageNumber: "C37",
    },
    {
      id: "power-006",
      name: "NT-MCST Milling Machine Tool Holder",
      image: "/images/c38-1.png",
      description: "Designed to be waterproof and dustproof with end face contact clamping. Its sealed structure prevents damage from dirt and debris. Achieves dynamic balance up to G6.3, 8000RPM.",
      series: "NT-MCST Series",
      pageNumber: "C38",
    },
    {
      id: "power-007",
      name: "BT-MCST Milling Machine Tool Holder",
      image: "/images/c38-2.png",
      description: "Features a waterproof and dustproof design with end face contact clamping, and a sealed structure to eliminate dirt and debris damage. Capable of dynamic balance up to G6.3, 8000RPM.",
      series: "BT-MCST Series",
      pageNumber: "C38",
    },
    {
      id: "power-008",
      name: "DAT-MCST Milling Machine Tool Holder",
      image: "/images/c39-1.png",
      description: "Offers waterproof and dustproof properties with end face contact clamping and a sealed structure for protection. Achieves dynamic balance up to G6.3, 8000RPM.",
      series: "DAT-MCST Series",
      pageNumber: "C39",
    },
    {
      id: "power-009",
      name: "MCST Collets Chuck Kit",
      image: "/images/c40-1.png",
      description: "A comprehensive set of strong type collet chuck tool holders, featuring waterproof and dustproof design, balanced to G6.3, 8000RPM.",
      series: "MCST Kit Series",
      pageNumber: "C40",
    },
  ]

  // Performance features based on the provided content
  const performanceFeatures = [
    {
      icon: "Shield",
      title: "Massive Clamping Force and Torque Transmission",
      description: "Sophisticated mechanical clamping system with needle roller bearings generates significantly more clamping power than traditional ER collet chucks, providing exceptional resistance to tool pull-out and slippage.",
    },
    {
      icon: "Target",
      title: "Exceptional Rigidity and Vibration Damping",
      description: "Immense clamping force creates incredibly rigid connection that minimizes tool deflection and effectively dampens vibrations, enabling deeper cuts and higher material removal rates.",
    },
    {
      icon: "Zap",
      title: "High Precision for Demanding Tasks",
      description: "MLT series achieves outstanding concentricity of 0.001mm to 0.005mm for high-performance roughing and semi-finishing, while maintaining excellent precision despite focus on power.",
    },
  ]

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Clamping Mechanism & Precision",
      description: "Needle roller bearing system on tapered race actuated by clamping nut. MLT series achieves 0.001mm-0.005mm concentricity, while C series maintains ≤0.015mm concentricity. Six internal grooves enhance grip on Morse taper shanks.",
    },
    {
      title: "Dynamic Balance & Interface Types",
      description: "Standard balance grade G6.3 at 8,000 RPM for heavy milling, optional precision balance G2.5 at 25,000 RPM. Available in BT, DAT, HSK-A, NT interfaces with comprehensive collet systems (ST20, ST25, ST32, ST42).",
    },
    {
      title: "Robustness & System Protection",
      description: "Waterproof and dustproof sealed design prevents coolant and contaminant penetration. Features overtightening protection, high-strength brass retainer, and special non-solidifying hydraulic oil for enhanced performance.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Strong Tool Holder Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG Strong Tool Holder System
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  MZG provide a comprehensive and detailed introduction to the Strong Tool Holder, also known as a Milling Chuck. This category of tool holder is the undisputed workhorse for applications where maximum clamping force, rigidity, and high torque transmission are the primary requirements. It is engineered for heavy-duty cutting operations that would overwhelm standard collet systems.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/millingcutter1.png"
                    alt="MZG Professional Strong Tool Holder System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-green-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">System Performance Analysis</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of a Strong Tool Holder is defined by its immense power, stability, and reliability under the most demanding cutting conditions. The core lies in its sophisticated mechanical clamping system that utilizes a cage of needle roller bearings positioned between the inner surface of the chuck body and the outer surface of the clamping bore section.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      As the clamping nut is tightened, these bearings roll along an inclined race, efficiently converting rotational torque into a massive, uniform radial clamping force that grips the tool shank. This mechanism generates significantly more clamping power than a traditional ER collet chuck, providing exceptional resistance to tool pull-out and slippage.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The immense clamping force creates an incredibly rigid connection between the tool and the holder, minimizing tool deflection and effectively dampening vibrations at the source. By mitigating chatter, the strong tool holder enables deeper axial and radial depths of cut, leading to higher material removal rates and improved productivity.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      Despite their focus on power, modern strong tool holders offer excellent precision. The MLT series achieves outstanding concentricity of 0.001mm to 0.005mm, making it suitable for high-performance roughing and semi-finishing, while other models maintain reliable concentricity within 0.015mm.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3 text-sm text-gray-700">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>MLT Concentricity: 0.001-0.005mm</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>C Series Concentricity: ≤0.015mm</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Balance Grade: G6.3 (8,000 RPM)</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Optional Balance: G2.5 (25,000 RPM)</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Collet Range: 4-32mm diameter</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                          <span className="text-gray-900 text-right">{product.series}</span>
                        </div>
                      )}
                      {product.description && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.description}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultPowerImages[0]}
                    alt="Strong Tool Holder Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                  </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultPowerImages[imageIndex % defaultPowerImages.length]
                  : defaultPowerImages[index % defaultPowerImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Strong Tool Holder Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                  </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Clamping Mechanism & Precision":
                      return <Layers className="h-6 w-6 text-blue-600 mr-3" />
                    case "Dynamic Balance & Interface Types":
                      return <Settings className="h-6 w-6 text-green-600 mr-3" />
                    case "Robustness & System Protection":
                      return <Shield className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                    </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                    </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios &amp; Applicable Machining</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Heavy-Duty Roughing:</strong> Primary application excelling at milling large volumes of material from steel, cast iron, stainless steel, and high-temperature alloys</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Torque Milling:</strong> Ideal for large-diameter cutters such as face mills, shoulder mills, and high-feed mills where significant rotational force is required</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Heavy Drilling and Boring:</strong> C-type models specifically designed to securely grip flat-tail Morse taper drills, preventing slippage during high-torque operations</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Slotting and Pocketing:</strong> High rigidity and anti-pull-out security perfect for aggressive full-width slotting and deep pocket milling with extreme side loads</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>General Purpose Workhorse Machining:</strong> Single, highly reliable tool holder for job shops requiring versatile solution for demanding tasks from roughing to semi-finishing</span>
                    </li>
                </ul>
              </div>

              {/* Machining Operations */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Machining Operations
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Maximum Clamping Force:</strong> Generates and maintains immense grip on tool shank ensuring absolute security and preventing slippage during aggressive cuts</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Rock-Solid Rigidity:</strong> Creates connection that minimizes tool deflection and dampens vibration, enabling higher productivity and extending tool life</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High Torque Transmission:</strong> Efficiently transfers full power and torque of machine spindle to cutting edge without loss or slippage</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Long-Term Reliability:</strong> Robust, sealed designs protect internal mechanisms from harsh workshop environment, guaranteeing consistent performance</span>
                    </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Environmental Protection:</strong> Waterproof and dustproof sealed structure prevents coolant and contaminant penetration for extended service life</span>
                    </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Primary Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Deliver Maximum Clamping Force:</strong> Generate and maintain immense grip on tool shank, ensuring absolute security and preventing slippage during aggressive cuts</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Ensure Maximum Rigidity and Stability:</strong> Create rock-solid connection that minimizes tool deflection and dampens vibration for higher productivity</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Transmit High Levels of Torque:</strong> Efficiently transfer full power and torque of machine spindle to cutting edge without loss or slippage</span>
                  </li>
                </ul>
                </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  Performance Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Long-Term Reliability:</strong> Robust, sealed designs protect internal mechanisms from harsh workshop environment, guaranteeing consistent performance</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Protect Tooling Investment:</strong> Waterproof and dustproof sealed structure prevents contamination damage and ensures extended service life</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Versatile System Solution:</strong> Comprehensive range of interfaces and collet systems for wide spectrum of demanding machining applications</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/milling-tool-holder/power-tool-holder" />
            </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional Strong Tool Holder Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal strong tool holders for heavy-duty cutting operations, high-torque milling, and demanding machining applications. From maximum clamping force to comprehensive system protection, we provide complete strong tool holder solutions.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same milling-tool-holder directory
                const allToolHolderCategories = [
                  {
                    title: "SR Shrink Fit Tool Holders",
                    image: "/images/C13-1.png",
                    description: "Thermal expansion precision clamping",
                    url: "/standard-tools/milling-tool-holder/sr-shrink-fit",
                  },
                  {
                    title: "HM Hydraulic Tool Holders",
                    image: "/images/C09-1.png",
                    description: "Precision hydraulic clamping systems",
                    url: "/standard-tools/milling-tool-holder/hm-hydraulic",
                  },
                  {
                    title: "SK High-Speed Tool Holders",
                    image: "/images/C05-1.png",
                    description: "High-speed precision tool holders",
                    url: "/standard-tools/milling-tool-holder/sk-high-speed",
                  },
                  {
                    title: "ER Tool Holders",
                    image: "/images/C01-1.png",
                    description: "Versatile collet chuck systems",
                    url: "/standard-tools/milling-tool-holder/er-tool-holder",
                  },
                  {
                    title: "Side Lock Tool Holders",
                    image: "/images/C04-1.png",
                    description: "Side clamping mechanism",
                    url: "/standard-tools/milling-tool-holder/side-lock",
                  },
                ];
                
                return allToolHolderCategories.slice(0, 5).map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Tool Holders" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
}