import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { formatDate } from "@/lib/utils"
import { getBlogPost, type ContentImage } from "@/app/actions/blog-actions-db-final"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Share2, Calendar, User, Tag } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import type { Metadata } from "next"

interface BlogPostPageProps {
  params: Promise<{
    slug: string
  }>
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const resolvedParams = await params
  const post = await getBlogPost(resolvedParams.slug)
  
  if (!post) {
    return {
      title: "Post Not Found",
    }
  }

  return {
    title: post.title,
    description: post.excerpt || post.content.substring(0, 160),
    openGraph: {
      title: post.title,
      description: post.excerpt || post.content.substring(0, 160),
      type: "article",
      publishedTime: post.created_at,
      authors: ["MZG Tools"],
    },
  }
}

// Enhanced content processing function with database images and smart insertion
function enhanceContentWithImages(content: string, contentImages?: ContentImage[]): string {
  // First, process any existing Markdown-style image syntax
  let processedContent = content

  // Convert Markdown image syntax: ![alt](url "title")
  processedContent = processedContent.replace(
    /!\[([^\]]*)\]\(([^)]+)(?:\s+"([^"]*)")?\)/g,
    '<img src="$2" alt="$1" title="$3" align="center" loading="lazy" />'
  )

  // Convert custom alignment syntax: ![alt](url){align:left}
  processedContent = processedContent.replace(
    /!\[([^\]]*)\]\(([^)]+)\)\{align:(left|right|center)\}/g,
    '<img src="$2" alt="$1" align="$3" loading="lazy" />'
  )

  // Use database images if available, otherwise fallback to sample images
  const imagesToUse = contentImages && Array.isArray(contentImages) && contentImages.length > 0
    ? contentImages
    : [
        {
          id: 'sample1',
          url: '/gun-drill-anatomy.png',
          alt: 'Industrial milling tool showcase',
          position: 'left' as const
        },
        {
          id: 'sample2',
          url: '/custom-gun-drill-manufacturing.png',
          alt: 'CNC machining process',
          position: 'center' as const
        },
        {
          id: 'sample3',
          url: '/counterbore-tool.png',
          alt: 'Precision manufacturing equipment',
          position: 'right' as const
        }
      ]

  // Normalize content - handle different paragraph formats
  // Split by double newlines first, then by periods followed by space and capital letter
  let paragraphs = processedContent.split('\n\n').filter(p => p.trim().length > 0)
  
  // If we don't have enough paragraphs, try splitting by sentences
  if (paragraphs.length < 3) {
    // Split by periods followed by space and capital letter (sentence boundaries)
    const sentences = processedContent.split(/\.\s+(?=[A-Z])/).filter(s => s.trim().length > 20)
    
    // Group sentences into paragraphs (2-3 sentences per paragraph)
    paragraphs = []
    for (let i = 0; i < sentences.length; i += 2) {
      const paragraph = sentences.slice(i, i + 2).join('. ')
      if (paragraph.trim().length > 0) {
        paragraphs.push(paragraph + (paragraph.endsWith('.') ? '' : '.'))
      }
    }
  }

  // Smart image insertion strategy
  if (paragraphs.length > 1 && imagesToUse.length > 0) {
    let imageIndex = 0

    // Insert first image after opening paragraph (if we have at least 2 paragraphs)
    if (paragraphs.length > 1 && imageIndex < imagesToUse.length) {
      const image = imagesToUse[imageIndex]
      const alignment = image.position || 'left'
      paragraphs.splice(1, 0,
        `<img src="${image.url}" alt="${image.alt}" align="${alignment}" loading="lazy" />`
      )
      imageIndex++
    }

    // Insert images every 2-3 paragraphs for natural flow
    let insertPosition = 3
    while (insertPosition < paragraphs.length && imageIndex < imagesToUse.length) {
      const image = imagesToUse[imageIndex]
      const alignment = image.position || (['left', 'right', 'center'][imageIndex % 3] as 'left' | 'right' | 'center')

      paragraphs.splice(insertPosition, 0,
        `<img src="${image.url}" alt="${image.alt}" align="${alignment}" loading="lazy" />`
      )
      imageIndex++
      
      // Move to next insertion point (skip 2-3 paragraphs)
      insertPosition += 3
    }
  }

  return paragraphs.join('\n\n')
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const resolvedParams = await params
  const post = await getBlogPost(resolvedParams.slug)

  if (!post) {
    notFound()
  }

  // Process content with images
  const enhancedContent = enhanceContentWithImages(post.content, post.content_images)

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Back Button */}
          <div className="mb-8">
            <Link href="/mzgblog">
              <Button variant="ghost" className="text-gray-600 hover:text-gray-900">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Blog
              </Button>
            </Link>
          </div>

          {/* Article Header */}
          <header className="mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4 leading-tight">
              {post.title}
            </h1>
            
            {/* Meta Information */}
            <div className="flex flex-wrap items-center gap-6 text-gray-600 mb-6">
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <time dateTime={post.created_at}>
                  {formatDate(post.created_at)}
                </time>
              </div>
              
              <div className="flex items-center gap-2">
                <User className="w-4 h-4" />
                <span>MZG Tools</span>
              </div>
              
              {post.category && (
                <div className="flex items-center gap-2">
                  <Tag className="w-4 h-4" />
                  <Link 
                    href={`/mzgblog?category=${post.category.slug}`}
                    className="hover:text-blue-600 transition-colors"
                  >
                    {post.category.name}
                  </Link>
                </div>
              )}
            </div>

            {/* Tags */}
            {post.tags && Array.isArray(post.tags) && post.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-8">
                {post.tags.map((tag) => (
                  <Link
                    key={tag.id}
                    href={`/mzgblog?tag=${tag.slug}`}
                    className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-3 py-1 rounded-full text-sm transition-colors"
                  >
                    #{tag.name}
                  </Link>
                ))}
              </div>
            )}

            {/* Share Button */}
            <div className="flex items-center gap-4">
              <Button variant="outline" size="sm">
                <Share2 className="w-4 h-4 mr-2" />
                Share
              </Button>
            </div>
          </header>

          {/* Article Content */}
          <article className="prose prose-lg max-w-none">
            <div
              className="blog-content
                [&_img]:max-w-full [&_img]:h-auto [&_img]:rounded-lg [&_img]:shadow-lg [&_img]:transition-all [&_img]:duration-300 [&_img]:hover:shadow-xl [&_img]:hover:scale-[1.02]
                [&_img[align='left']]:float-left [&_img[align='left']]:mr-6 [&_img[align='left']]:mb-4 [&_img[align='left']]:mt-2 [&_img[align='left']]:max-w-[240px] [&_img[align='left']]:w-[240px] [&_img[align='left']]:h-[135px] [&_img[align='left']]:object-cover
                [&_img[align='right']]:float-right [&_img[align='right']]:ml-6 [&_img[align='right']]:mb-4 [&_img[align='right']]:mt-2 [&_img[align='right']]:max-w-[240px] [&_img[align='right']]:w-[240px] [&_img[align='right']]:h-[135px] [&_img[align='right']]:object-cover
                [&_img[align='center']]:block [&_img[align='center']]:mx-auto [&_img[align='center']]:my-8 [&_img[align='center']]:max-w-[480px] [&_img[align='center']]:w-[480px] [&_img[align='center']]:h-[270px] [&_img[align='center']]:object-cover
                max-md:[&_img[align='left']]:float-none max-md:[&_img[align='left']]:block max-md:[&_img[align='left']]:mx-auto max-md:[&_img[align='left']]:max-w-[280px] max-md:[&_img[align='left']]:w-[280px] max-md:[&_img[align='left']]:h-[158px] max-md:[&_img[align='left']]:mb-6
                max-md:[&_img[align='right']]:float-none max-md:[&_img[align='right']]:block max-md:[&_img[align='right']]:mx-auto max-md:[&_img[align='right']]:max-w-[280px] max-md:[&_img[align='right']]:w-[280px] max-md:[&_img[align='right']]:h-[158px] max-md:[&_img[align='right']]:mb-6
                max-md:[&_img[align='center']]:max-w-[320px] max-md:[&_img[align='center']]:w-[320px] max-md:[&_img[align='center']]:h-[180px]
                [&_p]:mb-6 [&_p]:leading-[1.8] [&_p]:text-justify [&_p]:hyphens-auto
                [&_p:has(+img[align='left'])]:mb-2 [&_p:has(+img[align='right'])]:mb-2
                after:content-[''] after:table after:clear-both"
              dangerouslySetInnerHTML={{ __html: enhancedContent }}
            />
          </article>
        </div>
      </main>

      <Footer />
    </div>
  )
}
