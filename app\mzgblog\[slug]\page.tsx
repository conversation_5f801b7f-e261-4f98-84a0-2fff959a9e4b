import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { formatDate } from "@/lib/utils"
import { getBlogPost } from "@/app/actions/blog-actions-db-final"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Share2, Calendar, User, Tag, ChevronRight } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import type { Metadata } from "next"

interface BlogPostPageProps {
  params: Promise<{
    slug: string
  }>
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const resolvedParams = await params
  const post = await getBlogPost(resolvedParams.slug)

  if (!post) {
    return {
      title: "Post Not Found | MZG Tools",
    }
  }

  return {
    title: `${post.title} | MZG Tools Blog`,
    description: post.excerpt,
  }
}

// Safe image URL function to handle invalid URLs
function getSafeImageUrl(imageUrl?: string): string {
  const defaultImage = "/placeholder.svg?height=800&width=1200&query=industrial tool"

  if (!imageUrl) {
    return defaultImage
  }

  // Check if it's a relative path starting with /
  if (imageUrl.startsWith('/')) {
    return imageUrl
  }

  // For absolute URLs, try to validate them
  try {
    new URL(imageUrl)
    return imageUrl
  } catch {
    return defaultImage
  }
}

// Enhanced content processing function to add inline images
function enhanceContentWithImages(content: string): string {
  // Add sample images to demonstrate the layout capabilities
  const sampleImages = [
    '/placeholder.svg?height=300&width=400&query=milling tool',
    '/placeholder.svg?height=250&width=350&query=cnc machine',
    '/placeholder.svg?height=280&width=380&query=industrial equipment'
  ]

  // Split content into paragraphs
  const paragraphs = content.split('\n\n')

  if (paragraphs.length > 2) {
    // Insert a left-aligned image after the first paragraph
    paragraphs.splice(1, 0, `<img src="${sampleImages[0]}" alt="Industrial milling tool" align="left" />`)

    // Insert a center-aligned image in the middle
    if (paragraphs.length > 4) {
      const middleIndex = Math.floor(paragraphs.length / 2)
      paragraphs.splice(middleIndex, 0, `<img src="${sampleImages[1]}" alt="CNC machining process" align="center" />`)
    }

    // Insert a right-aligned image near the end
    if (paragraphs.length > 6) {
      paragraphs.splice(-2, 0, `<img src="${sampleImages[2]}" alt="Precision manufacturing equipment" align="right" />`)
    }
  }

  return paragraphs.join('\n\n')
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const resolvedParams = await params
  const post = await getBlogPost(resolvedParams.slug)

  if (!post) {
    notFound()
  }

  return (
    <>
      <style jsx>{`
        .article-content img {
          max-width: 100%;
          height: auto;
          border-radius: 8px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
          margin: 1.5rem 0;
        }

        .article-content img[align="left"] {
          float: left;
          margin: 0 1.5rem 1rem 0;
          max-width: 300px;
        }

        .article-content img[align="right"] {
          float: right;
          margin: 0 0 1rem 1.5rem;
          max-width: 300px;
        }

        .article-content img[align="center"] {
          display: block;
          margin: 2rem auto;
          max-width: 600px;
        }

        @media (max-width: 768px) {
          .article-content img[align="left"],
          .article-content img[align="right"] {
            float: none;
            display: block;
            margin: 1.5rem auto;
            max-width: 100%;
          }
        }

        .article-content p {
          margin-bottom: 1.25rem;
          line-height: 1.7;
        }

        .article-content h2 {
          margin-top: 2.5rem;
          margin-bottom: 1rem;
          font-size: 1.5rem;
          font-weight: 600;
          color: #1f2937;
        }

        .article-content h3 {
          margin-top: 2rem;
          margin-bottom: 0.75rem;
          font-size: 1.25rem;
          font-weight: 600;
          color: #374151;
        }

        .article-content blockquote {
          border-left: 4px solid #dc2626;
          padding-left: 1rem;
          margin: 1.5rem 0;
          font-style: italic;
          color: #6b7280;
        }

        .article-content ul, .article-content ol {
          margin: 1rem 0;
          padding-left: 1.5rem;
        }

        .article-content li {
          margin-bottom: 0.5rem;
        }
      `}</style>
      <Header />
      <div className="bg-white">
        {/* Hero Section - Enhanced with better proportions */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="container mx-auto px-4 py-8 md:py-16 lg:py-20">
            <div className="grid lg:grid-cols-2 gap-8 lg:gap-16 items-center min-h-[400px] lg:min-h-[500px]">
              <div className="max-w-4xl order-2 lg:order-1">
                <div className="inline-block bg-red-600 text-white px-4 py-2 rounded-full text-sm font-medium mb-6">
                  {post.category?.name || 'Uncategorized'}
                </div>
                <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-6 leading-tight text-gray-900">
                  {post.title}
                </h1>
                <p className="text-base md:text-lg mb-8 text-gray-600 leading-relaxed max-w-2xl">
                  {post.excerpt || 'Discover expert insights and technical knowledge in our comprehensive blog post.'}
                </p>
                <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                  <div className="flex items-center gap-2">
                    <Calendar size={16} />
                    <span>{formatDate(post.createdAt)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <User size={16} />
                    <span>{post.author}</span>
                  </div>
                  {post.viewCount > 0 && (
                    <div className="flex items-center gap-2">
                      <span>👁</span>
                      <span>{post.viewCount} views</span>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex justify-center lg:justify-end order-1 lg:order-2">
                <div className="relative w-full max-w-md lg:max-w-lg">
                  <div className="aspect-square bg-gray-50 rounded-xl overflow-hidden shadow-lg">
                    <Image
                      src={getSafeImageUrl(post.imageUrl)}
                      alt={post.featuredImageAlt || post.title}
                      width={500}
                      height={500}
                      className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                      priority
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Breadcrumb */}
        <div className="container mx-auto px-4 md:px-8 py-4">
          <nav className="flex items-center space-x-2 text-sm text-gray-600">
            <Link href="/" className="hover:text-red-600 transition-colors">Home</Link>
            <ChevronRight className="h-4 w-4" />
            <Link href="/mzgblog" className="hover:text-red-600 transition-colors">Blog</Link>
            <ChevronRight className="h-4 w-4" />
            <span className="text-gray-900">{post.title}</span>
          </nav>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 md:px-8 py-8">
          <div className="grid lg:grid-cols-4 gap-8">
            {/* Main Article */}
            <div className="lg:col-span-3">
              {/* Meta Information */}
              <div className="flex flex-wrap items-center gap-4 py-6 border-b border-gray-200 text-gray-600">
                <div className="flex items-center gap-2">
                  <Calendar size={16} />
                  <span>{formatDate(post.createdAt)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <User size={16} />
                  <span>{post.author}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Tag size={16} />
                  <span>{post.category?.name || 'Uncategorized'}</span>
                </div>
                <div className="ml-auto">
                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                    <Share2 size={16} />
                    Share
                  </Button>
                </div>
              </div>

              {/* Article Content */}
              <article className="prose prose-lg max-w-none py-8 text-[14px] leading-relaxed">
                <div
                  className="article-content"
                  dangerouslySetInnerHTML={{ __html: enhanceContentWithImages(post.content) }}
                />
              </article>

              {/* Tags */}
              {post.tags && post.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-8">
                  {post.tags.map((tag) => (
                    <Link
                      key={tag.id}
                      href={`/mzgblog?tag=${tag.slug}`}
                      className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-3 py-1 rounded-full text-sm transition-colors"
                    >
                      #{tag.name}
                    </Link>
                  ))}
                </div>
              )}

              {/* Navigation */}
              <div className="mt-12 border-t border-gray-200 pt-8">
                <Link href="/mzgblog">
                  <Button variant="outline" className="flex items-center gap-2">
                    <ArrowLeft size={16} />
                    Back to Blog
                  </Button>
                </Link>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-8">
                {/* Recent Posts */}
                <div className="bg-gray-50 rounded-lg p-6 mb-6">
                  <h3 className="text-lg font-bold mb-4">Recent Posts</h3>
                  <div className="space-y-3">
                    <Link href="/mzgblog" className="block text-sm text-gray-600 hover:text-red-600 transition-colors">
                      View all blog posts
                    </Link>
                  </div>
                </div>

                {/* Categories */}
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-bold mb-4">Categories</h3>
                  <div className="space-y-2">
                    <Link href="/mzgblog" className="block text-sm text-gray-600 hover:text-red-600 transition-colors">
                      All Categories
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Related Articles */}
        <div className="bg-gray-50 py-16">
          <div className="container mx-auto px-4 md:px-8">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Articles</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <h3 className="text-lg font-bold mb-2">Explore More Content</h3>
                <p className="text-gray-600 text-sm mb-4">
                  Discover more insights and technical articles in our blog.
                </p>
                <Link href="/mzgblog">
                  <Button variant="outline" className="text-sm">
                    View All Posts
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
}
