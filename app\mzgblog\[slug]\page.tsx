import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { formatDate } from "@/lib/utils"
import { getBlogPost } from "@/app/actions/blog-actions-db-final"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Share2, Calendar, User, Tag, ChevronRight } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import type { Metadata } from "next"

interface BlogPostPageProps {
  params: Promise<{
    slug: string
  }>
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const resolvedParams = await params
  const post = await getBlogPost(resolvedParams.slug)

  if (!post) {
    return {
      title: "Post Not Found | MZG Tools",
    }
  }

  return {
    title: `${post.title} | MZG Tools Blog`,
    description: post.excerpt,
  }
}

// Safe image URL function to handle invalid URLs
function getSafeImageUrl(imageUrl?: string): string {
  const defaultImage = "/placeholder.svg?height=800&width=1200&query=industrial tool"
  
  if (!imageUrl) {
    return defaultImage
  }
  
  // Check if it's a relative path starting with /
  if (imageUrl.startsWith('/')) {
    return imageUrl
  }
  
  // For absolute URLs, try to validate them
  try {
    new URL(imageUrl)
    return imageUrl
  } catch {
    return defaultImage
  }
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const resolvedParams = await params
  const post = await getBlogPost(resolvedParams.slug)

  if (!post) {
    notFound()
  }

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section - White background style */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  {post.category?.name || 'Uncategorized'}
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  {post.title}
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  {post.excerpt || 'Discover expert insights and technical knowledge in our comprehensive blog post.'}
                </p>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[400px] h-[300px] flex items-center justify-center">
                  <Image
                    src={getSafeImageUrl(post.imageUrl)}
                    alt={post.title}
                    width={400}
                    height={300}
                    className="object-contain rounded-lg"
                    priority
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Breadcrumb */}
        <div className="container mx-auto px-4 md:px-8 py-4">
          <nav className="flex items-center space-x-2 text-sm text-gray-600">
            <Link href="/" className="hover:text-red-600 transition-colors">Home</Link>
            <ChevronRight className="h-4 w-4" />
            <Link href="/mzgblog" className="hover:text-red-600 transition-colors">Blog</Link>
            <ChevronRight className="h-4 w-4" />
            <span className="text-gray-900">{post.title}</span>
          </nav>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 md:px-8 py-8">
          <div className="grid lg:grid-cols-4 gap-8">
            {/* Main Article */}
            <div className="lg:col-span-3">
              {/* Meta Information */}
              <div className="flex flex-wrap items-center gap-4 py-6 border-b border-gray-200 text-gray-600">
                <div className="flex items-center gap-2">
                  <Calendar size={16} />
                  <span>{formatDate(post.createdAt)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <User size={16} />
                  <span>{post.author}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Tag size={16} />
                  <span>{post.category?.name || 'Uncategorized'}</span>
                </div>
                <div className="ml-auto">
                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                    <Share2 size={16} />
                    Share
                  </Button>
                </div>
              </div>

              {/* Article Content */}
              <article className="prose max-w-none py-8 text-[12px]">
                <div dangerouslySetInnerHTML={{ __html: post.content }} />
              </article>

              {/* Tags */}
              {post.tags && post.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-8">
                  {post.tags.map((tag) => (
                    <Link
                      key={tag.id}
                      href={`/mzgblog?tag=${tag.slug}`}
                      className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-3 py-1 rounded-full text-sm transition-colors"
                    >
                      #{tag.name}
                    </Link>
                  ))}
                </div>
              )}

              {/* Navigation */}
              <div className="mt-12 border-t border-gray-200 pt-8">
                <Link href="/mzgblog">
                  <Button variant="outline" className="flex items-center gap-2">
                    <ArrowLeft size={16} />
                    Back to Blog
                  </Button>
                </Link>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-8">
                {/* Recent Posts */}
                <div className="bg-gray-50 rounded-lg p-6 mb-6">
                  <h3 className="text-lg font-bold mb-4">Recent Posts</h3>
                  <div className="space-y-3">
                    <Link href="/mzgblog" className="block text-sm text-gray-600 hover:text-red-600 transition-colors">
                      View all blog posts
                    </Link>
                  </div>
                </div>

                {/* Categories */}
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-bold mb-4">Categories</h3>
                  <div className="space-y-2">
                    <Link href="/mzgblog" className="block text-sm text-gray-600 hover:text-red-600 transition-colors">
                      All Categories
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Related Articles */}
        <div className="bg-gray-50 py-16">
          <div className="container mx-auto px-4 md:px-8">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Articles</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <h3 className="text-lg font-bold mb-2">Explore More Content</h3>
                <p className="text-gray-600 text-sm mb-4">
                  Discover more insights and technical articles in our blog.
                </p>
                <Link href="/mzgblog">
                  <Button variant="outline" className="text-sm">
                    View All Posts
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
}
