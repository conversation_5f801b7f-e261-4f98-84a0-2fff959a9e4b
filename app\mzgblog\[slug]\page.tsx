import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { formatDate } from "@/lib/utils"
import { getBlogPost } from "@/app/actions/blog-actions-db-final"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Share2, Calendar, User, Tag, ChevronRight } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import type { Metadata } from "next"

interface BlogPostPageProps {
  params: Promise<{
    slug: string
  }>
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const resolvedParams = await params
  const post = await getBlogPost(resolvedParams.slug)

  if (!post) {
    return {
      title: "Post Not Found | MZG Tools",
    }
  }

  return {
    title: `${post.title} | MZG Tools Blog`,
    description: post.excerpt,
  }
}

// Safe image URL function to handle invalid URLs
function getSafeImageUrl(imageUrl?: string): string {
  const defaultImage = "/placeholder.svg?height=800&width=1200&query=industrial tool"

  if (!imageUrl) {
    return defaultImage
  }

  // Check if it's a relative path starting with /
  if (imageUrl.startsWith('/')) {
    return imageUrl
  }

  // For absolute URLs, try to validate them
  try {
    new URL(imageUrl)
    return imageUrl
  } catch {
    return defaultImage
  }
}

// Enhanced content processing function to add inline images
function enhanceContentWithImages(content: string): string {
  // Add sample images with consistent 4:3 aspect ratio for all blog pages
  const sampleImages = [
    '/placeholder.svg?height=120&width=160&query=milling tool',
    '/placeholder.svg?height=240&width=320&query=cnc machine',
    '/placeholder.svg?height=120&width=160&query=industrial equipment'
  ]

  // Split content into paragraphs
  const paragraphs = content.split('\n\n')

  if (paragraphs.length > 2) {
    // Insert a left-aligned image after the first paragraph
    paragraphs.splice(1, 0, `<img src="${sampleImages[0]}" alt="Industrial milling tool" align="left" />`)

    // Insert a center-aligned image in the middle
    if (paragraphs.length > 4) {
      const middleIndex = Math.floor(paragraphs.length / 2)
      paragraphs.splice(middleIndex, 0, `<img src="${sampleImages[1]}" alt="CNC machining process" align="center" />`)
    }

    // Insert a right-aligned image near the end
    if (paragraphs.length > 6) {
      paragraphs.splice(-2, 0, `<img src="${sampleImages[2]}" alt="Precision manufacturing equipment" align="right" />`)
    }
  }

  return paragraphs.join('\n\n')
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const resolvedParams = await params
  const post = await getBlogPost(resolvedParams.slug)

  if (!post) {
    notFound()
  }

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section - Further reduced height to 50% */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="container mx-auto px-4 py-2 md:py-4 lg:py-5">
            <div className="grid lg:grid-cols-2 gap-4 lg:gap-8 items-center min-h-[100px] lg:min-h-[125px]">
              <div className="max-w-4xl order-2 lg:order-1">
                <div className="inline-block bg-red-600 text-white px-2 py-0.5 rounded-full text-xs font-medium mb-2">
                  {post.category?.name || 'Uncategorized'}
                </div>
                <h1 className="text-lg md:text-xl lg:text-2xl font-bold mb-2 leading-tight text-gray-900">
                  {post.title}
                </h1>
                <p className="text-xs md:text-sm mb-2 text-gray-600 leading-relaxed max-w-2xl">
                  {post.excerpt || 'Discover expert insights and technical knowledge in our comprehensive blog post.'}
                </p>
                <div className="flex flex-wrap items-center gap-2 text-xs text-gray-500">
                  <div className="flex items-center gap-1">
                    <Calendar size={12} />
                    <span>{formatDate(post.createdAt)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <User size={12} />
                    <span>{post.author}</span>
                  </div>
                  {post.viewCount > 0 && (
                    <div className="flex items-center gap-1">
                      <span>👁</span>
                      <span>{post.viewCount} views</span>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex justify-center lg:justify-end order-1 lg:order-2">
                <div className="relative w-full max-w-[200px] lg:max-w-[240px]">
                  <div className="aspect-[4/3] bg-gray-50 rounded-lg overflow-hidden shadow-md">
                    <Image
                      src={getSafeImageUrl(post.imageUrl)}
                      alt={post.featuredImageAlt || post.title}
                      width={240}
                      height={180}
                      className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                      priority
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Breadcrumb */}
        <div className="container mx-auto px-4 md:px-8 py-4">
          <nav className="flex items-center space-x-2 text-sm text-gray-600">
            <Link href="/" className="hover:text-red-600 transition-colors">Home</Link>
            <ChevronRight className="h-4 w-4" />
            <Link href="/mzgblog" className="hover:text-red-600 transition-colors">Blog</Link>
            <ChevronRight className="h-4 w-4" />
            <span className="text-gray-900">{post.title}</span>
          </nav>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 md:px-8 py-8">
          <div className="grid lg:grid-cols-4 gap-8">
            {/* Main Article */}
            <div className="lg:col-span-3">
              {/* Meta Information */}
              <div className="flex flex-wrap items-center gap-4 py-6 border-b border-gray-200 text-gray-600">
                <div className="flex items-center gap-2">
                  <Calendar size={16} />
                  <span>{formatDate(post.createdAt)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <User size={16} />
                  <span>{post.author}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Tag size={16} />
                  <span>{post.category?.name || 'Uncategorized'}</span>
                </div>
                <div className="ml-auto">
                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                    <Share2 size={16} />
                    Share
                  </Button>
                </div>
              </div>

              {/* Article Content */}
              <article className="prose prose-lg max-w-none py-8 text-[14px] leading-relaxed">
                <div
                  className="article-content
                    [&_img]:max-w-full [&_img]:h-auto [&_img]:rounded-lg [&_img]:shadow-md [&_img]:my-4
                    [&_img[align='left']]:float-left [&_img[align='left']]:mr-4 [&_img[align='left']]:mb-3 [&_img[align='left']]:max-w-[160px] [&_img[align='left']]:w-[160px] [&_img[align='left']]:h-[120px] [&_img[align='left']]:object-cover
                    [&_img[align='right']]:float-right [&_img[align='right']]:ml-4 [&_img[align='right']]:mb-3 [&_img[align='right']]:max-w-[160px] [&_img[align='right']]:w-[160px] [&_img[align='right']]:h-[120px] [&_img[align='right']]:object-cover
                    [&_img[align='center']]:block [&_img[align='center']]:mx-auto [&_img[align='center']]:my-6 [&_img[align='center']]:max-w-[320px] [&_img[align='center']]:w-[320px] [&_img[align='center']]:h-[240px] [&_img[align='center']]:object-cover
                    max-md:[&_img[align='left']]:float-none max-md:[&_img[align='left']]:block max-md:[&_img[align='left']]:mx-auto max-md:[&_img[align='left']]:max-w-[200px] max-md:[&_img[align='left']]:w-[200px] max-md:[&_img[align='left']]:h-[150px]
                    max-md:[&_img[align='right']]:float-none max-md:[&_img[align='right']]:block max-md:[&_img[align='right']]:mx-auto max-md:[&_img[align='right']]:max-w-[200px] max-md:[&_img[align='right']]:w-[200px] max-md:[&_img[align='right']]:h-[150px]
                    max-md:[&_img[align='center']]:max-w-[240px] max-md:[&_img[align='center']]:w-[240px] max-md:[&_img[align='center']]:h-[180px]
                    [&_p]:mb-5 [&_p]:leading-7
                    [&_h2]:mt-10 [&_h2]:mb-4 [&_h2]:text-2xl [&_h2]:font-semibold [&_h2]:text-gray-800
                    [&_h3]:mt-8 [&_h3]:mb-3 [&_h3]:text-xl [&_h3]:font-semibold [&_h3]:text-gray-700
                    [&_blockquote]:border-l-4 [&_blockquote]:border-red-600 [&_blockquote]:pl-4 [&_blockquote]:my-6 [&_blockquote]:italic [&_blockquote]:text-gray-600
                    [&_ul]:my-4 [&_ul]:pl-6 [&_ol]:my-4 [&_ol]:pl-6
                    [&_li]:mb-2"
                  dangerouslySetInnerHTML={{ __html: enhanceContentWithImages(post.content) }}
                />
              </article>

              {/* Tags */}
              {post.tags && post.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-8">
                  {post.tags.map((tag) => (
                    <Link
                      key={tag.id}
                      href={`/mzgblog?tag=${tag.slug}`}
                      className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-3 py-1 rounded-full text-sm transition-colors"
                    >
                      #{tag.name}
                    </Link>
                  ))}
                </div>
              )}

              {/* Navigation */}
              <div className="mt-12 border-t border-gray-200 pt-8">
                <Link href="/mzgblog">
                  <Button variant="outline" className="flex items-center gap-2">
                    <ArrowLeft size={16} />
                    Back to Blog
                  </Button>
                </Link>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-8">
                {/* Recent Posts */}
                <div className="bg-gray-50 rounded-lg p-6 mb-6">
                  <h3 className="text-lg font-bold mb-4">Recent Posts</h3>
                  <div className="space-y-3">
                    <Link href="/mzgblog" className="block text-sm text-gray-600 hover:text-red-600 transition-colors">
                      View all blog posts
                    </Link>
                  </div>
                </div>

                {/* Categories */}
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-bold mb-4">Categories</h3>
                  <div className="space-y-2">
                    <Link href="/mzgblog" className="block text-sm text-gray-600 hover:text-red-600 transition-colors">
                      All Categories
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Related Articles */}
        <div className="bg-gray-50 py-16">
          <div className="container mx-auto px-4 md:px-8">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Articles</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <h3 className="text-lg font-bold mb-2">Explore More Content</h3>
                <p className="text-gray-600 text-sm mb-4">
                  Discover more insights and technical articles in our blog.
                </p>
                <Link href="/mzgblog">
                  <Button variant="outline" className="text-sm">
                    View All Posts
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
}
