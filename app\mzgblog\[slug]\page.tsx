import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { formatDate } from "@/lib/utils"
import { getBlogPost, type ContentImage } from "@/app/actions/blog-actions-db-final"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Share2, Calendar, User, Tag } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import type { Metadata } from "next"

interface BlogPostPageProps {
  params: Promise<{
    slug: string
  }>
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const resolvedParams = await params
  const post = await getBlogPost(resolvedParams.slug)
  
  if (!post) {
    return {
      title: "Post Not Found",
    }
  }

  return {
    title: post.title,
    description: post.excerpt || post.content.substring(0, 160),
    openGraph: {
      title: post.title,
      description: post.excerpt || post.content.substring(0, 160),
      type: "article",
      publishedTime: post.created_at,
      authors: ["MZG Tools"],
    },
  }
}

// Enhanced content processing function with database images and smart insertion
function enhanceContentWithImages(content: string, contentImages?: ContentImage[]): string {
  // First, process any existing Markdown-style image syntax
  let processedContent = content

  // Convert Markdown image syntax: ![alt](url "title")
  processedContent = processedContent.replace(
    /!\[([^\]]*)\]\(([^)]+)(?:\s+"([^"]*)")?\)/g,
    '<img src="$2" alt="$1" title="$3" align="center" loading="lazy" />'
  )

  // Convert custom alignment syntax: ![alt](url){align:left}
  processedContent = processedContent.replace(
    /!\[([^\]]*)\]\(([^)]+)\)\{align:(left|right|center)\}/g,
    '<img src="$2" alt="$1" align="$3" loading="lazy" />'
  )

  // Use database images if available, otherwise fallback to sample images
  const imagesToUse = contentImages && Array.isArray(contentImages) && contentImages.length > 0
    ? contentImages
    : [
        {
          id: 'sample1',
          url: '/gun-drill-anatomy.png',
          alt: 'Industrial milling tool showcase',
          position: 'left' as const
        },
        {
          id: 'sample2',
          url: '/custom-gun-drill-manufacturing.png',
          alt: 'CNC machining process',
          position: 'center' as const
        },
        {
          id: 'sample3',
          url: '/counterbore-tool.png',
          alt: 'Precision manufacturing equipment',
          position: 'right' as const
        }
      ]

  // Normalize content - handle different paragraph formats
  // Split by double newlines first, then by periods followed by space and capital letter
  let paragraphs = processedContent.split('\n\n').filter(p => p.trim().length > 0)
  
  // If we don't have enough paragraphs, try splitting by sentences
  if (paragraphs.length < 3) {
    // Split by periods followed by space and capital letter (sentence boundaries)
    const sentences = processedContent.split(/\.\s+(?=[A-Z])/).filter(s => s.trim().length > 20)
    
    // Group sentences into paragraphs (2-3 sentences per paragraph)
    paragraphs = []
    for (let i = 0; i < sentences.length; i += 2) {
      const paragraph = sentences.slice(i, i + 2).join('. ')
      if (paragraph.trim().length > 0) {
        paragraphs.push(paragraph + (paragraph.endsWith('.') ? '' : '.'))
      }
    }
  }

  // Smart image insertion strategy
  if (paragraphs.length > 1 && imagesToUse.length > 0) {
    let imageIndex = 0

    // Insert first image after opening paragraph (if we have at least 2 paragraphs)
    if (paragraphs.length > 1 && imageIndex < imagesToUse.length) {
      const image = imagesToUse[imageIndex]
      const alignment = image.position || 'left'
      paragraphs.splice(1, 0,
        `<img src="${image.url}" alt="${image.alt}" align="${alignment}" loading="lazy" />`
      )
      imageIndex++
    }

    // Insert images every 2-3 paragraphs for natural flow
    let insertPosition = 3
    while (insertPosition < paragraphs.length && imageIndex < imagesToUse.length) {
      const image = imagesToUse[imageIndex]
      const alignment = image.position || (['left', 'right', 'center'][imageIndex % 3] as 'left' | 'right' | 'center')

      paragraphs.splice(insertPosition, 0,
        `<img src="${image.url}" alt="${image.alt}" align="${alignment}" loading="lazy" />`
      )
      imageIndex++
      
      // Move to next insertion point (skip 2-3 paragraphs)
      insertPosition += 3
    }
  }

  return paragraphs.join('\n\n')
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const resolvedParams = await params
  const post = await getBlogPost(resolvedParams.slug)

  if (!post) {
    notFound()
  }

  // Process content with images
  const enhancedContent = enhanceContentWithImages(post.content, post.content_images)

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Back Button */}
          <div className="mb-8">
            <Link href="/mzgblog">
              <Button variant="ghost" className="text-gray-600 hover:text-gray-900">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Blog
              </Button>
            </Link>
          </div>

          {/* Article Header */}
          <header className="mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4 leading-tight">
              {post.title}
            </h1>
            
            {/* Meta Information */}
            <div className="flex flex-wrap items-center gap-6 text-gray-600 mb-6">
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <time dateTime={post.created_at}>
                  {formatDate(post.created_at)}
                </time>
              </div>
              
              <div className="flex items-center gap-2">
                <User className="w-4 h-4" />
                <span>MZG Tools</span>
              </div>
              
              {post.category && (
                <div className="flex items-center gap-2">
                  <Tag className="w-4 h-4" />
                  <Link 
                    href={`/mzgblog?category=${post.category.slug}`}
                    className="hover:text-blue-600 transition-colors"
                  >
                    {post.category.name}
                  </Link>
                </div>
              )}
            </div>

            {/* Tags */}
            {post.tags && Array.isArray(post.tags) && post.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-8">
                {post.tags.map((tag) => (
                  <Link
                    key={tag.id}
                    href={`/mzgblog?tag=${tag.slug}`}
                    className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-3 py-1 rounded-full text-sm transition-colors"
                  >
                    #{tag.name}
                  </Link>
                ))}
              </div>
            )}

            {/* Share Button */}
            <div className="flex items-center gap-4">
              <Button variant="outline" size="sm">
                <Share2 className="w-4 h-4 mr-2" />
                Share
              </Button>
            </div>
          </header>

          {/* Article Content */}
          <article className="prose prose-lg max-w-none">
            <div 
              className="blog-content"
              dangerouslySetInnerHTML={{ __html: enhancedContent }}
            />
          </article>
        </div>
      </main>

      <Footer />
      
      <style jsx global>{`
        .blog-content img {
          max-width: 100%;
          height: auto;
          border-radius: 8px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
          transition: transform 0.2s ease-in-out;
        }
        
        .blog-content img:hover {
          transform: scale(1.02);
        }
        
        .blog-content img[align="left"] {
          float: left;
          margin: 0 20px 20px 0;
          width: 240px;
          height: 135px;
          object-fit: cover;
        }
        
        .blog-content img[align="right"] {
          float: right;
          margin: 0 0 20px 20px;
          width: 240px;
          height: 135px;
          object-fit: cover;
        }
        
        .blog-content img[align="center"] {
          display: block;
          margin: 30px auto;
          width: 480px;
          height: 270px;
          object-fit: cover;
        }
        
        @media (max-width: 768px) {
          .blog-content img[align="left"],
          .blog-content img[align="right"] {
            float: none;
            display: block;
            margin: 20px auto;
            width: 100%;
            max-width: 400px;
            height: auto;
            aspect-ratio: 16/9;
          }
          
          .blog-content img[align="center"] {
            width: 100%;
            max-width: 400px;
            height: auto;
            aspect-ratio: 16/9;
          }
        }
        
        .blog-content p {
          margin-bottom: 1.5rem;
          line-height: 1.7;
          color: #374151;
        }
        
        .blog-content::after {
          content: "";
          display: table;
          clear: both;
        }
      `}</style>
    </div>
  )
}
