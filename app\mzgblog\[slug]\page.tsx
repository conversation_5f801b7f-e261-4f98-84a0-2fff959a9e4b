import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { formatDate } from "@/lib/utils"
import { getBlogPost } from "@/app/actions/blog-actions-db-final"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Share2, Calendar, User, Tag } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import type { Metadata } from "next"

interface BlogPostPageProps {
  params: Promise<{
    slug: string
  }>
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const resolvedParams = await params
  const post = await getBlogPost(resolvedParams.slug)

  if (!post) {
    return {
      title: "Post Not Found | MZG Tools",
    }
  }

  return {
    title: `${post.title} | MZG Tools Blog`,
    description: post.excerpt,
  }
}

// Safe image URL function to handle invalid URLs
function getSafeImageUrl(imageUrl?: string): string {
  const defaultImage = "/placeholder.svg?height=800&width=1200&query=industrial tool"

  if (!imageUrl) {
    return defaultImage
  }

  // Check if it's a relative path starting with /
  if (imageUrl.startsWith('/')) {
    return imageUrl
  }

  // For absolute URLs, try to validate them
  try {
    new URL(imageUrl)
    return imageUrl
  } catch {
    return defaultImage
  }
}

// Enhanced content processing function with multiple image integration strategies
function enhanceContentWithImages(content: string): string {
  // Sample images with consistent 16:9 aspect ratio
  const sampleImages = [
    '/placeholder.svg?height=135&width=240&query=milling tool',
    '/placeholder.svg?height=270&width=480&query=cnc machine',
    '/placeholder.svg?height=135&width=240&query=industrial equipment',
    '/placeholder.svg?height=180&width=320&query=precision tools',
    '/placeholder.svg?height=135&width=240&query=manufacturing process'
  ]

  // First, process any existing Markdown-style image syntax
  let processedContent = content

  // Convert Markdown image syntax: ![alt](url "title")
  processedContent = processedContent.replace(
    /!\[([^\]]*)\]\(([^)]+)(?:\s+"([^"]*)")?\)/g,
    '<img src="$2" alt="$1" title="$3" align="center" />'
  )

  // Convert custom alignment syntax: ![alt](url){align:left}
  processedContent = processedContent.replace(
    /!\[([^\]]*)\]\(([^)]+)\)\{align:(left|right|center)\}/g,
    '<img src="$2" alt="$1" align="$3" />'
  )

  // Split content into paragraphs for smart insertion
  const paragraphs = processedContent.split('\n\n')

  // Smart image insertion strategy
  if (paragraphs.length > 2) {
    let imageIndex = 0

    // Insert first image after opening paragraph (left-aligned for text flow)
    if (paragraphs.length > 1 && imageIndex < sampleImages.length) {
      paragraphs.splice(1, 0, `<img src="${sampleImages[imageIndex]}" alt="Industrial milling tool showcase" align="left" />`)
      imageIndex++
    }

    // Insert images every 2-3 paragraphs for natural flow
    for (let i = 3; i < paragraphs.length && imageIndex < sampleImages.length; i += 3) {
      const alignments = ['right', 'center', 'left']
      const alignment = alignments[imageIndex % alignments.length]
      const altTexts = [
        'CNC machining process',
        'Precision manufacturing equipment',
        'Advanced tooling technology',
        'Industrial automation system'
      ]

      paragraphs.splice(i, 0, `<img src="${sampleImages[imageIndex]}" alt="${altTexts[imageIndex % altTexts.length]}" align="${alignment}" />`)
      imageIndex++
      i++ // Adjust index due to insertion
    }
  }

  return paragraphs.join('\n\n')
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const resolvedParams = await params
  const post = await getBlogPost(resolvedParams.slug)

  if (!post) {
    notFound()
  }

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section - Further reduced height to 50% */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="container mx-auto px-4 py-2 md:py-4 lg:py-5">
            <div className="grid lg:grid-cols-2 gap-4 lg:gap-8 items-center min-h-[100px] lg:min-h-[125px]">
              <div className="max-w-4xl order-2 lg:order-1">
                <div className="inline-block bg-red-600 text-white px-2 py-0.5 rounded-full text-xs font-medium mb-2">
                  {post.category?.name || 'Uncategorized'}
                </div>
                <h1 className="text-lg md:text-xl lg:text-2xl font-bold mb-2 leading-tight text-gray-900">
                  {post.title}
                </h1>
                <p className="text-xs md:text-sm mb-2 text-gray-600 leading-relaxed max-w-2xl">
                  {post.excerpt || 'Discover expert insights and technical knowledge in our comprehensive blog post.'}
                </p>
                <div className="flex flex-wrap items-center gap-2 text-xs text-gray-500">
                  <div className="flex items-center gap-1">
                    <Calendar size={12} />
                    <span>{formatDate(post.createdAt)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <User size={12} />
                    <span>{post.author}</span>
                  </div>
                  {post.viewCount > 0 && (
                    <div className="flex items-center gap-1">
                      <span>👁</span>
                      <span>{post.viewCount} views</span>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex justify-center lg:justify-end order-1 lg:order-2">
                <div className="relative w-full max-w-[280px] lg:max-w-[320px]">
                  <div className="aspect-[16/9] bg-gray-50 rounded-lg overflow-hidden shadow-md">
                    <Image
                      src={getSafeImageUrl(post.imageUrl)}
                      alt={post.featuredImageAlt || post.title}
                      width={320}
                      height={180}
                      className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                      priority
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>



        {/* Main Content */}
        <div className="container mx-auto px-4 md:px-8 py-8">
          <div className="grid lg:grid-cols-4 gap-8">
            {/* Main Article */}
            <div className="lg:col-span-3">
              {/* Meta Information */}
              <div className="flex flex-wrap items-center gap-4 py-6 border-b border-gray-200 text-gray-600">
                <div className="flex items-center gap-2">
                  <Calendar size={16} />
                  <span>{formatDate(post.createdAt)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <User size={16} />
                  <span>{post.author}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Tag size={16} />
                  <span>{post.category?.name || 'Uncategorized'}</span>
                </div>
                <div className="ml-auto">
                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                    <Share2 size={16} />
                    Share
                  </Button>
                </div>
              </div>

              {/* Article Content */}
              <article className="prose prose-lg max-w-none py-8 text-[14px] leading-relaxed">
                <div
                  className="article-content
                    [&_img]:max-w-full [&_img]:h-auto [&_img]:rounded-lg [&_img]:shadow-lg [&_img]:transition-all [&_img]:duration-300 [&_img]:hover:shadow-xl [&_img]:hover:scale-[1.02]
                    [&_img[align='left']]:float-left [&_img[align='left']]:mr-6 [&_img[align='left']]:mb-4 [&_img[align='left']]:mt-2 [&_img[align='left']]:max-w-[240px] [&_img[align='left']]:w-[240px] [&_img[align='left']]:h-[135px] [&_img[align='left']]:object-cover [&_img[align='left']]:shape-outside-margin-box
                    [&_img[align='right']]:float-right [&_img[align='right']]:ml-6 [&_img[align='right']]:mb-4 [&_img[align='right']]:mt-2 [&_img[align='right']]:max-w-[240px] [&_img[align='right']]:w-[240px] [&_img[align='right']]:h-[135px] [&_img[align='right']]:object-cover [&_img[align='right']]:shape-outside-margin-box
                    [&_img[align='center']]:block [&_img[align='center']]:mx-auto [&_img[align='center']]:my-8 [&_img[align='center']]:max-w-[480px] [&_img[align='center']]:w-[480px] [&_img[align='center']]:h-[270px] [&_img[align='center']]:object-cover
                    max-md:[&_img[align='left']]:float-none max-md:[&_img[align='left']]:block max-md:[&_img[align='left']]:mx-auto max-md:[&_img[align='left']]:max-w-[280px] max-md:[&_img[align='left']]:w-[280px] max-md:[&_img[align='left']]:h-[158px] max-md:[&_img[align='left']]:mb-6
                    max-md:[&_img[align='right']]:float-none max-md:[&_img[align='right']]:block max-md:[&_img[align='right']]:mx-auto max-md:[&_img[align='right']]:max-w-[280px] max-md:[&_img[align='right']]:w-[280px] max-md:[&_img[align='right']]:h-[158px] max-md:[&_img[align='right']]:mb-6
                    max-md:[&_img[align='center']]:max-w-[320px] max-md:[&_img[align='center']]:w-[320px] max-md:[&_img[align='center']]:h-[180px]
                    [&_p]:mb-6 [&_p]:leading-[1.8] [&_p]:text-justify [&_p]:hyphens-auto
                    [&_p:has(+img[align='left'])]:mb-2 [&_p:has(+img[align='right'])]:mb-2
                    [&_h2]:mt-12 [&_h2]:mb-6 [&_h2]:text-2xl [&_h2]:font-semibold [&_h2]:text-gray-800 [&_h2]:clear-both
                    [&_h3]:mt-10 [&_h3]:mb-4 [&_h3]:text-xl [&_h3]:font-semibold [&_h3]:text-gray-700 [&_h3]:clear-both
                    [&_blockquote]:border-l-4 [&_blockquote]:border-red-600 [&_blockquote]:pl-6 [&_blockquote]:my-8 [&_blockquote]:italic [&_blockquote]:text-gray-600 [&_blockquote]:bg-gray-50 [&_blockquote]:py-4 [&_blockquote]:clear-both
                    [&_ul]:my-6 [&_ul]:pl-6 [&_ol]:my-6 [&_ol]:pl-6
                    [&_li]:mb-3 [&_li]:leading-relaxed"
                  dangerouslySetInnerHTML={{ __html: enhanceContentWithImages(post.content) }}
                />
              </article>

              {/* Tags */}
              {post.tags && post.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-8">
                  {post.tags.map((tag) => (
                    <Link
                      key={tag.id}
                      href={`/mzgblog?tag=${tag.slug}`}
                      className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-3 py-1 rounded-full text-sm transition-colors"
                    >
                      #{tag.name}
                    </Link>
                  ))}
                </div>
              )}

              {/* Navigation */}
              <div className="mt-12 border-t border-gray-200 pt-8">
                <Link href="/mzgblog">
                  <Button variant="outline" className="flex items-center gap-2">
                    <ArrowLeft size={16} />
                    Back to Blog
                  </Button>
                </Link>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-8">
                {/* Recent Posts */}
                <div className="bg-gray-50 rounded-lg p-6 mb-6">
                  <h3 className="text-lg font-bold mb-4">Recent Posts</h3>
                  <div className="space-y-3">
                    <Link href="/mzgblog" className="block text-sm text-gray-600 hover:text-red-600 transition-colors">
                      View all blog posts
                    </Link>
                  </div>
                </div>

                {/* Categories */}
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-bold mb-4">Categories</h3>
                  <div className="space-y-2">
                    <Link href="/mzgblog" className="block text-sm text-gray-600 hover:text-red-600 transition-colors">
                      All Categories
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Related Articles */}
        <div className="bg-gray-50 py-16">
          <div className="container mx-auto px-4 md:px-8">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Articles</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <h3 className="text-lg font-bold mb-2">Explore More Content</h3>
                <p className="text-gray-600 text-sm mb-4">
                  Discover more insights and technical articles in our blog.
                </p>
                <Link href="/mzgblog">
                  <Button variant="outline" className="text-sm">
                    View All Posts
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
}
