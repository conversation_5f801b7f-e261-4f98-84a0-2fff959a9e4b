"use client"

import { useState, useCallback, useRef } from "react"
import { Upload, X, Image as ImageIcon, Loader2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"

interface ContentImage {
  id: string
  url: string
  alt: string
  position?: 'left' | 'right' | 'center'
  width?: number
  height?: number
}

interface ImageUploadZoneProps {
  images: ContentImage[]
  onImagesChange: (images: ContentImage[]) => void
  maxImages?: number
}

export default function ImageUploadZone({ 
  images, 
  onImagesChange, 
  maxImages = 10 
}: ImageUploadZoneProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [uploading, setUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 压缩图片函数
  const compressImage = useCallback((file: File, maxWidth = 800, quality = 0.8): Promise<Blob> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      const img = new window.Image()
      
      img.onload = () => {
        // 计算新尺寸，保持宽高比
        const ratio = Math.min(maxWidth / img.width, maxWidth / img.height)
        canvas.width = img.width * ratio
        canvas.height = img.height * ratio
        
        // 绘制压缩后的图片
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
        
        canvas.toBlob(resolve, 'image/jpeg', quality)
      }
      
      img.src = URL.createObjectURL(file)
    })
  }, [])

  // 上传图片到服务器
  const uploadImage = useCallback(async (file: File): Promise<string> => {
    // 压缩图片
    const compressedBlob = await compressImage(file)
    const compressedFile = new File([compressedBlob!], file.name, { type: 'image/jpeg' })
    
    const formData = new FormData()
    formData.append('file', compressedFile)
    formData.append('type', 'blog-content')
    
    const response = await fetch('/api/admin-mzg/upload-image', {
      method: 'POST',
      body: formData,
    })
    
    if (!response.ok) {
      throw new Error('Upload failed')
    }
    
    const data = await response.json()
    return data.url
  }, [compressImage])

  // 处理文件选择
  const handleFiles = useCallback(async (files: FileList) => {
    if (images.length + files.length > maxImages) {
      alert(`最多只能上传 ${maxImages} 张图片`)
      return
    }

    setUploading(true)
    
    try {
      const newImages: ContentImage[] = []
      
      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        if (!file.type.startsWith('image/')) continue
        
        const url = await uploadImage(file)
        const img = new window.Image()
        
        await new Promise((resolve) => {
          img.onload = resolve
          img.src = url
        })
        
        newImages.push({
          id: `img_${Date.now()}_${i}`,
          url,
          alt: file.name.replace(/\.[^/.]+$/, ""),
          width: img.width,
          height: img.height,
        })
      }
      
      onImagesChange([...images, ...newImages])
    } catch (error) {
      console.error('Upload error:', error)
      alert('图片上传失败，请重试')
    } finally {
      setUploading(false)
    }
  }, [images, maxImages, onImagesChange, uploadImage])

  // 拖拽处理
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
    
    const files = e.dataTransfer.files
    if (files.length > 0) {
      handleFiles(files)
    }
  }, [handleFiles])

  // 点击上传
  const handleClick = useCallback(() => {
    fileInputRef.current?.click()
  }, [])

  // 删除图片
  const removeImage = useCallback((id: string) => {
    onImagesChange(images.filter(img => img.id !== id))
  }, [images, onImagesChange])

  // 更新图片信息
  const updateImage = useCallback((id: string, updates: Partial<ContentImage>) => {
    onImagesChange(images.map(img => 
      img.id === id ? { ...img, ...updates } : img
    ))
  }, [images, onImagesChange])

  return (
    <div className="space-y-4">
      {/* 上传区域 */}
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer ${
          isDragging 
            ? 'border-red-500 bg-red-50' 
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          className="hidden"
          onChange={(e) => e.target.files && handleFiles(e.target.files)}
        />
        
        {uploading ? (
          <div className="flex flex-col items-center">
            <Loader2 className="h-12 w-12 text-red-500 animate-spin mb-4" />
            <p className="text-gray-600">正在上传图片...</p>
          </div>
        ) : (
          <div className="flex flex-col items-center">
            <Upload className="h-12 w-12 text-gray-400 mb-4" />
            <p className="text-lg font-medium text-gray-700 mb-2">
              拖拽图片到此处或点击上传
            </p>
            <p className="text-sm text-gray-500">
              支持 JPG、PNG、GIF 格式，最多 {maxImages} 张图片
            </p>
          </div>
        )}
      </div>

      {/* 图片列表 */}
      {images.length > 0 && (
        <div className="space-y-4">
          <h4 className="font-medium text-gray-700">已上传的图片 ({images.length})</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {images.map((image) => (
              <div key={image.id} className="border rounded-lg p-4 space-y-3">
                <div className="relative">
                  <Image
                    src={image.url}
                    alt={image.alt}
                    width={200}
                    height={120}
                    className="w-full h-32 object-cover rounded"
                  />
                  <Button
                    variant="destructive"
                    size="sm"
                    className="absolute top-2 right-2"
                    onClick={() => removeImage(image.id)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="space-y-2">
                  <input
                    type="text"
                    placeholder="图片描述"
                    value={image.alt}
                    onChange={(e) => updateImage(image.id, { alt: e.target.value })}
                    className="w-full px-3 py-2 border rounded text-sm"
                  />
                  
                  <select
                    value={image.position || 'center'}
                    onChange={(e) => updateImage(image.id, { 
                      position: e.target.value as 'left' | 'right' | 'center' 
                    })}
                    className="w-full px-3 py-2 border rounded text-sm"
                  >
                    <option value="left">左对齐</option>
                    <option value="right">右对齐</option>
                    <option value="center">居中</option>
                  </select>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
