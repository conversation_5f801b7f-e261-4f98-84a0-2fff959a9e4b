"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function TappingToolHolderPage() {
  // Tapping Tool Holder相关的默认图片
  const defaultTappingImages = [
    "/images/C55-1.png",
    "/images/C55-2.png",
    "/images/C56-1.png",
    "/images/C57-1.png",
    "/images/C57-2.png",
    "/images/C58-1.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/milling-tool-holder/tapping-tool-holder");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认Tapping图片
          setGalleryImages(defaultTappingImages);
        }
      } else {
        // API请求失败，使用默认Tapping图片
        setGalleryImages(defaultTappingImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认Tapping图片
      setGalleryImages(defaultTappingImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认Tapping图片，避免显示无关图片
    setGalleryImages(defaultTappingImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data based on the Tapping Tool Holder system
  const products = [
    {
      id: "tap-holder-001",
      name: "Torque Telescopic Tapping Protection Holder (GTP)",
      image: "/images/C55-1.png",
      description: "Provides protective torque control during tapping operations, ensuring precision and preventing damage.",
      series: "GTP Series",
      interface: "BT/HSK/CAT",
      application: "Torque protection tapping operations",
      pageNumber: "C55",
    },
    {
      id: "tap-holder-002",
      name: "Radial Floating Reaming Tool Holder (FDER)",
      image: "/images/C55-2.png",
      description: "Provides protective torque control during tapping operations, ensuring precision and preventing damage.",
      series: "FDER Series",
      interface: "BT/HSK/CAT",
      application: "Radial floating reaming operations",
      pageNumber: "C55",
    },
    {
      id: "tap-holder-003",
      name: "Flexible Telescopic Tapping Protection Tool Holder (G)",
      image: "/images/C56-1.png",
      description: "Offers flexible telescopic capabilities for tapping operations, providing enhanced protection.",
      series: "G Series",
      interface: "BT/HSK/CAT",
      application: "Flexible telescopic tapping",
      pageNumber: "C56",
    },
    {
      id: "tap-holder-004",
      name: "Micro Telescopic Tapping Protection Tool Holder (VER)",
      image: "/images/C57-1.png",
      description: "Provides micro telescopic capabilities for tapping operations, offering enhanced protection.",
      series: "VER Series",
      interface: "BT/HSK/CAT",
      application: "Micro telescopic tapping protection",
      pageNumber: "C57",
    },
    {
      id: "tap-holder-005",
      name: "Micro Telescopic Synchronous Tapping Protection Tool Holder (SVER)",
      image: "/images/C57-2.png",
      description: "Features micro telescopic synchronous movement for precise tapping operations and protection.",
      series: "SVER Series",
      interface: "BT/HSK/CAT",
      application: "Synchronous tapping operations",
      pageNumber: "C57",
    },
    {
      id: "tap-holder-006",
      name: "Rigid Tapping Protection Tool Holder (TER)",
      image: "/images/C58-1.png",
      description: "Offers rigid protection during tapping operations, ensuring stability and accuracy.",
      series: "TER Series",
      interface: "BT/HSK/CAT",
      application: "Rigid tapping protection",
      pageNumber: "C58",
    },
  ]

  // Performance features based on the provided content
  const performanceFeatures = [
    {
      icon: "Shield",
      title: "Intelligent Axial Compensation (Telescopic Action)",
      description: "Core performance feature providing axial float to compensate for synchronization errors between spindle rotation and Z-axis feed. Prevents excessive axial forces and tap breakage through controlled movement.",
    },
    {
      icon: "Target",
      title: "Torque Overload Protection",
      description: "Select models incorporate adjustable torque-limiting clutch. Critical safety feature causes holder to slip if tapping torque exceeds preset limit, providing ultimate protection against tap breakage.",
    },
    {
      icon: "Zap",
      title: "Rigid Tapping with Safety Backup (TER)",
      description: "Designed for modern machines with rigid tapping cycles, providing highly rigid connection for maximum precision while incorporating safety mechanism to protect tap in unexpected events.",
    },
  ]

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Compensation Mechanisms & Clamping Systems",
      description: "Torsion Telescopic (GTP): Combines axial float with torque-limiting clutch. Flexible Telescopic (G): Robust axial float for non-synchronous tapping. Micro Telescopic (VER): Fine, sensitive axial compensation. Micro Telescopic Synchronous (SVER): Ultra-sensitive compensation for high-speed rigid tapping. Rigid with Protection (TER): Designed for rigid tapping cycles with safety backup. Radial Floating (FDER): Provides radial compensation for hole misalignment.",
    },
    {
      title: "Collet Systems & Interface Types",
      description: "G-Type Collets (for GTP/G): Quick-change collets specifically for tapping, often with torque-limiting feature. Available in series like G3, G12, G24. ER-Type Collets (for VER/SVER/TER): Utilizes industry-standard ER collets, often specialized tapping collets with square drive. Available for ER16, ER20, ER25, ER32, and ER40 systems. Comprehensive shank interface availability: BT, HSK, CAT, C (Straight Shank), MTA, MTB, NT, and JT.",
    },
    {
      title: "System Versatility & Adaptability",
      description: "Tapping holders available across unparalleled range of machine interfaces—including BT, HSK, CAT, Straight Shank (C), Morse Taper (MTA/MTB), and Jacobs Taper (JT)—ensuring high-performance tapping solution for virtually any machine in any workshop. Problem-solving compensation and system versatility for maximum adaptability.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Tapping Tool Holder Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG Tapping Tool Holder System
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  MZG provide a comprehensive and detailed introduction to our specialized range of Tapping Tool Holders. Tapping is a high-stakes operation where the tool is extremely susceptible to breakage. Our tapping holders are not mere adapters; they are sophisticated engineering solutions designed to protect the tap, compensate for machine inaccuracies, and guarantee the production of high-quality, precise threads. Each type is meticulously designed to address specific challenges, from non-synchronous machines to the hyper-precise demands of modern rigid tapping.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/millingcutter1.png"
                    alt="MZG Professional Tapping Tool Holder System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">System Performance Analysis</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of a Tapping Tool Holder is defined by its ability to provide process security through intelligent compensation and protection mechanisms, ensuring thread quality and preventing costly downtime. A core performance feature is the holder's ability to provide axial float.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      This is critical for compensating for any minute synchronization errors between the machine's spindle rotation and its Z-axis feed. This float prevents excessive axial forces on the tap's flanks, which is a primary cause of thread form damage and tap breakage. Flexible/Micro Telescopic (G, VER) offer controlled axial movement, ideal for general-purpose CNC tapping.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      Synchronous Telescopic (SVER) is the pinnacle of performance, offering ultra-sensitive micro-compensation specifically for high-speed rigid tapping cycles. It absorbs minute acceleration/deceleration mismatches of the machine, ensuring zero axial force on the tap for perfect threads.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      Select models incorporate adjustable torque-limiting clutch (GTP series), critical safety feature that causes holder to slip if tapping torque exceeds preset limit. Problem-Solving Compensation (FDER) offers unique radial floating capability, allowing tap to self-center in pre-drilled hole.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Functional Types: GTP, G, SVER, VER, TER, FDER</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Compensation: Axial, Radial, Torque Protection</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Interfaces: BT, HSK, CAT, C, MTA, MTB, NT, JT</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Tapping Modes: Synchronous & Non-Synchronous</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Application: High-quality precise thread creation</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                          <span className="text-gray-900 text-right">{product.series}</span>
                        </div>
                      )}
                      {product.application && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.application}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultTappingImages[0]}
                    alt="Tapping Tool Holder Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                    </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultTappingImages[imageIndex % defaultTappingImages.length]
                  : defaultTappingImages[index % defaultTappingImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Tapping Tool Holder Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                    </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Compensation Mechanisms & Clamping Systems":
                      return <Target className="h-6 w-6 text-blue-600 mr-3" />
                    case "Collet Systems & Interface Types":
                      return <Zap className="h-6 w-6 text-green-600 mr-3" />
                    case "System Versatility & Adaptability":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                    </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios &amp; Applicable Machining</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Speed, High-Volume Production (Synchronous Tapping):</strong> SVER holders essential for automotive, aerospace, and medical manufacturing where thread quality and cycle time are critical. Used on modern CNC machines with high-speed rigid tapping cycles</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>General CNC Machining:</strong> VER and TER holders are workhorses of modern job shop, providing reliable and precise tapping on wide range of CNC milling centers and lathes</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Machines without Synchronized Tapping:</strong> GTP and G series holders indispensable for older CNC machines, manual mills, or drill presses. Built-in telescopic compensation provides "intelligence" that machine lacks</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Challenging Materials and Blind Hole Tapping:</strong> GTP's torque control invaluable when tapping tough materials or blind holes, where risk of breakage is highest</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Imperfect Setups or Large Parts:</strong> FDER's radial float is problem-solver for applications where perfect alignment between spindle and hole is difficult to achieve, such as on large, complex workpieces</span>
                  </li>
                </ul>
              </div>

              {/* Machining Operations */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Main Functions
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Protect the Tap from Breakage:</strong> Most critical function, achieved through axial compensation to reduce stress and torque-limiting clutches to prevent overload during high-stakes tapping operations</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Ensure Consistent, High-Quality Thread Production:</strong> By compensating for machine errors and providing stable clamping, these holders guarantee that threads are cut to the correct pitch, form, and quality every time</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Primary Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Protect the Tap from Breakage:</strong> Most critical function, achieved through axial compensation to reduce stress and torque-limiting clutches to prevent overload during high-stakes tapping operations</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Ensure Consistent, High-Quality Thread Production:</strong> By compensating for machine errors and providing stable clamping, these holders guarantee that threads are cut to the correct pitch, form, and quality every time</span>
                  </li>
                </ul>
                  </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  System Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Compensate for Machine-Tool Synchronization Errors:</strong> Function as crucial intermediary, absorbing minute differences between spindle rotation and axis feed to prevent damage to tap and workpiece</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Universal Tapping Solution:</strong> Through extensive range of shank interfaces and compensation types, system offers high-performance tapping solution for any machine, any application, and any challenge</span>
                  </li>
                </ul>
                </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/milling-tool-holder/tapping-tool-holder" />
            </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional Tapping Tool Holder Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal tapping tool holders for high-quality thread production and tap protection. From intelligent compensation to torque overload protection, we provide the most sophisticated engineering solutions for your tapping challenges.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same milling-tool-holder directory
                const allToolHolderCategories = [
                  {
                    title: "SK High-Speed Tool Holders",
                    image: "/images/C05-1.png",
                    description: "High-speed precision tool holders",
                    url: "/standard-tools/milling-tool-holder/sk-high-speed",
                  },
                  {
                    title: "HM Hydraulic Tool Holders",
                    image: "/images/C09-1.png",
                    description: "Precision hydraulic clamping systems",
                    url: "/standard-tools/milling-tool-holder/hm-hydraulic",
                  },
                  {
                    title: "SR Shrink Fit Tool Holders",
                    image: "/images/C13-1.png",
                    description: "Thermal expansion precision clamping",
                    url: "/standard-tools/milling-tool-holder/sr-shrink-fit",
                },
                {
                  title: "ER Tool Holders",
                    image: "/images/c41-1.png",
                    description: "Versatile collet chuck systems",
                  url: "/standard-tools/milling-tool-holder/er-tool-holder",
                },
                {
                    title: "Morse Taper Tool Holders",
                    image: "/images/c71-1.png",
                    description: "Foundational precision tool holders",
                    url: "/standard-tools/milling-tool-holder/morse-taper",
                  },
                ];
                
                return allToolHolderCategories.slice(0, 5).map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Tool Holders" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
} 