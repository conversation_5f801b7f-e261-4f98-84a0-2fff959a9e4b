import { NextRequest, NextResponse } from 'next/server'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { sql } from '@/lib/database'
import { sendConsultationEmail } from '@/lib/email-service'

// 处理文件上传
async function saveUploadedFiles(formData: FormData): Promise<{
  urls: string[]
  metadata: Array<{
    originalName: string
    fileName: string
    size: number
    type: string
    url: string
  }>
}> {
  const uploadDir = join(process.cwd(), 'public', 'attachments', 'consultations')
  
  // 确保上传目录存在
  try {
    await mkdir(uploadDir, { recursive: true })
  } catch (error) {
    console.log('Directory already exists or created')
  }

  const urls: string[] = []
  const metadata: Array<{
    originalName: string
    fileName: string
    size: number
    type: string
    url: string
  }> = []

  // 处理所有上传的文件
  const files: File[] = []
  for (const [key, value] of formData.entries()) {
    if (key.startsWith('attachment_') && value instanceof File) {
      files.push(value)
    }
  }

  const timestamp = Date.now()
  for (let i = 0; i < files.length; i++) {
    const file = files[i]
    if (file.size === 0) continue

    // 生成唯一文件名
    const fileExtension = file.name.split('.').pop()
    const fileName = `consultation_${timestamp}_${i}.${fileExtension}`
    const filePath = join(uploadDir, fileName)
    const fileUrl = `/attachments/consultations/${fileName}`

    try {
      // 保存文件
      const bytes = await file.arrayBuffer()
      const buffer = Buffer.from(bytes)
      await writeFile(filePath, buffer)

      urls.push(fileUrl)
      metadata.push({
        originalName: file.name,
        fileName,
        size: file.size,
        type: file.type,
        url: fileUrl
      })
    } catch (error) {
      console.error(`Error saving file ${file.name}:`, error)
    }
  }

  return { urls, metadata }
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    
    // 提取表单数据
    const consultationData = {
      name: formData.get('name') as string,
      email: formData.get('email') as string,
      company: formData.get('company') as string,
      phone: formData.get('phone') as string,
      partDetails: formData.get('partDetails') as string,
      source: formData.get('source') as string || 'website',
      productPagePath: formData.get('productPagePath') as string || '',
      productName: formData.get('productName') as string || '',
    }

    // 验证必填字段
    const requiredFields = ['name', 'email', 'company', 'phone', 'partDetails']
    for (const field of requiredFields) {
      if (!consultationData[field as keyof typeof consultationData]) {
        return NextResponse.json(
          { success: false, message: `${field} 是必填字段` },
          { status: 400 }
        )
      }
    }

    // 处理文件上传
    const { urls: attachmentUrls, metadata: attachmentMetadata } = await saveUploadedFiles(formData)

    // 存储到数据库
    const result = await sql`
      INSERT INTO consultation (
        name, email, company, phone, part_details,
        attachment_urls, attachment_metadata, source,
        product_page_path, product_name, status
      ) VALUES (
        ${consultationData.name},
        ${consultationData.email},
        ${consultationData.company},
        ${consultationData.phone},
        ${consultationData.partDetails},
        ${JSON.stringify(attachmentUrls)},
        ${JSON.stringify(attachmentMetadata)},
        ${consultationData.source},
        ${consultationData.productPagePath || ''},
        ${consultationData.productName || ''},
        'new'
      )
      RETURNING id
    `

    // 准备邮件数据
    const insertedId = result[0].id
    const emailData = {
      id: insertedId,
      name: consultationData.name,
      email: consultationData.email,
      company: consultationData.company,
      phone: consultationData.phone,
      partDetails: consultationData.partDetails,
      source: consultationData.source,
      productPagePath: consultationData.productPagePath,
      productName: consultationData.productName,
      attachments: attachmentMetadata,
      submittedAt: new Date().toISOString()
    }

    // 发送邮件通知
    try {
      await sendConsultationEmail(emailData)
    } catch (emailError) {
      console.error('邮件发送失败:', emailError)
      // 即使邮件发送失败，也不影响数据存储
    }

    return NextResponse.json({
      success: true,
      message: '咨询信息已成功提交',
      data: {
        id: insertedId,
        attachmentCount: attachmentUrls.length
      }
    })

  } catch (error) {
    console.error('处理咨询表单时出错:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: '服务器错误，请稍后重试' 
      },
      { status: 500 }
    )
  }
} 