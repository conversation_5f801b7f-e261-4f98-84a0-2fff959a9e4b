"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function TapsPage() {
  // Taps相关的默认图片
  const defaultTapsImages = [
    "/images/L-TAP-PG01.JPG",
    "/images/L-TAP-PG02.JPG", 
    "/images/L-TAP-PG03.JPG",
    "/images/L-TAP-PG04.jpg",
    "/images/L-TAP-PG05.JPG",
    "/images/L-TAP-PG06.jpg",
    "/images/L-TAP-PG07.JPG",
    "/images/L-TAP-PG08.jpg",
    "/images/L-TAP-PG09.jpg",
    "/images/L-TAP-PG10.JPG",
    "/images/L-TAP-PG11.jpg",
    "/images/L-TAP-PG12.jpg",
    "/images/L-TAP-PG13.JPG",
    "/images/L-TAP-PG14.JPG",
    "/images/L-TAP-PG15.jpg"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/threading/taps");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认Taps图片
          setGalleryImages(defaultTapsImages);
        }
      } else {
        // API请求失败，使用默认Taps图片
        setGalleryImages(defaultTapsImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认Taps图片
      setGalleryImages(defaultTapsImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认Taps图片，避免显示无关图片
    setGalleryImages(defaultTapsImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);



  // Product data based on the provided content
  const products = [
    {
      id: "tap-008",
      name: "Spiral Groove Tap (JIS-SP, Metric)",
      image: "/images/L27-1.png",
      description: "Features upward chip removal, suitable for blind hole processing",
      threadStandards: "JIS, Metric",
      application: "Features upward chip removal, suitable for blind hole processing, common for steel, stainless steel, and cast iron",
      pageNumber: "L27",
    },
    {
      id: "tap-010",
      name: "Spiral Groove Tap - Short Cutting Edge (JIS-SP, Metric)",
      image: "/images/L28-1.png",
      description: "Short cutting edge design with upward chip removal for blind hole processing",
      threadStandards: "JIS, Metric",
      application: "Features upward chip removal, suitable for blind hole processing, common for steel, stainless steel, and cast iron",
      pageNumber: "L28",
    },
    {
      id: "tap-011",
      name: "JIS-SP American Spiral Groove Tap (UNC/UNF)",
      image: "/images/L29-1.png",
      description: "American standard spiral groove tap with upward chip removal for blind hole processing",
      threadStandards: "UNC/UNF",
      application: "Features upward chip removal, suitable for blind hole processing, common for steel, stainless steel, and cast iron",
      pageNumber: "L29",
    },
    {
      id: "tap-012",
      name: "JIS-SP British Spiral Groove Tap (W)",
      image: "/images/L30-1.png",
      description: "British standard spiral groove tap with upward chip removal for blind hole processing",
      threadStandards: "W (Whitworth)",
      application: "Features upward chip removal, suitable for blind hole processing, common for steel, stainless steel, and cast iron",
      pageNumber: "L30",
    },
    {
      id: "tap-013",
      name: "Straight Groove Tip Tap (JIS-PO, Metric)",
      image: "/images/L31-1.png",
      description: "Straight groove design with downward chip removal for through hole processing",
      threadStandards: "JIS, Metric",
      application: "Features downward chip removal, suitable for through hole processing, common for steel, stainless steel, and cast iron",
      pageNumber: "L31",
    },
    {
      id: "tap-014",
      name: "JIS-PO British Straight Groove Tip Tap (W)",
      image: "/images/L33-1.png",
      description: "British standard straight groove tap with downward chip removal for through hole processing",
      threadStandards: "W (Whitworth)",
      application: "Features downward chip removal, suitable for through hole processing, common for steel, stainless steel, and cast iron",
      pageNumber: "L33",
    },
    {
      id: "tap-015",
      name: "JIS-PO American Straight Groove Tip Tap (UNC/UNF)",
      image: "/images/L34-1.png",
      description: "American standard straight groove tap with downward chip removal for through hole processing",
      threadStandards: "UNC/UNF",
      application: "Features downward chip removal, suitable for through hole processing, common for steel, stainless steel, and cast iron",
      pageNumber: "L34",
    },
    {
      id: "tap-016",
      name: "JIS-NRT Extrusion Tap (Metric)",
      image: "/images/L35-1.png",
      description: "Specialized extrusion tap design for thin wall parts processing",
      threadStandards: "JIS, Metric",
      application: "Suitable for the processing of thin wall parts, common for steel, stainless steel, and cast iron",
      pageNumber: "L35",
    },
    {
      id: "tap-017",
      name: "JIS-NRT American Extrusion Tap (UNC/UNF)",
      image: "/images/L36-1.png",
      description: "American standard extrusion tap for thin wall parts processing",
      threadStandards: "UNC/UNF",
      application: "Suitable for the processing of thin wall parts, common for steel, stainless steel, and cast iron",
      pageNumber: "L36",
    },
    {
      id: "tap-018",
      name: "GB-SPS Spiral Groove Tap",
      image: "/images/L37-1.png",
      description: "Chinese national standard spiral groove tap with upward chip removal",
      threadStandards: "GB (Chinese Standard)",
      application: "Features upward chip removal, suitable for blind hole processing, common for steel, stainless steel, and cast iron",
      pageNumber: "L37",
    },
    {
      id: "tap-019",
      name: "GB-POZ Straight Groove Tip Tap",
      image: "/images/L38-1.png",
      description: "Chinese national standard straight groove tap with downward chip removal",
      threadStandards: "GB (Chinese Standard)",
      application: "Features downward chip removal, suitable for through hole processing, common for steel, stainless steel, and cast iron",
      pageNumber: "L38",
    },
    {
      id: "tap-020",
      name: "DIN-SP Spiral Groove Tap",
      image: "/images/L39-1.png",
      description: "German standard spiral groove tap with upward chip removal",
      threadStandards: "DIN (German Standard)",
      application: "Features upward chip removal, suitable for blind hole processing, common for steel, stainless steel, and cast iron",
      pageNumber: "L39",
    },
    {
      id: "tap-021",
      name: "DIN-PO Straight Groove Tip Tap",
      image: "/images/L40-1.png",
      description: "German standard straight groove tap with downward chip removal",
      threadStandards: "DIN (German Standard)",
      application: "Features downward chip removal, suitable for through hole processing, common for steel, stainless steel, and cast iron",
      pageNumber: "L40",
    },
    {
      id: "tap-022",
      name: "Standard Cylinder Pipe Thread Taps (Rp, G)",
      image: "/images/L41-1.png",
      description: "Cylinder pipe thread taps for both sealed and non-sealed applications",
      threadStandards: "Rp, G (ISO)",
      application: "Rp tap is for processing female threads with thread-seal (conforms to GB/T7306.1-2000 eqv ISO7-1:1994). G tap is for processing female threads of pipe fittings without thread-seal (conforms to GB/T7307-2001 eqv ISO228-1:1994)",
      pageNumber: "L41",
    },
    {
      id: "tap-023",
      name: "55° Taper Pipe Thread Taps (RC)",
      image: "/images/L41-2.png",
      description: "55° taper pipe thread taps for imperial threaded pipe sealing",
      threadStandards: "RC (ISO 7-1:1994)",
      application: "Suitable for processing internal threads of conical pipes sealed with imperial threads. Can be used to process malleable steel and stainless steel pipe fittings. Processed threads comply with GB7306.2-2000 eqv ISO7-1:1994 standard. Made of high-quality high-speed steel W6Mo4Cr4V2 for high precision and durability",
      pageNumber: "L41",
    },
    {
      id: "tap-024",
      name: "55° Taper Pipe Thread Tap with Spiral Flute (PT)",
      image: "/images/L41-3.png",
      description: "55° taper pipe thread tap with spiral flute design and TiN coating",
      threadStandards: "PT (ISO 7-1:1994)",
      application: "Suitable for processing internal threads of tapered pipe threads of stainless steel pipe fittings sealed with inch threads. Tap surface is TIN-treated, and processed threads comply with ISO7-1:1994 standard. Made of high-quality high-speed steel W6Mo4Cr4V2 for high precision and durability",
      pageNumber: "L41",
    },
    {
      id: "tap-025",
      name: "60° Taper Pipe Thread Taps (NPT)",
      image: "/images/L42-1.png",
      description: "60° taper pipe thread taps for thread-sealed applications",
      threadStandards: "NPT (ASME B1.20.1)",
      application: "Designed for processing female threads with thread-seal. Thread standard conforms to ASME Bl. 20.1-1983. Used to produce malleable fittings and stainless steel products",
      pageNumber: "L42",
    },
    {
      id: "tap-026",
      name: "60° Taper Pipe Thread Tap with Spiral Flute (NPT)",
      image: "/images/L42-2.png",
      description: "60° NPT taper pipe thread tap with spiral flute and TiN treatment",
      threadStandards: "NPT (ISO 7-1:1994)",
      application: "Suitable for processing internal threads of tapered pipe threads of stainless steel pipe fittings sealed with inch threads. Tap surface is TIN-treated, and processed threads comply with ISO7-1:1994 standard. Made of high-quality high-speed steel W6Mo4Cr4V2 for high precision and durability",
      pageNumber: "L42",
    },
    {
      id: "tap-027",
      name: "ANPT Taper Pipe Thread Taps",
      image: "/images/L42-3.png",
      description: "Aeronautical National Form taper pipe thread taps",
      threadStandards: "ANPT (MIL-P-7105B)",
      application: "Designed for taper pipe threads with thread-seal, Aeronautical National Form. Thread standard conforms to MIL-P-7105B",
      pageNumber: "L42",
    },
    {
      id: "tap-028",
      name: "NPTF Taper Pipe Thread Taps (HSS Dry Seal)",
      image: "/images/L43-1.png",
      description: "Dry seal pipe thread taps requiring no sealants",
      threadStandards: "NPTF (ANSI B1.20.3)",
      application: "Suitable for dry seal pipe thread processing. Thread standard conforms to ANSI Bl.20.3-1976. Assembly of NPTF thread does not require sealants",
      pageNumber: "L43",
    },
    {
      id: "tap-029",
      name: "60° Taper Pipe Thread Taps HSS Bright-Low Hook for Cast Iron",
      image: "/images/L43-2.png",
      description: "Specialized HSS taper pipe thread taps for cast iron applications",
      threadStandards: "NPT (ASME B1.20.1)",
      application: "Widely applied in malleable iron products. Due to reliable performance, also used for cast copper and other copper products. Thread standard conforms to GB/T12716-2002 eqv ASME Bl.20.1-1983",
      pageNumber: "L43",
    },
    {
      id: "tap-030",
      name: "NGT Taper Thread Tap for Gas Cylinder",
      image: "/images/L43-3.png",
      description: "Specialized taper thread tap for gas cylinder applications",
      threadStandards: "NGT (ANSI 5.7.1)",
      application: "Processed threads conform to ANSI5.7.1 NGT gas cylinder taper thread standard",
      pageNumber: "L43",
    },
    {
      id: "tap-031",
      name: "Taper Thread Tap for Gas Cylinder (PZ)",
      image: "/images/L44-1.png",
      description: "Gas cylinder taper thread tap with 3:25 taper ratio",
      threadStandards: "PZ (GB/T8335-2011)",
      application: "Designed for processing female thread for cylinder mouth with a 3:25 taper (for oxygen, acetylene, LPG, CO2 steel cylinders). Thread standard conforms to GB/T8335-2011",
      pageNumber: "L44",
    },
    {
      id: "tap-032",
      name: "Building Sleeves Joint Tap (JZTT)",
      image: "/images/L44-2.png",
      description: "Custom special order tap for building joint sleeves",
      threadStandards: "Custom Design",
      application: "A special order product, suitable for processing internal threads of building joint sleeves. Pitch and thread angle can be designed according to user requirements. Spiral flute and taps with inclination angle can be specified",
      pageNumber: "L44",
    },
    {
      id: "tap-033",
      name: "Radiator Tap (LH)",
      image: "/images/L44-3.png",
      description: "Left and right-hand thread tap for radiator applications",
      threadStandards: "LH (Left Hand)",
      application: "Suitable for processing left and right-hand internal threads on large column radiator taps",
      pageNumber: "L44",
    },
    {
      id: "tap-034",
      name: "NPSC, NPSL Pipe Steel Taps",
      image: "/images/L45-1.png",
      description: "American standard pipe steel taps for cylindrical internal threads",
      threadStandards: "NPSC/NPSL (H28)",
      application: "Processed threads conform to American Standard H28 for steel pipe cylindrical internal threads",
      pageNumber: "L45",
    },
    {
      id: "tap-035",
      name: "Rp Pipe Steel Taps",
      image: "/images/L45-2.png",
      description: "Chinese standard pipe steel taps for cylindrical internal threads",
      threadStandards: "Rp (GB/T7306.1)",
      application: "Processed threads conform to GB/T7306.1-2000 standard for steel pipe cylindrical internal threads",
      pageNumber: "L45",
    },
    {
      id: "tap-036",
      name: "Extruding Tap for Sucker Rod Joint (YG)",
      image: "/images/L45-3.png",
      description: "Specialized extruding tap for oil industry sucker rod joints",
      threadStandards: "YG (SY/T5029, SY/T5550)",
      application: "Suitable for extrusion forming of internal threads of sucker rod joints (through-hole and blind-hole types). Processed threads conform to SY/T5029-2003, SY/T5550-2006 standards",
      pageNumber: "L45",
    },
    {
      id: "tap-037",
      name: "Extruding Tap for Barrel Cap (TC)",
      image: "/images/L46-1.png",
      description: "Extruding tap designed for barrel cap thread forming",
      threadStandards: "TC (Custom)",
      application: "Designed for extrusion forming of internal threads of barrel caps",
      pageNumber: "L46",
    },
    {
      id: "tap-038",
      name: "Metric Extruding Tap for Filter (LQQ-M)",
      image: "/images/L46-2.png",
      description: "Metric extruding tap for filter thread forming",
      threadStandards: "Metric (GB/T192-197)",
      application: "Suitable for extrusion forming of internal threads of filters. Processed threads conform to GB/T192-2003, 193-2003, 196-2003, 197-2003 thread standards",
      pageNumber: "L46",
    },
    {
      id: "tap-039",
      name: "Unified Extruding Tap for Filter (LQQ-UN)",
      image: "/images/L46-3.png",
      description: "Unified thread extruding tap for filter applications",
      threadStandards: "Unified (ANSI B1.1)",
      application: "Suitable for extrusion forming of internal threads of filters. Processed threads conform to American Unified Thread Standard ANSI-B1.1",
      pageNumber: "L46",
    },
    {
      id: "tap-040",
      name: "Trapezoidal Thread Taps (TR...)",
      image: "/images/L47-1.png",
      description: "Trapezoidal thread taps for valve and nozzle applications",
      threadStandards: "TR (GB5796-2005)",
      application: "Suitable for processing left and right-hand trapezoidal internal threads on cold water nozzles, low-pressure valves, etc. Processed threads conform to GB5796-2005 standard",
      pageNumber: "L47",
    },
    {
      id: "tap-041",
      name: "Anti-loose Locknut Tap (NS)",
      image: "/images/L47-2.png",
      description: "Specialized tap for anti-loose locknut threads",
      threadStandards: "NS (Spiralock)",
      application: "Suitable for processing America Spiralock's anti-loose locknut threads",
      pageNumber: "L47",
    },
    {
      id: "tap-042",
      name: "Spiral Flute Metric Tap for Martensite Steel (MST)",
      image: "/images/L47-3.png",
      description: "Specialized spiral flute tap for martensite steel in automotive industry",
      threadStandards: "Metric (Automotive)",
      application: "Suitable for processing martensite materials in the automotive industry",
      pageNumber: "L47",
    },
    {
      id: "tap-043",
      name: "Special Metric Tap (FM)",
      image: "/images/L47-4.png",
      description: "Special metric tap for valve and industrial applications",
      threadStandards: "Metric (GB192-197)",
      application: "Suitable for processing internal threads on low-pressure valves and other industry products. Processed threads conform to 192, 193, 196, 197 (old secondary) precision standards",
      pageNumber: "L47",
    },
    {
      id: "tap-044",
      name: "55°Taper Pipe Thread Cutting U-value Flat Top Full Tooth Working Thread Plug Gauge",
      image: "/images/L48-1.png",
      description: "Comprehensive taper thread measurement gauge with U-value flat top design",
      threadStandards: "55° Taper (JB/T10031-1999)",
      application: "Suitable for comprehensive measurement of taper female and cylinder female threads with thread-seal. Design conforms to JB/T10031-1999 standard",
      pageNumber: "L48",
    },
    {
      id: "tap-045",
      name: "NPT Work Plug Gauge (Limit Type)",
      image: "/images/L48-2.png",
      description: "Limit type plug gauge for NPT taper female thread inspection",
      threadStandards: "NPT (ANSI B47.1)",
      application: "Designed for checking the taper female thread. Conforms to FED-STD-H28: ANSI B47.1",
      pageNumber: "L48",
    },
    {
      id: "tap-046",
      name: "NPT Work Ring Gauge",
      image: "/images/L49-1.png",
      description: "NPT work ring gauge for external thread inspection",
      threadStandards: "NPT (ANSI B47.1)",
      application: "Designed according to American Standard H28. Thread standard conforms to ANSI B47.1",
      pageNumber: "L49",
    },
    {
      id: "tap-047",
      name: "Taper Smooth Ring for Gas Cylinder",
      image: "/images/L49-2.png",
      description: "Smooth ring gauge for gas cylinder taper diameter inspection",
      threadStandards: "Gas Cylinder (GB/T8336-2011)",
      application: "Designed for checking the taper of the minor diameter of female thread for steel cylinder (oxygen, acetylene, LPG, CO2). Standard conforms to GB/T8336-2011",
      pageNumber: "L49",
    },
    {
      id: "tap-048",
      name: "R, NPT Die Chaser Body",
      image: "/images/L49-3.png",
      description: "Die chaser body for R and NPT taper pipe thread processing",
      threadStandards: "R, NPT (ISO 7-1, ASME B1.20.1)",
      application: "Used with R, NPT taper pipe thread die chaser inserts. Suitable for processing taper external threads (R, NPT) with thread-seal. Conforms to GB/T7306.2-2000 eqv ISO7-1:1994, ASME B1.20.1-1983, and GB/T12716-2002 standards",
      pageNumber: "L49",
    },
    {
      id: "tap-049",
      name: "R, NPT Die Chaser Body (Hilt Type)",
      image: "/images/L50-1.png",
      description: "Hilt type die chaser body for R and NPT taper pipe threads",
      threadStandards: "R, NPT (ISO 7-1, ASME B1.20.1)",
      application: "Used with R, NPT taper pipe thread die chaser inserts (hilt type). Suitable for processing taper external threads (R, NPT) with thread-seal. Conforms to GB/T7306.2-2000 eqv ISO7-1:1994, ASME B1.20.1-1983, and GB/T12716-2002 standards",
      pageNumber: "L50",
    },
    {
      id: "tap-050",
      name: "Die Chaser for Exporting",
      image: "/images/L50-2.png",
      description: "Export-specification die chaser with Japanese SAKAI standard dimensions",
      threadStandards: "PT, NPT (ISO 7-1, ASME B1.20.1)",
      application: "PT series and NPT series products are suitable for processing external threads with thread-seal. Used with die chaser bodies for export. Conforms to GB/T7306.2-2000 eqv ISO7-1:1994, ASME B1.20.1-1983, and GB/T12716-2002 standards. Structure dimensions conform to Japanese SAKAI standard",
      pageNumber: "L50",
    },
    {
      id: "tap-051",
      name: "R, NPT Die Chaser (Hilt Type)",
      image: "/images/L50-3.png",
      description: "Hilt type die chaser for R and NPT taper pipe thread cutting",
      threadStandards: "R, NPT (ISO 7-1, ASME B1.20.1)",
      application: "Used with R, NPT taper pipe thread die chaser bodies (hilt type). Suitable for processing external threads with thread-seal. Conforms to GB/T7306.2-2000 eqv ISO7-1:1994, ASME B1.20.1-1983, and GB/T12716-2002 standards",
      pageNumber: "L50",
    },
    {
      id: "tap-052",
      name: "R, NPT Die Chaser",
      image: "/images/L50-4.png",
      description: "Standard die chaser for R and NPT taper pipe thread processing",
      threadStandards: "R, NPT (ISO 7-1, ASME B1.20.1)",
      application: "Used with R, NPT taper pipe thread die chaser bodies. Suitable for processing external threads with thread-seal. Conforms to GB/T7306.2-2000 eqv ISO7-1:1994 and ASME B1.20.1-1983 standards",
      pageNumber: "L50",
    },
    {
      id: "tap-053",
      name: "Threaded Tap Drill Hole Diameter Reference (Drill Bits) (Metric)",
      image: "/images/L51-1.png",
      description: "Metric thread tap drill hole diameter reference chart and drill bits",
      threadStandards: "Metric",
      application: "Provides reference data for tap drill hole diameter for Metric System threads before tapping. D1 value represents the allowed upper/lower limits of the internal diameter after internal thread processing",
      pageNumber: "L51",
    },
    {
      id: "tap-054",
      name: "Extrusion Tap Drill Hole Diameter (Metric)",
      image: "/images/L52-1.png",
      description: "Metric extrusion tap drill hole diameter reference for thin-walled parts",
      threadStandards: "Metric",
      application: "Provides tap drill hole diameter for extrusion taps, suitable for threading thin-walled parts, common in steel, stainless steel, and cast iron materials",
      pageNumber: "L52",
    },
    {
      id: "tap-055",
      name: "Extrusion Tap Drill Hole Diameter (American Threads UNC/UNF)",
      image: "/images/L52-2.png",
      description: "American thread extrusion tap drill hole diameter reference for thin-walled parts",
      threadStandards: "UNC/UNF",
      application: "Provides tap drill hole diameter for extrusion taps, suitable for threading thin-walled parts, common in steel, stainless steel, and cast iron materials",
      pageNumber: "L52",
    },
    {
      id: "tap-056",
      name: "Threaded Tap Drill Hole Diameter Reference (Drill Bits) (American Threads UNC/UNF)",
      image: "/images/L53-1.png",
      description: "American thread tap drill hole diameter reference chart and drill bits",
      threadStandards: "UNC/UNF",
      application: "Provides drill hole diameter reference data for American (UNC/UNF) threads. D1 value represents the allowed upper/lower limits of the internal diameter after internal thread processing",
      pageNumber: "L53",
    },
    {
      id: "tap-057",
      name: "Threaded Tap Drill Hole Diameter Reference (Drill Bits) (British Whitworth Threads W)",
      image: "/images/L53-2.png",
      description: "British Whitworth thread tap drill hole diameter reference chart and drill bits",
      threadStandards: "W (Whitworth)",
      application: "Provides drill hole diameter reference data for British Whitworth (W) threads. D1 value represents the allowed upper/lower limits of the internal diameter after internal thread processing",
      pageNumber: "L53",
    },
    {
      id: "tap-058",
      name: "Threaded Tap Drill Hole Diameter Reference (Drill Bits) (British Parallel Pipe Threads PS (Rp))",
      image: "/images/L54-1.png",
      description: "British parallel pipe thread tap drill hole diameter reference chart and drill bits",
      threadStandards: "PS (Rp) - British Parallel Pipe",
      application: "Provides drill hole diameter reference data for British Parallel Pipe Threads. D1 value represents the allowed upper/lower limits of the internal diameter after internal thread processing",
      pageNumber: "L54",
    },
    {
      id: "tap-059",
      name: "Threaded Tap Drill Hole Diameter Reference (Drill Bits) (British Taper Pipe Threads PT (Rc))",
      image: "/images/L54-2.png",
      description: "British taper pipe thread tap drill hole diameter reference chart and drill bits",
      threadStandards: "PT (Rc) - British Taper Pipe",
      application: "Provides drill hole diameter reference data for British Taper Pipe Threads. D1 value represents the allowed upper/lower limits of the internal diameter after internal thread processing",
      pageNumber: "L54",
    },
  ]

  // Performance features
  const performanceFeatures = [
    {
      icon: "Shield",
      title: "Superior Chip Evacuation",
      description:
        "Spiral flute design provides excellent chip evacuation, pulling chips up and out of blind holes, preventing chip packing and ensuring high thread quality.",
    },
    {
      icon: "Zap",
      title: "Precision Thread Quality",
      description:
        "Advanced manufacturing processes and quality control ensure consistent, high-precision threads that meet all major international standards.",
    },
    {
      icon: "Target",
      title: "Multi-Material Performance",
      description:
        "Engineered for optimal performance across diverse materials from general steels to challenging titanium alloys and heat-resistant superalloys.",
    },
  ]



  // Industries served
  const industries = [
    "Automotive Manufacturing",
    "Aerospace Industry",
    "Medical Device Manufacturing",
    "Heavy Machinery",
    "Plumbing and HVAC",
    "Petrochemical Industry",
    "Fastener Manufacturing",
    "General Machine Shops",
  ]

  // Threading operations
  const threadingOperations = [
    "Internal Threading in Blind Holes",
    "Pipe Thread Creation",
    "External Thread Production",
    "Quality Control Inspection",
    "Thread Starting Operations",
    "Dryseal Thread Creation",
    "High-Volume Threading",
    "Precision Thread Verification",
  ]

  // Materials that can be threaded
  const threadableMaterials = [
    "General Steels",
    "Stainless Steels",
    "Cast Iron",
    "Titanium Alloys",
    "Aluminum and Copper Alloys",
    "Heat-Resistant Superalloys",
    "Tool Steels",
    "Non-Ferrous Metals",
  ]

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Thread Standards Support",
      description:
        "Comprehensive coverage of Metric (M), Unified (UNC/UNF), British Standard (BSW), and specialized pipe thread standards (NPT, BSPT, BSPP, NPTF).",
    },
    {
      title: "Advanced Materials & Coatings",
      description:
        "High-Speed Steel (HSS), Cobalt-enhanced HSS-E, and solid carbide substrates with TiN, TiCN, TiAlN coatings for enhanced performance.",
    },
    {
      title: "Precision Manufacturing",
      description:
        "Manufactured to extremely tight tolerances with optimized geometries for superior chip evacuation, thread quality, and tool life.",
    },
  ]

  // Specifications
  const specifications = [
    { label: "Type", value: "Threading Tools and Inspection Gauges" },
    { label: "Material", value: "HSS, HSS-E, Carbide, Tool Steel" },
    { label: "Coating Options", value: "TiN, TiCN, TiAlN, Uncoated" },
    { label: "Helix Angle", value: "15°-52° (Spiral Taps)" },
    { label: "Thread Standards", value: "Metric, Unified, BSW, Pipe Threads" },
    { label: "Chamfer Types", value: "Taper, Plug, Bottoming" },
    { label: "Size Range", value: "M1-M64, #0-80 to 4\"" },
    { label: "Tolerance Classes", value: "6H, 6G, Class X/XX" },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Threading Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  Industrial Taps - Professional Threading Solutions
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  Comprehensive threading tools meticulously designed to create internal (female) screw threads in workpieces. From spiral flute taps for blind holes to specialized pipe thread solutions, our precision-engineered tools ensure efficiency, accuracy, and reliability in all thread processing systems.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/millingcutter1.png"
                    alt="MZG Professional Industrial Taps System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">System Performance Analysis</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      Taps are fundamental cutting tools in the manufacturing and engineering sectors, meticulously designed to create internal (female) screw threads in a workpiece. This process, known as tapping, can be performed on a variety of materials and is crucial for assembling components with bolts or screws. The precision, material, and design of a tap determine its performance, lifespan, and the quality of the threads it produces.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      Spiral Flute Taps (SP Type) are engineered for creating threads in blind holes. Their defining feature is the helical flute, which actively draws chips upward and out of the hole during cutting. This upward chip evacuation prevents chips from packing at the bottom of the hole, which could otherwise lead to tap breakage and poor thread quality. They excel in processing materials like general steel, durable stainless steel, and cast iron.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      Straight Flute with Spiral Point Taps (PO Type / "Gun-Nose") are the ideal choice for threading through holes. They feature a straight flute combined with an angular cutting edge at the point. This geometry pushes chips forward and ahead of the tap as it cuts, ensuring a clean evacuation path through the open end of the hole.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      Forming Taps (Extrusion / NRT Type) create threads through a chipless cold-forming process. Instead of cutting material, they displace and form it into the shape of a thread. This method produces stronger threads due to the cold working of the material grain, ideal for thin-walled parts and ductile materials.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3 text-sm text-gray-700">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Material: High-Speed Steel (HSS), HSS-E, Carbide with enhanced durability and cutting performance</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Thread Types: Spiral Flute, Straight Flute, Forming for different hole configurations and material requirements</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Thread Angle: 60° (Metric/American), 55° (British) ensuring universal compatibility and precision</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Size Range: M1.0 to M48, UNC/UNF, Whitworth series covering comprehensive threading applications</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Coating: TIN (Titanium Nitride) for enhanced wear resistance and extended tool life in demanding applications</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {products.map((product) => (
              <div
                key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
              >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                  <Image
                    src={product.image || "/placeholder.svg"}
                    alt={product.name}
                    fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                  />
                </div>
                                    <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.threadStandards && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Standards:</span>
                          <span className="text-gray-900">{product.threadStandards}</span>
                    </div>
                      )}
                      {product.application && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.application}</p>
                    </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
                        </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultTapsImages[0]}
                    alt="Taps Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                  </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultTapsImages[imageIndex % defaultTapsImages.length]
                  : defaultTapsImages[index % defaultTapsImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Taps Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                  </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Specifications</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Thread Standards Support":
                      return <Layers className="h-6 w-6 text-blue-600 mr-3" />
                    case "Advanced Materials & Coatings":
                      return <Shield className="h-6 w-6 text-green-600 mr-3" />
                    case "Precision Manufacturing":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                    </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios & Processing</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Automotive Manufacturing:</strong> Engine blocks, transmission cases, and critical threaded fasteners</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Aerospace Industry:</strong> Aircraft components requiring precision threads and high reliability</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Plumbing & HVAC:</strong> Pipe thread connections for fluid and gas systems</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Medical Device Manufacturing:</strong> Precision components requiring biocompatible materials</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Heavy Machinery:</strong> Large-scale industrial equipment with demanding thread requirements</span>
                  </li>
                </ul>
              </div>

              {/* Threading Operations */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Threading Operations
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Internal Threading:</strong> Blind holes and through holes with optimal chip evacuation</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Pipe Thread Creation:</strong> Taper and parallel threads for pressure-tight sealing</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Precision Threading:</strong> High-accuracy threads meeting international standards</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Quality Control:</strong> Thread verification and inspection with precision gauges</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Volume Production:</strong> Efficient threading for mass manufacturing applications</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Primary Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Spiral Chip Evacuation:</strong> Helical flutes guide chips upward and away from the cutting zone, preventing chip packing and ensuring superior thread quality</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Pressure-Tight Sealing:</strong> Specialized pipe threads create reliable seals through thread deformation, ensuring leak-proof connections</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Precision Quality Control:</strong> Go/No-Go gauge sets provide quick, accurate verification of thread dimensions and interchangeability</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  Performance Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Volume Production:</strong> Self-opening die chaser systems enable rapid external thread production with automatic retraction for maximum efficiency</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Thread Starting Alignment:</strong> Extended taper chamfers distribute cutting action over multiple teeth, reducing torque and ensuring proper alignment</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Dryseal Technology:</strong> Metal-to-metal sealing through precise thread form control eliminates the need for chemical sealants</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/threading/taps" />
          </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional Threading Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal threading tools for specific applications, materials, and production requirements. From spiral flute taps to precision gauges, we provide comprehensive threading solutions for all industrial needs.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
              {(() => {
                // 从本分类产品中随机获取图片的函数
                const getRandomProductImage = () => {
                  const randomIndex = Math.floor(Math.random() * products.length);
                  return products[randomIndex].image;
                };
                
                // 定义同目录下的所有分类
                const allThreadingCategories = [
                  {
                    title: "Inserts Type Thread Milling Cutter",
                    image: getRandomProductImage(),
                    description: "可转位螺纹铣刀，用于各种螺纹加工",
                    url: "/standard-tools/threading/inserts-type-thread-milling-cutter",
                  },
                  {
                    title: "Integral Thread Milling Cutters",
                    image: getRandomProductImage(),
                    description: "整体式螺纹铣刀，高精度螺纹加工",
                    url: "/standard-tools/threading/integral-thread-milling-cutters",
                  },
                  {
                    title: "Thread Milling Cutters",
                    image: getRandomProductImage(),
                    description: "通用螺纹铣刀，适用于多种材料",
                    url: "/standard-tools/threading/thread-milling-cutters",
                  },
                  {
                    title: "Thread Mills",
                    image: getRandomProductImage(),
                    description: "CNC螺纹铣刀，内外螺纹加工",
                    url: "/standard-tools/threading/thread-mills",
                  },
                  {
                    title: "Thread Turning",
                    image: getRandomProductImage(),
                    description: "车床螺纹加工工具",
                    url: "/standard-tools/threading/thread-turning",
                  },
                  {
                    title: "Thread Whirling",
                    image: getRandomProductImage(),
                    description: "螺纹旋风铣削工具",
                    url: "/standard-tools/threading/thread-whirling",
                  },
                ];
                
                // 随机选择最多5个分类
                const shuffled = [...allThreadingCategories].sort(() => 0.5 - Math.random());
                const selectedCategories = shuffled.slice(0, 5);
                
                return selectedCategories.map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Threading Tools" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
}