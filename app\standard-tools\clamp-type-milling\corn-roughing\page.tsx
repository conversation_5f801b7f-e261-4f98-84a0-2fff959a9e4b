"use client"

import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function CornRoughingPage() {
  // Corn Roughing相关的默认图片
  const defaultCornRoughingImages = [
    "/images/D70-1.png",
    "/images/D70-2.png",
    "/images/D71-1.png",
    "/images/D72-2.png",
    "/images/D72-1.png",
    "/images/corn-roughing-small.png",
    "/images/corn-roughing-medium.png",
    "/images/corn-roughing-large.png",
    "/images/high-efficiency-corn.png",
    "/images/deep-cavity-corn.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);
  
  // State to track hydration completion
  const [isHydrated, setIsHydrated] = useState(false);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/clamp-type-milling/corn-roughing");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认图片
          setGalleryImages(defaultCornRoughingImages);
        }
      } else {
        // API请求失败，使用默认图片
        setGalleryImages(defaultCornRoughingImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认图片
      setGalleryImages(defaultCornRoughingImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Hydration effect
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认图片，避免显示无关图片
    setGalleryImages(defaultCornRoughingImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data based on the MZG Clamp Type Corn / Roughing Milling Cutters system (保持原有产品数据)
  const products = [
    {
      id: "cr-001",
      name: "AP Integral Type Maize Rough Milling Cutter",
      image: "/images/D70-1.png",
      description: "Deep hole processing, rough milling",
      series: "AP Series",
      insertType: "APMT1604..",
      application: "Deep hole processing, rough milling",
      pageNumber: "D70",
    },
    {
      id: "cr-002",
      name: "APC Corn Type Rough Milling Cutter Arbor",
      image: "/images/D70-2.png",
      description: "High-efficiency rough milling cutter capable of deep hole machining. Size can be arbitrarily changed by arbor depth to maximize performance.",
      series: "APC Series",
      insertType: "AP..1135..",
      application: "High-efficiency rough milling, deep hole machining",
      pageNumber: "D70",
    },
    {
      id: "cr-003",
      name: "SPM Corn End Mill",
      image: "/images/D71-1.png",
      description: "High-efficiency rough milling cutter capable of deep hole machining. Size can be arbitrarily changed by arbor depth to maximize performance.",
      series: "SPM Series",
      insertType: "SDMT090308",
      application: "High-efficiency rough milling, deep hole machining",
      pageNumber: "D71",
    },
    {
      id: "cr-004",
      name: "SPC Corn Type Rough Milling Cutter",
      image: "/images/D72-2.png",
      description: "High-efficiency rough milling cutter capable of deep hole machining. Size can be arbitrarily changed by arbor depth to maximize performance.",
      series: "SPC Series",
      insertType: "SCMT09T308",
      application: "High-efficiency rough milling, deep hole machining",
      pageNumber: "D71",
    },
    {
      id: "cr-005",
      name: "HMP01 Integral Type Maize Rough Milling Cutter",
      image: "/images/D72-1.png",
      description: "Integral structure with better cutting rigidity! Multiple cutting depth lengths for flexible selection.",
      series: "HMP01 Series",
      insertType: "LPMT1504.. SPMT1204..",
      application: "Integral structure cutting, flexible depth selection",
      pageNumber: "D72",
    },
  ]

  // Performance features
  const performanceFeatures = [
    {
      icon: "Shield",
      title: "Extreme Material Removal & Deep Pocketing Performance",
      description: "AP, APC, SPM, SPC, and HMP01 series deliver voracious appetite for material removal. Stacked insert design allows extremely deep axial depths of cut (Ap), making these cutters ultimate solution for deep hole processing and cavity roughing with ability to machine deep walls in single pass.",
    },
    {
      icon: "Zap", 
      title: "Superior Rigidity & Process Security",
      description: "AP and HMP01 series featuring integral structure (single-piece shank and body) offer exceptional cutting rigidity. This robust, monolithic design effectively dampens vibration, critical when machining with long tool overhangs, ensuring process security and predictable tool life.",
    },
    {
      icon: "Target",
      title: "Modular Flexibility & Cost-Effective Configuration",
      description: "System's adaptability allows users to pair cutter shanks or heads with different length arbors to achieve required reach. HMP01 exemplifies this with detachable head design, allowing different cutter heads to be mounted on single shank body for maximum efficiency.",
    },
  ]

  // Helper function to render icons
  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case "Shield":
        return <Shield className="h-8 w-8 text-red-600" />
      case "Zap":
        return <Zap className="h-8 w-8 text-red-600" />
      case "Target":
        return <Target className="h-8 w-8 text-red-600" />
      default:
        return <Tool className="h-8 w-8 text-red-600" />
    }
  }

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Cutter Body Configurations",
      description: "Integral Shank Arbor (AP, SPM, HMP01) with single-piece design for maximum rigidity, reaching up to 250mm overall lengths. Arbor-Mounted Disc (APC, SPC) for larger diameters up to 100mm. Modular Detachable Head System (HMP01) offering blend of rigidity and modularity with secure mounting system.",
    },
    {
      title: "Insert Systems & Arrangement",
      description: "APMT/APKT/AKPT inserts for general-purpose steel and cast iron machining with 11° relief angle. SPMT/SDMT/LPMT square inserts for strong cutting edge and versatility. Inserts mounted in staggered, helical pattern with single D80 AP cutter holding 36 inserts for distributed cutting load.",
    },
    {
      title: "Secure Clamping & Technical Range",
      description: "Each insert individually secured with high-strength Torx screws (M4x10 or M5x13) and corresponding wrenches (T15 or T20). Effective cutting lengths up to 165mm with robust technical specifications ensuring inserts remain firmly seated despite extreme cutting forces.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Professional Roughing Tool Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG Corn Roughing Milling Cutters
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  This tooling system represents the pinnacle of efficiency for heavy-duty roughing operations. The "corn cutter" concept is a masterful piece of engineering designed for one primary purpose: to achieve the highest possible Metal Removal Rates (MRR). By arranging multiple, standard indexable inserts in a helical pattern along a robust cutter body, we create a tool with an exceptionally long axial cutting edge. This allows operators to take deep side-milling passes, rapidly evacuating material from deep pockets, cavities, and component sides. This system is a highly economical and performant alternative to long, expensive solid carbide end mills, offering superior rigidity and flexibility.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/D-CRM-PG05.jpg"
                    alt="MZG Professional Corn Roughing Milling Cutters System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
              <div
                key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
              </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Performance</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of our Corn Milling Cutter system is best understood by its targeted capabilities, each designed to provide a distinct advantage in high-demand production environments.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Extreme Material Removal & Deep Pocketing Performance:</strong> The core performance characteristic of the entire portfolio—<strong>AP, APC, SPM, SPC, and HMP01 series</strong>—is its voracious appetite for material removal. The stacked insert design allows for extremely deep axial depths of cut (Ap), making these cutters the ultimate solution for <strong>deep hole processing</strong> and cavity roughing. For large-scale operations, the arbor-mounted <strong>APC and SPC disc types</strong> provide a wide cutting diameter and a high number of inserts, maximizing MRR on powerful horizontal or vertical milling centers.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Superior Rigidity & Process Security:</strong> Heavy roughing generates immense cutting forces. Our system is engineered for uncompromising stability. The <strong>AP and HMP01 series</strong>, featuring an <strong>integral structure</strong> (single-piece shank and body), offer exceptional cutting rigidity. This robust, monolithic design effectively dampens vibration, which is critical when machining with long tool overhangs. The <strong>HMP01</strong> further enhances this with its advanced design, ensuring process security and predictable tool life even under the most aggressive cutting parameters.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Modular Flexibility & Cost-Effective Configuration:</strong> A key performance advantage is the system's adaptability. The principle that "size can be arbitrarily changed by the arbor depth" highlights the modularity. Instead of stocking numerous expensive, fixed-length solid tools, our system allows users to pair cutter shanks or heads with different length arbors or extensions to achieve the required reach. The <strong>HMP01</strong> exemplifies this with its <strong>detachable head</strong> design, allowing different cutter heads to be mounted on a single shank body.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3 text-sm text-gray-700">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Material Removal Rate:</strong> Maximum MRR through multiple insert configuration</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Cutting Length:</strong> Up to 165mm effective cutting length</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Insert Capacity:</strong> D80 cutter holds 36 inserts for distributed load</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Rigidity:</strong> Integral structure design for maximum stability</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Modularity:</strong> Detachable head system for flexibility</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {!isHydrated ? (
                // Server-side rendering placeholder
                <div className="col-span-full flex justify-center items-center h-64">
                  <div className="animate-pulse text-gray-400">Loading products...</div>
                </div>
              ) : (
                products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                    <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                        <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                      <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                            <span className="text-gray-900 text-right">{product.series}</span>
                        </div>
                      )}
                        {(product as any).diameter && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Diameter:</span>
                            <span className="text-gray-900 text-right">{(product as any).diameter}</span>
                        </div>
                      )}
                        {(product as any).shankType && (
                        <div className="flex justify-between">
                            <span className="font-medium text-gray-700">Shank:</span>
                            <span className="text-gray-900 text-right">{(product as any).shankType}</span>
                        </div>
                      )}
                        {(product as any).insertType && (
                        <div className="flex justify-between">
                            <span className="font-medium text-gray-700">Insert:</span>
                            <span className="text-gray-900 text-right">{(product as any).insertType}</span>
                        </div>
                      )}
                        {(product as any).coating && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Coating:</span>
                            <span className="text-gray-900 text-right">{(product as any).coating}</span>
                        </div>
                      )}
                        {(product as any).features && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Features:</span>
                            <span className="text-gray-900 text-right">{(product as any).features}</span>
                        </div>
                      )}
                        {product.description && (
                        <div className="pt-2 border-t border-gray-100">
                            <p className="text-xs text-gray-600">{product.description}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                ))
              )}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultCornRoughingImages[0]}
                    alt="Corn Roughing Milling Cutters Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                    </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultCornRoughingImages[imageIndex % defaultCornRoughingImages.length]
                  : defaultCornRoughingImages[index % defaultCornRoughingImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Corn Roughing Milling Cutters Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                    </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Cutter Body Configurations":
                      return <Layers className="h-6 w-6 text-blue-600 mr-3" />
                    case "Insert Systems & Arrangement":
                      return <Shield className="h-6 w-6 text-green-600 mr-3" />
                    case "Secure Clamping & Technical Range":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                    </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios & Processing</h2>
            </div>
              <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Heavy-Duty Roughing & Slotting:</strong> Primary application is "hogging," rapidly removing large volumes of material from raw stock to get it to near-net shape</span>
                  </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Deep Cavity/Pocket Milling:</strong> Ideal for mold & die, aerospace, and heavy equipment industries where deep pockets must be roughed out from solid blocks</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Full-Width Slotting:</strong> Using cutter's diameter to machine wide slot in single, deep pass for maximum efficiency</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Efficiency Side Milling:</strong> Using long effective cutting length to machine tall vertical walls, dramatically reducing required passes</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Block Squaring and Stock Preparation:</strong> Quickly machining raw billets or forgings to create square, parallel reference surfaces</span>
                    </li>
                  </ul>
                </div>

              {/* Main Functions */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Main Functions
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Maximize Metal Removal Rate (MRR):</strong> Fundamental purpose is to remove maximum volume of material in shortest possible time through multiple insert configuration</span>
                  </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Enable Deep Axial Machining:</strong> Provide effective and stable solution for machining deep pockets, cavities, and side walls inaccessible to standard-length cutters</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Highly Economical Roughing Solution:</strong> Drastically reduce tooling costs by utilizing inexpensive, multi-use indexable inserts instead of expensive solid carbide tools</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Ensure High Process Security:</strong> Offer rigid and stable cutting platform that minimizes vibration and ensures predictable, reliable performance under extreme cutting loads</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Offer Configurability for Various Machining Depths:</strong> Provide flexible, modular system adaptable to different reach and depth requirements, reducing specialized tool inventory</span>
                    </li>
                  </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/clamp-type-milling/corn-roughing" />
            </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need High-Efficiency Roughing Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal corn roughing cutters for maximum material removal rates and deep cavity machining. From integral shank arbors to modular detachable head systems, we provide comprehensive cutting solutions for heavy-duty roughing across all materials and applications.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same clamp-type-milling directory
                const allClampTypeCategories = [
                  {
                    title: "Face Milling Cutters",
                    image: "/images/D04-1.png",
                    description: "High-performance indexable face milling solutions",
                    url: "/standard-tools/clamp-type-milling/face-milling-cutters",
                  },
                  {
                    title: "Right Angle Face Milling Cutters",
                    image: "/images/D03-1.png",
                    description: "Precise 90-degree shoulder machining solutions",
                    url: "/standard-tools/clamp-type-milling/right-angle-square-shoulder",
                  },
                  {
                    title: "Ball End Milling Cutters",
                    image: "/images/D46-1.png",
                    description: "3D contouring and surface finishing solutions",
                    url: "/standard-tools/clamp-type-milling/ball-end-milling-cutters",
                  },
                  {
                    title: "High Feed Milling Cutter",
                    image: "/images/D07-2.png",
                    description: "Maximum productivity milling solutions",
                  url: "/standard-tools/clamp-type-milling/high-feed-milling-cutter",
                },
                {
                    title: "Chamfering Cutters",
                    image: "/images/D55-1.png",
                    description: "Precision chamfering and edge breaking solutions",
                    url: "/standard-tools/clamp-type-milling/chamfering-cutters",
                  },
                ];
                
                return allClampTypeCategories.map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Clamp Type Milling" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
}