"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Users, Award, Globe, Building, Shield, Target, Zap, Factory, Clock, CheckCircle, PhoneCall } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import FAQSectionEn from "@/components/faq-section-en"
import ConsultationForm from "@/components/consultation-form"
import { useState, useEffect } from "react"

export default function AboutPage() {
  // Default company images
  const defaultCompanyImages = [
    "/images/company-exterior.jpg",
    "/images/manufacturing-floor.jpg", 
    "/images/quality-control.jpg",
    "/images/research-development.jpg",
    "/images/team-meeting.jpg",
    "/images/warehouse.jpg"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/about");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API returned success but no images, use default company images
          setGalleryImages(defaultCompanyImages);
        }
      } else {
        // API request failed, use default company images
        setGalleryImages(defaultCompanyImages);
      }
    } catch (error) {
      console.error("Failed to load images:", error);
      // Network error or other exceptions, use default company images
      setGalleryImages(defaultCompanyImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // First set default company images to avoid displaying unrelated images
    setGalleryImages(defaultCompanyImages);
    setIsLoadingImages(false);
    
    // Then asynchronously load API images
    loadGalleryImages();
  }, []);

  // Separate useEffect to handle image carousel
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // Rotate every 20 seconds

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Company values and capabilities
  const companyFeatures = [
    {
      icon: "Shield",
      title: "Quality Excellence",
      description:
        "MZG Tool Machine Company is committed to delivering the highest quality industrial milling tools. With over two decades of experience in precision manufacturing, we maintain rigorous quality control standards throughout our entire production process. Our ISO 9001 certified facility ensures every tool meets international standards for precision, durability, and performance.",
    },
    {
      icon: "Target",
      title: "Innovation & Technology",
      description:
        "We invest heavily in research and development, utilizing the latest manufacturing technologies including advanced CNC machining centers, state-of-the-art coating systems, and precision measuring equipment. Our engineering team continuously develops innovative solutions to meet the evolving demands of modern manufacturing industries.",
    },
    {
      icon: "Zap",
      title: "Customer-Centric Service",
      description:
        "Our success is built on understanding and exceeding customer expectations. We provide comprehensive technical support, rapid delivery times, and custom solutions tailored to specific machining challenges. Our experienced engineers work closely with customers to optimize their manufacturing processes and achieve superior results.",
    },
  ]

  // Company statistics
  const companyStats = [
    { number: "20+", label: "Years of Experience", icon: Clock },
    { number: "5000+", label: "Satisfied Customers", icon: Users },
    { number: "50+", label: "Countries Served", icon: Globe },
    { number: "100%", label: "Quality Guaranteed", icon: CheckCircle },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  About MZG Tools
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  Leading Manufacturer of Precision Industrial Milling Tools
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  MZG Tool Machine Company has been at the forefront of precision tooling innovation for over 20 years. Founded with a vision to revolutionize industrial manufacturing through superior cutting tools, we have grown from a small workshop to an internationally recognized manufacturer serving customers in over 50 countries. Our commitment to quality, innovation, and customer service has made us a trusted partner for manufacturers across automotive, aerospace, medical device, and general machining industries.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                    onClick={() => {
                      document.getElementById("about-consultation")?.scrollIntoView({ behavior: "smooth" });
                    }}
                  >
                    Contact Us
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/company-hero.jpg"
                    alt="MZG Tool Machine Company Facility"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
        
          {/* Company Values - Single Container */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Values & Capabilities</h2>
            </div>
            
            {/* Performance Features */}
            <div className="container mx-auto px-4 py-5">
              <div className="grid md:grid-cols-3 gap-8">
                {companyFeatures.map((feature, index) => {
                  const getIcon = (iconName: string) => {
                    switch (iconName) {
                      case "Shield":
                        return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                      case "Target":
                        return <Target className="h-6 w-6 text-green-600 mr-3" />
                      case "Zap":
                        return <Zap className="h-6 w-6 text-purple-600 mr-3" />
                      default:
                        return <Building className="h-6 w-6 text-gray-600 mr-3" />
                    }
                  }
                  
                  return (
                    <div
                      key={index}
                      className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                    >
                      <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                        {getIcon(feature.icon)}
                        {feature.title}
                      </h3>
                      <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                    </div>
                  )
                })}
              </div>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-100 text-sm">
              <p className="mb-4 font-normal">
                Since our establishment, MZG Tool Machine Company has been dedicated to pushing the boundaries of what's possible in precision manufacturing. Our journey began with a simple mission: to create cutting tools that not only meet but exceed the demanding requirements of modern industrial applications. Today, we stand as a testament to the power of innovation, quality craftsmanship, and unwavering commitment to customer satisfaction.
              </p>
              <p className="mb-4 font-normal">
                Our state-of-the-art manufacturing facility spans over 50,000 square feet and houses the most advanced equipment in the industry. From precision grinding machines capable of achieving tolerances within microns to sophisticated coating systems that enhance tool life by up to 300%, every aspect of our operation is designed to deliver exceptional results. We maintain complete control over our production process, from raw material selection to final inspection, ensuring consistent quality in every tool we produce.
              </p>
              <p className="mb-4 font-normal">
                What sets MZG apart is our team of highly skilled engineers and technicians who bring decades of experience in tool design and manufacturing. Our research and development department works continuously to develop new cutting-edge solutions, often in collaboration with leading universities and research institutions. This commitment to innovation has resulted in numerous patents and industry-first technologies that have revolutionized manufacturing processes across various sectors.
              </p>
              <p className="font-normal">
                We understand that every customer's needs are unique, which is why we offer comprehensive custom tooling services alongside our extensive standard product line. Our customer service philosophy centers on building long-term partnerships rather than simple transactions. When you choose MZG Tool Machine Company, you're not just purchasing tools – you're gaining a partner committed to your success, providing ongoing technical support, training, and consultation to help you achieve maximum efficiency and profitability in your operations.
              </p>
            </div>
          </div>

          {/* Company Statistics */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Achievements</h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {companyStats.map((stat, index) => {
                const IconComponent = stat.icon;
                return (
                  <div key={index} className="text-center bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                    <IconComponent className="h-8 w-8 text-red-600 mx-auto mb-4" />
                    <div className="text-3xl font-bold text-gray-900 mb-2">{stat.number}</div>
                    <div className="text-sm text-gray-600">{stat.label}</div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Company Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Facility</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - Main carousel image */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultCompanyImages[0]}
                    alt="MZG Tool Machine Company Facility"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                  </div>
                )}
              </div>

              {/* Middle section and Right section - Small image grid */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultCompanyImages[imageIndex % defaultCompanyImages.length]
                  : defaultCompanyImages[index % defaultCompanyImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`MZG Company Facility ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                  </div>
                );
              })}
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-12">
            <FAQSectionEn pageUrl="/about" />
          </div>

          {/* Contact Form */}
          <div id="about-consultation" className="mb-12 bg-gray-50 p-8 rounded-lg">
            <ConsultationForm 
              source="about-page"
              title="Get in Touch with MZG Tools"
              productInfo={{
                pagePath: "/about",
                productName: "General Inquiry"
              }}
            />
          </div>
        </div>
      </div>
      
      <Footer />
    </>
  )
} 