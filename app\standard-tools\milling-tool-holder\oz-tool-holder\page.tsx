"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function OZToolHolderPage() {
  // OZ Tool Holder相关的默认图片
  const defaultOZImages = [
    "/images/c53-1.png",
    "/images/c54-1.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/milling-tool-holder/oz-tool-holder");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认OZ图片
          setGalleryImages(defaultOZImages);
        }
      } else {
        // API请求失败，使用默认OZ图片
        setGalleryImages(defaultOZImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认OZ图片
      setGalleryImages(defaultOZImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认OZ图片，避免显示无关图片
    setGalleryImages(defaultOZImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data based on the OZ Tool Holder system
  const products = [
    {
      id: "oz-004",
      name: "OZ COLLET CHUCK",
      image: "/images/c53-1.png",
      description: "A specialized tool holder designed for heavy cutting applications.",
      series: "OZ Series",
      application: "Heavy cutting applications requiring superior clamping integrity and durability",
      pageNumber: "C53",
    },
    {
      id: "oz-005",
      name: "OZ Collets Chuck Kit",
      image: "/images/c54-1.png",
      description: "A comprehensive set of OZ collet chucks, intended for heavy cutting tasks.",
      series: "OZ Kit Series",
      application: "Comprehensive heavy cutting solutions with multiple collet options",
      pageNumber: "C54",
    },
  ]

  // Performance features based on the provided content
  const performanceFeatures = [
    {
      icon: "Shield",
      title: "Exceptional Clamping Force",
      description: "Core performance is ability to generate tremendous clamping force. OZ collet combined with heavy-duty clamping nut provides powerful, full-contact grip essential for preventing tool slippage during heavy-duty cutting.",
    },
    {
      icon: "Target",
      title: "Outstanding Rigidity and Vibration Damping",
      description: "Immense clamping force creates incredibly rigid assembly between tool, collet, and holder. High rigidity effectively resists tool deflection and dampens vibrations, enabling aggressive depths of cut and higher feed rates.",
    },
    {
      icon: "Zap",
      title: "Robust and Durable Construction",
      description: "Built to endure harsh conditions of heavy machining. Solid construction ensures absorption of high torque and radial loads associated with roughing without compromising integrity, translating to long service life.",
    },
  ]

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Clamping System & Collet Series",
      description: "Utilizes robust OZ collet system specifically designed for heavy-duty cutting applications. OZ25: Clamps diameters from 3mm up to 25mm. OZ32: Clamps diameters from 6mm up to 32mm. Comprehensive kits available: OZ25 Kit (7-piece or 15-piece set), OZ32 Kit (8-piece or 15-piece set).",
    },
    {
      title: "Shank Interface Types & Compatibility",
      description: "Available in comprehensive range to fit vast array of machines: BT-OZ (BT30, BT40, BT50), NT-OZ (NT30, NT40, NT50), SK-OZ (SK40, SK50), CAT-OZ (CAT40, CAT50), MTB-OZ (Morse Taper: MTB2, MTB3, MTB4, MTB5), R8-OZ for broad machine compatibility.",
    },
    {
      title: "Critical Ordering Information",
      description: "When ordering holders that require pull stud (e.g., NT, MT), essential to confirm required pull rod thread specification (metric or imperial) to ensure compatibility with machine's drawbar. System bridges gap between new and legacy equipment.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  OZ Tool Holder Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG OZ Tool Holder System
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  MZG provide a comprehensive and detailed introduction to the OZ Tool Holder system. Explicitly designated as a heavy-duty cutting tool holder, the OZ system is the quintessential choice for machining applications that demand immense clamping force and unwavering stability. It is a robust, collet-based system engineered to withstand the significant stresses of aggressive roughing and high-torque milling operations.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/millingcutter1.png"
                    alt="MZG Professional OZ Tool Holder System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">System Performance Analysis</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of an OZ Tool Holder is defined by its raw power, exceptional rigidity, and durable construction, making it a reliable workhorse in demanding environments. The core of the OZ system's performance is its ability to generate tremendous clamping force.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The OZ collet, combined with a heavy-duty clamping nut, provides a powerful, full-contact grip on the tool shank. This secure lock is essential for preventing tool slippage or pull-out during heavy-duty cutting, deep slotting, and operations using large-diameter cutters, where cutting forces are at their peak.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The immense clamping force creates an incredibly rigid assembly between the tool, collet, and holder. This high level of rigidity effectively resists tool deflection and dampens vibrations at the source, critical for enabling aggressive depths of cut and higher feed rates.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      OZ tool holders are built to endure the harsh conditions of heavy machining. Their solid construction ensures they can absorb the high torque and radial loads associated with roughing without compromising their integrity, providing broad machine compatibility across extensive range of machine taper interfaces.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Clamping Force: Tremendous heavy-duty grip</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Collet Range: OZ25 (3-25mm), OZ32 (6-32mm)</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Interface Types: BT, NT, SK, CAT, MTB, R8</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Kit Options: 7-piece, 8-piece, 15-piece sets</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Application: Heavy-duty roughing & high-torque milling</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                          <span className="text-gray-900 text-right">{product.series}</span>
                        </div>
                      )}
                      {product.application && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.application}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultOZImages[0]}
                    alt="OZ Tool Holder Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                  </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultOZImages[imageIndex % defaultOZImages.length]
                  : defaultOZImages[index % defaultOZImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`OZ Tool Holder Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                  </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Clamping System & Collet Series":
                      return <Target className="h-6 w-6 text-blue-600 mr-3" />
                    case "Shank Interface Types & Compatibility":
                      return <Zap className="h-6 w-6 text-green-600 mr-3" />
                    case "Critical Ordering Information":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                    </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                    </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios &amp; Applicable Machining</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Heavy-Duty Roughing:</strong> Primary application excelling at aggressively milling large volumes of material from steel, cast iron, and other tough alloys with maximum efficiency</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Torque Milling:</strong> Ideal for using large-diameter end mills, shell mills, and face mills where significant rotational force and slip-proof grip are required</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Conventional and Manual Milling:</strong> Availability of R8, Morse Taper (MT), and NT shanks makes OZ system perfect performance upgrade for Bridgeport-style mills and conventional machines</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Drilling and Boring:</strong> Strong clamping force makes it suitable for holding large drills and boring bars securely, preventing rotation within collet during high-torque hole-making operations</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>General Fabrication and Job Shops:</strong> Excellent choice for workshops requiring versatile, strong, and reliable tool holding system that can handle wide spectrum of demanding jobs without failure</span>
                  </li>
                </ul>
              </div>

              {/* Machining Operations */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Main Functions
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Deliver Maximum Clamping Force:</strong> Most critical function to generate and maintain unwavering grip on tool shank, ensuring absolute security and preventing slippage during most aggressive cuts</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Robust and Stable Platform:</strong> Functions to create rock-solid connection that minimizes tool deflection and dampens vibration, enabling higher productivity and extending tool life in roughing applications</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Ensure Broad Machine Compatibility:</strong> Designed to equip vast range of machinery, from latest CNCs to legacy manual mills, with high-performance, heavy-duty clamping capability</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Offer Comprehensive Heavy-Duty Solution:</strong> Through complete collet kits, system functions to provide workshops with ready-to-use, versatile package for all heavy-duty milling and drilling needs</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Power-Intensive Operations:</strong> Go-to solution for power-intensive machining operations where stock removal and stability are more critical than ultra-high speed</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Primary Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Deliver Maximum Clamping Force:</strong> Most critical function to generate and maintain unwavering grip on tool shank, ensuring absolute security and preventing slippage during most aggressive cuts</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Robust and Stable Platform for Aggressive Cutting:</strong> Functions to create rock-solid connection that minimizes tool deflection and dampens vibration</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Ensure Broad Machine Compatibility:</strong> Designed to equip vast range of machinery from latest CNCs to legacy manual mills with high-performance heavy-duty clamping capability</span>
                  </li>
                </ul>
                  </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  Performance Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Offer Comprehensive Heavy-Duty Solution:</strong> Through complete collet kits, system functions to provide workshops with ready-to-use, versatile package for all heavy-duty milling and drilling needs</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Power-Intensive Machining Excellence:</strong> Go-to solution for power-intensive machining operations where stock removal and stability are more critical than ultra-high speed</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Superior Material Removal Rates:</strong> Enables higher productivity and extending tool life in roughing applications through exceptional rigidity and vibration damping</span>
                  </li>
                </ul>
                </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/milling-tool-holder/oz-tool-holder" />
            </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional OZ Tool Holder Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal OZ tool holders for heavy-duty cutting and high-torque milling applications. From maximum clamping force to broad machine compatibility, we provide the most robust and reliable heavy-duty solutions.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same milling-tool-holder directory
                const allToolHolderCategories = [
                  {
                    title: "SK High-Speed Tool Holders",
                    image: "/images/C05-1.png",
                    description: "High-speed precision tool holders",
                  url: "/standard-tools/milling-tool-holder/sk-high-speed",
                },
                {
                    title: "HM Hydraulic Tool Holders",
                    image: "/images/C09-1.png",
                    description: "Precision hydraulic clamping systems",
                  url: "/standard-tools/milling-tool-holder/hm-hydraulic",
                },
                  {
                    title: "SR Shrink Fit Tool Holders",
                    image: "/images/C13-1.png",
                    description: "Thermal expansion precision clamping",
                    url: "/standard-tools/milling-tool-holder/sr-shrink-fit",
                  },
                {
                  title: "ER Tool Holders",
                    image: "/images/c41-1.png",
                    description: "Versatile collet chuck systems",
                    url: "/standard-tools/milling-tool-holder/er-tool-holder",
                  },
                  {
                    title: "Power Tool Holders",
                    image: "/images/c34-1.png",
                    description: "Heavy-duty strong clamping systems",
                    url: "/standard-tools/milling-tool-holder/power-tool-holder",
                  },
                ];
                
                return allToolHolderCategories.slice(0, 5).map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Tool Holders" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
} 