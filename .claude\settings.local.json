{"permissions": {"allow": ["Bash(node -e \"const { Pool } = require(''@neondatabase/serverless''); const pool = new Pool({ connectionString: process.env.DATABASE_URL }); pool.query(''SELECT NOW()'').then(r => console.log(''✅ 数据库连接正常:'', r.rows[0])).catch(e => console.log(''❌ 数据库连接失败:'', e.message)).finally(() => pool.end())\")", "Bash(npm run build)", "<PERSON><PERSON>(tail:*)", "Bash(grep:*)", "Bash(npm run lint:*)", "Bash(node:*)", "mcp__ide__getDiagnostics"], "deny": []}}