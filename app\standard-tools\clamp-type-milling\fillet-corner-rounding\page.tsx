"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function FilletCornerRoundingPage() {
  // Fillet Corner Rounding相关的默认图片
  const defaultFilletCornerImages = [
    "/images/D34-1.png",
    "/images/D35-1.png", 
    "/images/D36-1.png",
    "/images/D38-1.png",
    "/images/D38-2.png",
    "/images/D40-1.png",
    "/images/D41-1.png",
    "/images/D42-1.png",
    "/images/D42-2.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/clamp-type-milling/fillet-corner-rounding");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认图片
          setGalleryImages(defaultFilletCornerImages);
        }
      } else {
        // API请求失败，使用默认图片
        setGalleryImages(defaultFilletCornerImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认图片
      setGalleryImages(defaultFilletCornerImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认图片，避免显示无关图片
    setGalleryImages(defaultFilletCornerImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data (保持原有产品内容)
  const products = [
    {
      id: "fillet-001",
      name: "EMRW Round Nose Milling Cutter",
      image: "/images/D34-1.png",
      description: "High-strength round insert suitable for mold machining",
      insertType: "RPMT1204.., RPMW1204..",
      application: "圆刀片强度高，适合模具加工",
      page: "D34",
      url: "/standard-tools/clamp-type-milling/fillet-corner-rounding/EMRW-milling",
    },
    {
      id: "fillet-002",
      name: "EMR Round Nose End Mill",
      image: "/images/D35-1.png",
      description: "Compatible with various economical inserts, high cost-performance ratio",
      insertType: "RPMT0802.., RPMW1003..",
      application: "适配多种经济型刀片，性价比高",
      page: "D35",
      url: "/standard-tools/clamp-type-milling/fillet-corner-rounding/EMR-endmill",
    },
    {
      id: "fillet-003",
      name: "EMRW Round Nose End Mill",
      image: "/images/D36-1.png",
      description: "Economical inserts with high applicability",
      insertType: "RPMT08T2.., RPMT10T3.., RPMT1204..",
      application: "刀片经济，适用性高",
      page: "D36",
      url: "/standard-tools/clamp-type-milling/fillet-corner-rounding/EMRW-endmill",
    },
    {
      id: "fillet-004",
      name: "R200 Round Nose End Mill",
      image: "/images/D38-1.png",
      description: "Compatible with precision-ground inserts for higher accuracy",
      insertType: "RCKT/RCMT (10T3, 1204)",
      application: "适配山特精磨刀片，精度更高",
      page: "D38",
      url: "/standard-tools/clamp-type-milling/fillet-corner-rounding/R200-endmill",
    },
    {
      id: "fillet-005",
      name: "R200 Round Nose Milling Cutter",
      image: "/images/D38-2.png",
      description: "High precision for fine machining, tapered design with high rigidity",
      insertType: "RCKT/RCMT/RCMX",
      application: "精度高，适于精加工，锥形设计刚性高",
      page: "D38",
      url: "/standard-tools/clamp-type-milling/fillet-corner-rounding/R200-milling",
    },
    {
      id: "fillet-006",
      name: "TRS Round Nose End Mill",
      image: "/images/D40-1.png",
      description: "Economical inserts with high applicability",
      insertType: "RDKW, RDKX, RDMT, RDMW",
      application: "刀片经济，适用性高",
      page: "D40",
      url: "/standard-tools/clamp-type-milling/fillet-corner-rounding/TRS-endmill",
    },
    {
      id: "fillet-007",
      name: "TRS Round Nose Milling Cutter",
      image: "/images/D41-1.png",
      description: "Economical inserts with high applicability, tapered design with high rigidity",
      insertType: "RDMT, RDMW",
      application: "刀片经济，适用性高，锥形设计刚性高",
      page: "D41",
      url: "/standard-tools/clamp-type-milling/fillet-corner-rounding/TRS-milling",
    },
    {
      id: "fillet-008",
      name: "KDR Cylindrical Milling Cutter",
      image: "/images/D42-1.png",
      description: "Double-sided inserts with 12 indexing positions for maximum economy",
      insertType: "RNPJ1204",
      application: "刀粒可两面使用，12次转位更经济",
      page: "D42",
      url: "/standard-tools/clamp-type-milling/fillet-corner-rounding/KDR-RNPJ",
    },
    {
      id: "fillet-009",
      name: "KDR Cylindrical Milling Cutter",
      image: "/images/D42-2.png",
      description: "Double-sided inserts with excellent economy",
      insertType: "RNGJ1204",
      application: "刀粒可两面使用，经济性好",
      page: "D42",
      url: "/standard-tools/clamp-type-milling/fillet-corner-rounding/KDR-RNGJ",
    },
  ]

  // Performance features
  const performanceFeatures = [
    {
      icon: "Shield",
      title: "Excellence in Mold & Die Machining",
      description: "EMRW and EMR series with high-strength circular cutting edges, perfectly suited to constantly changing engagement conditions of 3D cavity roughing and copy milling with large chip spaces.",
    },
    {
      icon: "Zap", 
      title: "High-Precision & Finishing Performance",
      description: "R200 series delivers exceptional performance with precision-ground inserts and high-rigidity tapered body design, minimizing vibration for superior processing effects.",
    },
    {
      icon: "Target",
      title: "General-Purpose Versatility & Economic Performance",
      description: "TRS and KDR series offer outstanding value with economical inserts. KDR's unique RNPJ1204 inserts provide up to 12 true cutting edges per insert for maximum economic performance.",
    },
  ]

  // Helper function to render icons
  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case "Shield":
        return <Shield className="h-8 w-8 text-red-600" />
      case "Zap":
        return <Zap className="h-8 w-8 text-red-600" />
      case "Target":
        return <Target className="h-8 w-8 text-red-600" />
      default:
        return <Tool className="h-8 w-8 text-red-600" />
    }
  }

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Insert Systems & Physical Formats",
      description: "Portfolio supports wide array of industry-standard round inserts: RPMW/RPMT for EMRW and EMR series, RC□T for high-precision R200 series, RDMW/RDMT for versatile TRS series, and RNPJ highly economical 12-edge inserts for KDR series.",
    },
    {
      title: "Physical Formats & Design Features",
      description: "Available in Fillet End Mills from Ø8 to Ø50 for reach and rigidity, and Fillet Face Mills from Ø50 to Ø300 for wide cuts. Features secure clamping systems, large chip spaces, and high rigidity bodies.",
    },
    {
      title: "Advanced Geometry & Coating",
      description: "TRS cutter features 15° positive angle blade for lighter cutting action. Robust clamping plates (Y-5R, Y-6R, Y-8R) ensure maximum insert security. Tapered designs maximize stiffness and minimize chatter for process stability.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Fillet Corner Rounding Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG Fillet Corner Rounding System
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  I am proud to present a detailed introduction to our comprehensive portfolio of Machine Clamped Fillet Milling Cutters. These tools, also known as Round Nose or Copy Mills, are the cornerstone of modern 3D and contour machining. The defining characteristic of this system is the use of strong, round indexable inserts. This geometry eliminates the weak corners found in square inserts, providing an inherently robust cutting edge that excels in complex, multi-axis toolpaths.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/millingcutter1.png"
                    alt="MZG Professional Fillet Corner Rounding System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">System Performance Analysis</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of our Fillet Milling Cutter system is best understood through its application-specific strengths, which demonstrate a deep commitment to solving distinct manufacturing challenges. Our portfolio is meticulously engineered into two primary formats—integral shank <strong>Fillet End Mills</strong> and arbor-mounted <strong>Fillet Face Mills</strong>—to provide a targeted solution for every application, from heavy-duty mold roughing to high-precision surface finishing.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      Our <strong>EMRW and EMR series</strong> are the premier choice for mold and die applications. Their performance is rooted in the <strong>high-strength circular cutting edge</strong>, which is perfectly suited to the constantly changing engagement conditions of 3D cavity roughing and copy milling. These cutters produce a superior, scalloped surface finish that requires less time in subsequent semi-finishing and finishing operations.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      When the final surface quality and dimensional accuracy are critical, our <strong>R200 series</strong> delivers exceptional performance. These cutters are engineered to use <strong>precision-ground inserts</strong>, which ensures extremely tight tolerances between cutting edges. This high accuracy, combined with a <strong>high-rigidity tapered body design</strong>, minimizes vibration and produces a superior processing effect.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      For shops seeking a balance of high performance and low running costs, our <strong>TRS and KDR series</strong> offer outstanding value. The <strong>KDR series</strong> performance is defined by its remarkable economy; its unique <strong>RNPJ1204 inserts can be indexed on both sides, providing up to 12 true cutting edges per insert</strong>, drastically reducing the cost-per-edge and maximizing the value of each insert.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3 text-sm text-gray-700">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Insert Systems:</strong> RPMW/RPMT, RC□T, RDMW/RDMT, and RNPJ systems for comprehensive application coverage</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Physical Formats:</strong> Fillet End Mills (Ø8-Ø50) and Fillet Face Mills (Ø50-Ø300) for all machining requirements</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Economic Performance:</strong> Up to 12 cutting edges per insert with RNPJ1204 system for maximum cost efficiency</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Advanced Features:</strong> Secure clamping systems, large chip spaces, and high rigidity tapered body designs</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Precision Performance:</strong> High-rigidity body design minimizes vibration for superior surface quality</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.page}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.insertType && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Insert:</span>
                          <span className="text-gray-900 text-right">{product.insertType}</span>
                        </div>
                      )}
                      {product.description && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.description}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultFilletCornerImages[0]}
                    alt="Fillet Corner Rounding Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                  </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultFilletCornerImages[imageIndex % defaultFilletCornerImages.length]
                  : defaultFilletCornerImages[index % defaultFilletCornerImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Fillet Corner Rounding Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                  </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Specifications</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Insert Systems & Physical Formats":
                      return <Layers className="h-6 w-6 text-blue-600 mr-3" />
                    case "Physical Formats & Design Features":
                      return <Shield className="h-6 w-6 text-green-600 mr-3" />
                    case "Advanced Geometry & Coating":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                    </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios & Processing</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Mold & Die Cavity Machining:</strong> Primary application for roughing, semi-finishing, and finishing of complex core and cavity surfaces in injection molds and stamping dies</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>3D Profiling and Copy Milling:</strong> Round insert perfect for tracing complex contours and profiles, essential for turbine blades, medical implants, and aerospace components</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>General Engineering Fillet Creation:</strong> Creating specified corner radii and rounded transitions on machine parts, improving strength by reducing stress concentrations</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Performance Face Milling:</strong> Large-diameter face mills highly effective for general-purpose surfacing, providing smooth, high-quality finish</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Complex Multi-Axis Machining:</strong> Robust cutting edge excels in complex, multi-axis toolpaths with constantly changing engagement conditions</span>
                  </li>
                </ul>
              </div>

              {/* Machining Operations */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Machining Operations
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Machine Rounded Surfaces and Fillets:</strong> Fundamental purpose is to create smooth, rounded transitions and specified corner radii</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Perform Complex 3D Contouring:</strong> Enable efficient and accurate copy milling and multi-axis machining of complex, non-planar surfaces</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Robust Platform for Roughing:</strong> Offer strong and stable cutting edge that can withstand high and variable forces during heavy roughing</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Primary Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Machine Rounded Surfaces and Fillets:</strong> Fundamental purpose is to create smooth, rounded transitions and specified corner radii</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Perform Complex 3D Contouring:</strong> Enable efficient and accurate copy milling and multi-axis machining of complex, non-planar surfaces</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Robust Platform for Roughing:</strong> Offer strong and stable cutting edge that can withstand high and variable forces during heavy roughing</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  Performance Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Deliver Economical High-Performance Solution:</strong> Reduce overall machining costs through use of durable, multi-edge indexable inserts</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Round Insert Geometry Advantage:</strong> Eliminates weak corners found in square inserts, providing inherently robust cutting edge</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Cornerstone of Modern 3D Machining:</strong> Essential tools for complex, multi-axis toolpaths and contour machining applications</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/clamp-type-milling/fillet-corner-rounding" />
          </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional Fillet Corner Rounding Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal fillet corner rounding cutters for specific 3D contouring, mold and die machining, and complex surface finishing applications. From integral shank fillet end mills to arbor-mounted face mills, we provide comprehensive round nose cutting solutions.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same clamp-type-milling directory
                const allClampTypeCategories = [
                  {
                    title: "Ball End Milling Cutters",
                    image: "/images/2F45CRB.png",
                    description: "3D contouring and curved surface machining",
                    url: "/standard-tools/clamp-type-milling/ball-end-milling-cutters",
                  },
                  {
                    title: "Right Angle End Mill",
                    image: "/images/D04-2.png",
                    description: "Precision 90-degree shoulder milling",
                    url: "/standard-tools/clamp-type-milling/right-angle-end-mill",
                  },
                  {
                    title: "Face Milling Cutters",
                    image: "/images/D04-1.png",
                    description: "High-efficiency face milling operations",
                    url: "/standard-tools/clamp-type-milling/face-milling-cutters",
                  },
                  {
                    title: "High Feed Milling Cutter",
                    image: "/images/D07-2.png",
                    description: "High-efficiency material removal",
                    url: "/standard-tools/clamp-type-milling/high-feed-milling-cutter",
                  },
                  {
                    title: "Chamfering Cutters",
                    image: "/images/2F45C.png",
                    description: "Precision chamfering and deburring",
                    url: "/standard-tools/clamp-type-milling/chamfering-cutters",
                  },
                ];
                
                return allClampTypeCategories.map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Clamp Type Milling" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
}