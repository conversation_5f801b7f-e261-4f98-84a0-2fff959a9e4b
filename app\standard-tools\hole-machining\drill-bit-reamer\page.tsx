"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function DrillBitReamerPage() {
  // Drill Bit & Reamer 相关的默认图片
  const defaultDrillImages = [
    "/images/K57-1.png",
    "/images/K59-1.png", 
    "/images/K60-1.png",
    "/images/K61-1.png",
    "/images/K62-2.png",
    "/images/K63-1.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/hole-machining/drill-bit-reamer");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认钻头图片
          setGalleryImages(defaultDrillImages);
        }
      } else {
        // API请求失败，使用默认钻头图片
        setGalleryImages(defaultDrillImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认钻头图片
      setGalleryImages(defaultDrillImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认钻头图片，避免显示无关图片
    setGalleryImages(defaultDrillImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Performance features based on the provided content
  const performanceFeatures = [
    {
      title: "High-Performance Indexable Systems",
      description: "U-Drills, Spade Drills, and Swordtooth Drills with replaceable carbide inserts for maximum productivity and speed",
      icon: "Zap"
    },
    {
      title: "Premium Solid Carbide Performance", 
      description: "Fixed Shank Tungsten Steel Drill Bits with nano coatings for superior hardness and heat resistance",
      icon: "Shield"
    },
    {
      title: "Reliable HSS Performance",
      description: "High-Speed Steel (L6542 M35) drills providing excellent combination of toughness and cost-effectiveness",
      icon: "Target"
    }
  ];

  // Product data based on provided content
  const products = [
    {
      id: "K57-1",
      name: "Straight Shank Drill (L6542 M35)",
      series: "HSS Drill Series",
      image: "/images/K57-1.png",
      description: "High-efficiency drilling with M35 HSS material",
      specifications: "L6542 M35",
      application: "High-efficiency drilling applications",
      pageNumber: "K57",
    },
    {
      id: "K59-1", 
      name: "Oblique Shank Drill (DIN345 HSS L6542 M35)",
      series: "DIN345 Series",
      image: "/images/K59-1.png",
      description: "General purpose oblique shank drill with DIN345 standard",
      specifications: "DIN345 HSS L6542 M35",
      application: "General purpose drilling applications",
      pageNumber: "K59",
    },
    {
      id: "K60-1",
      name: "Oblique Shank Extension Drill (DIN345 HSS)", 
      series: "DIN345 Extension Series",
      image: "/images/K60-1.png",
      description: "General purpose extension drill with DIN345 standard",
      specifications: "DIN345 HSS",
      application: "General purpose extended reach drilling",
      pageNumber: "K60",
    },
    {
      id: "K61-1",
      name: "Tungsten Steel Fixed-Point Drill (WGDDZ)",
      series: "Fixed-Point Drill Series",
      image: "/images/K61-1.png", 
      description: "For aluminum and steel with various coating options and drill point angles",
      specifications: "90°, 60°, 120° angles",
      application: "Aluminum, aluminum alloy, die-cast aluminum; Die prehardened steel, quenched steel",
      pageNumber: "K61",
    },
    {
      id: "K62-2",
      name: "Tungsten Steel Mechanical Reamer (H7)",
      series: "H7 Precision Reamer Series",
      image: "/images/K62-2.png",
      description: "For hard materials up to HRC55 with H7 tolerance",
      specifications: "H7 Tolerance",
      application: "HRC55 steel, stainless steel, titanium, cast iron and other hard materials",
      pageNumber: "K62",
    },
    {
      id: "K62-1",
      name: "Tungsten Steel Fixed Point Drill, Center Drill",
      series: "Center Drill Series",
      image: "/images/K62-1.png",
      description: "Tungsten steel center drill for precision centering",
      specifications: "Center Drill",
      application: "Precision centering operations",
      pageNumber: "K62",
    },
    {
      id: "K63-1",
      name: "Powder High Speed Steel Drill Bit 3D (FMZT)",
      series: "Powder HSS Series",
      image: "/images/K63-1.png",
      description: "High precision drilling with nano coating and powder HSS material",
      specifications: "3D Length",
      application: "Steel, carbon steel, alloy steel, stainless steel, aluminum alloy, copper alloy",
      pageNumber: "K63",
    },
    {
      id: "K64-1",
      name: "Powder High Speed Steel Drill Bit 5D (FMZT)",
      series: "Powder HSS Series",
      image: "/images/K63-1.png",
      description: "Extended length high precision drilling with nano coating",
      specifications: "5D Length",
      application: "Steel, carbon steel, alloy steel, stainless steel, aluminum alloy, copper alloy",
      pageNumber: "K64",
    },
    {
      id: "K65-1",
      name: "HSS Straight Shank Drill",
      series: "HSS Standard Series",
      image: "/images/K65-1.png",
      description: "Standard HSS drill for general material processing",
      specifications: "HSS Material",
      application: "Copper, aluminum, steel, cast iron processing",
      pageNumber: "K65",
    },
    {
      id: "K67-1",
      name: "HRC45° Tungsten Steel Fixed Shank Drill Bit (DBWGZT45C)",
      series: "Fixed Shank Drill Series",
      image: "/images/K67-1.png",
      description: "Nano coating tungsten steel drill for general materials",
      specifications: "HRC45° with nano coating",
      application: "Nonferrous metal, gray cast iron, stainless steel, heat resistant alloy steel",
      pageNumber: "K67",
    },
    {
      id: "K67-2",
      name: "Aluminum-Use Fixed Shank Tungsten Steel Drill-3D/5D (DBWGZT-AL)",
      series: "Aluminum Specialist Series",
      image: "/images/K67-2.png",
      description: "Specialized tungsten steel drill designed specifically for aluminum",
      specifications: "3D/5D Length",
      application: "Specifically designed for aluminum processing",
      pageNumber: "K67",
    },
    {
      id: "K68-1",
      name: "55° Bronze Colored Coating Tungsten Steel Drill-3D/5D (DBWGZT55C)",
      series: "Bronze Coated Series",
      image: "/images/K68-1.png",
      description: "Bronze coating for difficult-to-cut materials",
      specifications: "55° with bronze coating, 3D/5D",
      application: "Hardened steel, high temperature alloy, difficult processing materials",
      pageNumber: "K68",
    },
  ];

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Professional Hole-Making Tools
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  Drill Bit & Reamer System
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  Comprehensive hole-making tool ecosystem engineered to address every stage of the hole-machining process with maximum efficiency and precision. From initial pilot mark to final accurately reamed bore.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/drilling-system.png"
                    alt="MZG Professional Drill Bit & Reamer System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">System Performance Analysis</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      As a senior industrial tooling expert at MZG Tool Machine Company, I am proud to present a detailed introduction to our comprehensive hole-making tool ecosystem. This collection represents a complete spectrum of solutions, engineered to address every stage of the hole-machining process with maximum efficiency and precision. From the initial pilot mark to the final, accurately reamed bore, each tool is designed with a specific purpose, material, and performance level in mind.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The drill bit is the cornerstone of our hole-making system, encompassing a vast array of designs from fundamental solid drills to advanced, high-productivity indexable systems. Our High-Performance Indexable Systems including U-Drills, Spade Drills, and Swordtooth Drills deliver exceptional performance through replaceable carbide inserts, operating at drastically higher speeds and feeds than solid drills.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      Our Premium Solid Carbide Performance Fixed Shank Tungsten Steel Drill Bits represent the pinnacle of solid drilling performance. Made from micro-grain carbide and enhanced with advanced nano coatings, these drills offer superior hardness, heat resistance, and wear life, enabling effective machining of difficult materials like hardened steels up to HRC55°.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The foundation of our range is High-Speed Steel (L6542 M35) drills, providing an excellent combination of toughness and cost-effectiveness. The inclusion of cobalt (M35) enhances red-hardness, improving performance over standard HSS for general-purpose drilling applications.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Materials: HSS L6542 M35, Solid Tungsten Carbide</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Indexable Systems: U-Drills (Ø8-65mm), Spade Drills, Swordtooth Drills (Ø12-33.5mm)</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Shank Types: Straight Shank, Oblique Shank (DIN345), Morse Taper</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Coatings: Nano & Bronze Coating technology</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Length Ratios: 3D and 5D standard offerings</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                          <span className="text-gray-900 text-right">{product.series}</span>
                        </div>
                      )}
                      {product.application && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.application}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultDrillImages[0]}
                    alt="Drill Bit & Reamer Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                  </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultDrillImages[imageIndex % defaultDrillImages.length]
                  : defaultDrillImages[index % defaultDrillImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Drill Bit & Reamer Product ${index + 1}`}
                      width={120}
                      height={120}
                      quality={90}
                      className="object-contain w-full h-full transition-all duration-300 group-hover:scale-110"
                    />
                  </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Specifications</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Drill className="h-6 w-6 text-blue-600 mr-3" />
                  Drill Bit Specifications
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Materials:</strong> High-Speed Steel (L6542 M35), Powder High-Speed Steel, Solid Tungsten Carbide</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Indexable Systems:</strong> U-Drills (Ø8-65mm), Spade Drills, Swordtooth Drills (Ø12-33.5mm)</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Shank Types:</strong> Straight Shank, Oblique Shank (DIN345), Morse Taper</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Coatings:</strong> Nano Coating, Bronze Colored Coating, Uncoated for aluminum</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Length Ratios:</strong> 3D and 5D standard offerings</span>
                  </li>
                </ul>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Reamer & Fixed-Point Drill Specifications
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Reamer Material:</strong> Ultrafine Tungsten Steel cutting edges (brazed or butt-welded)</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Tolerance:</strong> H7 precision for extremely tight tolerances</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Fixed-Point Drill:</strong> Point Angles 60°, 90°, 120° for various applications</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Design Features:</strong> Left-handed helix, unequal flute spacing for vibration reduction</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Diameter Range:</strong> Typically 2mm to 20mm and beyond</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios & Processing</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Production CNC Machining:</strong> Indexable U-Drills and coated solid carbide drills for automotive and aerospace</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Deep Hole Drilling:</strong> Spade Drills for L/D ratios exceeding 5x in mold making and hydraulic systems</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Hard Material Machining:</strong> HRC55° coated drills for hardened die steels and heat-resistant alloys</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Precision Finishing:</strong> Reamers for bearing fits, dowel pin locations, and hydraulic valve bores</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>General Fabrication:</strong> HSS drills for vertical drills, radial drills, and manual milling machines</span>
                  </li>
                </ul>
              </div>

              {/* Machining Operations */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Machining Operations
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Hole Creation:</strong> Efficiently and reliably create holes from solid material with scalable performance solutions</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Precision Guidance:</strong> Fixed-point drills create accurate pilot points preventing drill "walking" and ensuring positional accuracy</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Hole Finishing:</strong> Reamers provide final size accuracy with H7 tolerance and superior surface finish</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Primary Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Hole Creation:</strong> Efficiently and reliably create holes from solid material with scalable performance solutions</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Precision Guidance:</strong> Fixed-point drills create accurate pilot points preventing drill "walking" and ensuring positional accuracy</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Hole Finishing:</strong> Reamers provide final size accuracy with H7 tolerance and superior surface finish</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  Performance Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Maximum Rigidity:</strong> Solid tungsten steel construction ensures hole accuracy and straightness</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Speed Capability:</strong> Advanced coatings enable aggressive cutting parameters in tough materials</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Optimal Chip Control:</strong> Produce manageable chips with efficient evacuation for process security</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/hole-machining/drill-bit-reamer" />
          </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional Hole-Making Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal drill bits and reamers for specific hole-making applications. From high-speed indexable systems to precision finishing tools, we provide comprehensive drilling and reaming solutions for all materials and applications.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same hole-machining directory
                const allHoleMachiningCategories = [
                  {
                    title: "Boring Machining",
                    image: "/images/boring-tool.png",
                    description: "Precision boring tools for accurate holes",
                    url: "/standard-tools/hole-machining/boring-machining",
                  },
                  {
                    title: "Fast Drilling",
                    image: "/images/fast-drill.png",
                    description: "High-speed drilling solutions",
                    url: "/standard-tools/hole-machining/fast-drilling",
                  },
                  {
                    title: "Fine Boring",
                    image: "/images/fine-boring.png",
                    description: "Ultra-precision boring applications",
                    url: "/standard-tools/hole-machining/fine-boring",
                  },
                  {
                    title: "Rough Boring",
                    image: "/images/rough-boring.png",
                    description: "Heavy-duty boring operations",
                    url: "/standard-tools/hole-machining/rough-boring",
                  },
                  {
                    title: "Drill Bit",
                    image: "/images/K57-1.png",
                    description: "Comprehensive drilling solutions",
                    url: "/standard-tools/hole-machining/drill-bit",
                  },
                ];
                
                return allHoleMachiningCategories.slice(0, 5).map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Hole Machining" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
} 