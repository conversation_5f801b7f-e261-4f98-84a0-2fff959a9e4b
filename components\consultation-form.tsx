"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Upload, FileIcon, X, Send, CheckCircle, User, Building2, FileText } from "lucide-react"

// Form validation schema - only name, email, and detailed requirements are required
const consultationFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  company: z.string().optional(), // Optional
  phone: z.string().optional(), // Optional
  partDetails: z.string().min(10, "Please provide detailed requirements (at least 10 characters)"),
})

type ConsultationFormData = z.infer<typeof consultationFormSchema>

interface ConsultationFormProps {
  /** Information source identifier */
  source?: string
  /** Form title */
  title?: string
  /** Product page information (optional) */
  productInfo?: {
    pagePath: string
    productName: string
  }
}

export default function ConsultationForm({ 
  source = "website", 
  title = "Technical Consultation",
  productInfo 
}: ConsultationFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitMessage, setSubmitMessage] = useState<{ type: 'success' | 'error', message: string } | null>(null)
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([])
  const [isDragOver, setIsDragOver] = useState(false)

  const form = useForm<ConsultationFormData>({
    resolver: zodResolver(consultationFormSchema),
    defaultValues: {
      name: "",
      email: "",
      company: "",
      phone: "",
      partDetails: "",
    },
  })

  // Handle file upload
  const handleFileUpload = (files: FileList | null) => {
    if (!files) return

    const validFiles: File[] = []
    const maxSize = 10 * 1024 * 1024 // 10MB
    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'application/pdf',
      'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ]

    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      
      if (!allowedTypes.includes(file.type)) {
        setSubmitMessage({
          type: 'error',
          message: `File ${file.name} format not supported. Supported formats: Images, PDF, Word, Excel`
        })
        continue
      }
      
      if (file.size > maxSize) {
        setSubmitMessage({
          type: 'error',
          message: `File ${file.name} is too large. Maximum file size is 10MB`
        })
        continue
      }
      
      validFiles.push(file)
    }

    if (uploadedFiles.length + validFiles.length > 5) {
      setSubmitMessage({
        type: 'error',
        message: 'Maximum 5 files allowed'
      })
      return
    }

    setUploadedFiles(prev => [...prev, ...validFiles])
    setSubmitMessage(null)
  }

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleFileUpload(event.target.files)
  }

  // Handle drag events
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    handleFileUpload(e.dataTransfer.files)
  }

  // Remove file
  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index))
  }

  // Submit form
  const onSubmit = async (data: ConsultationFormData) => {
    setIsSubmitting(true)
    setSubmitMessage(null)

    try {
      // Create FormData for file upload
      const formData = new FormData()
      
      // Add form data
      Object.entries(data).forEach(([key, value]) => {
        if (value) { // Only add fields with values
          formData.append(key, value)
        }
      })
      
      // Add other information
      formData.append('source', source)
      if (productInfo) {
        formData.append('productPagePath', productInfo.pagePath)
        formData.append('productName', productInfo.productName)
      }
      
      // Add files
      uploadedFiles.forEach((file, index) => {
        formData.append(`attachment_${index}`, file)
      })

      const response = await fetch('/api/consultation', {
        method: 'POST',
        body: formData,
      })

      const result = await response.json()

      if (result.success) {
        setSubmitMessage({
          type: 'success',
          message: 'Thank you for your consultation! We have received your information and our technical experts will contact you within 24 hours.'
        })
        
        // Reset form
        form.reset()
        setUploadedFiles([])
        
        // Clear success message after 5 seconds
        setTimeout(() => {
          setSubmitMessage(null)
        }, 5000)
      } else {
        setSubmitMessage({
          type: 'error',
          message: result.message || 'Submission failed, please try again or contact customer service'
        })
      }
    } catch (error) {
      console.error('Submission error:', error)
      setSubmitMessage({
        type: 'error',
        message: 'Network error, please check your connection and try again'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Get file icon
  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) return '🖼️'
    if (fileType.includes('pdf')) return '📄'
    if (fileType.includes('word')) return '📝'
    if (fileType.includes('excel') || fileType.includes('sheet')) return '📊'
    return '📁'
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm max-w-6xl mx-auto">
      <div className="p-8">
          {/* Form title */}
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-2">{title}</h3>
            <p className="text-gray-600">
              Tell us your specific requirements and our technical experts will provide you with professional solutions
            </p>
          </div>

          {/* Submit message */}
          {submitMessage && (
            <Alert className={`mb-6 ${submitMessage.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
              <div className="flex items-center">
                {submitMessage.type === 'success' ? (
                  <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                ) : (
                  <X className="h-4 w-4 text-red-600 mr-2" />
                )}
                <AlertDescription className={submitMessage.type === 'success' ? 'text-green-700' : 'text-red-700'}>
                  {submitMessage.message}
                </AlertDescription>
              </div>
            </Alert>
          )}

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              {/* Two column layout */}
              <div className="grid lg:grid-cols-2 gap-8 items-start">
                {/* Left Column - Contact Information */}
                <div className="space-y-6 h-full">
                  <div className="flex items-center space-x-2 mb-4">
                    <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-lg">
                      <User className="h-4 w-4 text-blue-600" />
                    </div>
                    <h4 className="text-lg font-semibold text-gray-900">Contact Information</h4>
                  </div>

                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">
                          Your Name <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Enter your full name" 
                            {...field}
                            className="h-11 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="company"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">Company/Store Name</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Enter your company or store name" 
                            {...field}
                            className="h-11 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">
                          Email Address <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input 
                            type="email"
                            placeholder="Enter your email address" 
                            {...field}
                            className="h-11 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">
                          Phone Number <span className="text-gray-400">(Optional, for quick contact)</span>
                        </FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Enter your phone number" 
                            {...field}
                            className="h-11 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Right Column - Consultation Details */}
                <div className="space-y-6 h-full">
                  <div className="flex items-center space-x-2 mb-4">
                    <div className="flex items-center justify-center w-8 h-8 bg-indigo-100 rounded-lg">
                      <Building2 className="h-4 w-4 text-indigo-600" />
                    </div>
                    <h4 className="text-lg font-semibold text-gray-900">Consultation Details</h4>
                  </div>

                  {/* Detailed requirements */}
                  <FormField
                    control={form.control}
                    name="partDetails"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">
                          Part Details <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Textarea 
                            placeholder="Please describe your specific requirements in detail, including:&#10;• Required tool types and specifications&#10;• Processing materials and process requirements&#10;• Technical parameters and performance requirements&#10;• Expected quantity and delivery requirements"
                            className="min-h-[200px] border-gray-200 focus:border-blue-500 focus:ring-blue-500 resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* File upload area */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Related Files (Optional)
                    </label>
                    <div 
                      className={`border-2 border-dashed rounded-lg p-6 transition-colors cursor-pointer ${
                        isDragOver 
                          ? 'border-blue-400 bg-blue-50' 
                          : 'border-gray-300 hover:border-blue-400'
                      }`}
                      onDragOver={handleDragOver}
                      onDragLeave={handleDragLeave}
                      onDrop={handleDrop}
                      onClick={() => document.getElementById('file-upload')?.click()}
                    >
                      <div className="text-center">
                        <Upload className="h-10 w-10 text-gray-400 mx-auto mb-4" />
                        <div className="text-sm text-gray-600 mb-2">
                          <span className="text-blue-600 hover:text-blue-700 font-medium">
                            Click to upload files
                          </span>
                          <span className="ml-1">or drag and drop files here</span>
                        </div>
                        <p className="text-xs text-gray-500">
                          Support Images, PDF, Word, Excel formats, max 10MB per file, up to 5 files
                        </p>
                        <input
                          id="file-upload"
                          type="file"
                          multiple
                          accept="image/*,.pdf,.doc,.docx,.xls,.xlsx"
                          onChange={handleFileSelect}
                          className="hidden"
                        />
                      </div>
                    </div>

                    {/* Uploaded files list */}
                    {uploadedFiles.length > 0 && (
                      <div className="mt-4 space-y-2">
                        <p className="text-sm font-medium text-gray-700">Uploaded Files:</p>
                        {uploadedFiles.map((file, index) => (
                          <div key={index} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                            <div className="flex items-center">
                              <span className="text-lg mr-3">{getFileIcon(file.type)}</span>
                              <div>
                                <p className="text-sm font-medium text-gray-900">{file.name}</p>
                                <p className="text-xs text-gray-500">
                                  {(file.size / 1024 / 1024).toFixed(2)} MB
                                </p>
                              </div>
                            </div>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeFile(index)}
                              className="text-gray-400 hover:text-red-600 p-2"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Submit button */}
              <div className="flex justify-center pt-6">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium transition-all duration-300 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Submitting...
                    </div>
                  ) : (
                    <div className="flex items-center justify-center">
                      <Send className="h-4 w-4 mr-2" />
                      Submit Consultation
                    </div>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </div>
    )
  } 