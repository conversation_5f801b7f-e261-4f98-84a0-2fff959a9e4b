const { neon } = require('@neondatabase/serverless');
const sql = neon(process.env.DATABASE_URL);

async function checkPost() {
  try {
    console.log('Checking boring-machining post...');
    
    const result = await sql`
      SELECT slug, title, content_images, content_images_count, 
             LEFT(content, 500) as content_preview
      FROM blog_posts 
      WHERE slug = 'system-architecture-and-integration'
    `;
    
    if (result.length === 0) {
      console.log('❌ Post not found');
      return;
    }
    
    const post = result[0];
    console.log('✅ Post found:');
    console.log('Title:', post.title);
    console.log('Content Images Count:', post.content_images_count);
    console.log('Content Images:', post.content_images);
    console.log('Content Preview:', post.content_preview);
    
    if (post.content_images) {
      console.log('\n📸 Content Images Details:');
      const images = JSON.parse(post.content_images);
      images.forEach((img, index) => {
        console.log(`  ${index + 1}. URL: ${img.url}`);
        console.log(`     Alt: ${img.alt}`);
        console.log(`     Position: ${img.position}`);
      });
    } else {
      console.log('❌ No content images found');
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

checkPost();
