"use client"

import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function BoringMachiningPage() {
  // Boring Machining 相关的默认图片
  const defaultBoringMachiningImages = [
    "/images/k36-1.png",
    "/images/k36-2.png", 
    "/images/k38-1.png",
    "/images/k39-1.png",
    "/images/k44-1.png",
    "/images/k45-1.png",
    "/images/k48-1.png",
    "/images/k50-1.png",
    "/images/k51-1.png",
    "/images/k52-1.png",
    "/images/k56-1.png",
    "/images/k56-2.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/hole-machining/boring-machining");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认Boring Machining图片
          setGalleryImages(defaultBoringMachiningImages);
        }
      } else {
        // API请求失败，使用默认Boring Machining图片
        setGalleryImages(defaultBoringMachiningImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认Boring Machining图片
      setGalleryImages(defaultBoringMachiningImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认Boring Machining图片，避免显示无关图片
    setGalleryImages(defaultBoringMachiningImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data based on provided content
  const products = [
    {
      id: "boring-001",
      name: "NBH2084 Boring Cutter Toolholder",
      series: "NBH Heavy-Duty Toolholder Series",
      image: "/images/k36-1.png",
      description: "Heavy-duty tool holder for large-diameter boring applications",
      specifications: "Ø>200mm",
      application: "For boring cutters with diameters greater than 200mm; compatible with various machine interfaces",
      pageNumber: "K36",
    },
    {
      id: "boring-002", 
      name: "BST Boring Cutter Toolholder",
      series: "BST Heavy-Duty Toolholder Series",
      image: "/images/k36-2.png",
      description: "Heavy-duty tool holder for large-diameter boring applications",
      specifications: "Ø>200mm",
      application: "For boring cutters with diameters greater than 200mm; compatible with various machine interfaces",
      pageNumber: "K36",
    },
    {
      id: "boring-003",
      name: "LBK Series Boring Cutter Toolholders", 
      series: "LBK Universal Toolholder Series",
      image: "/images/k38-1.png",
      description: "Comprehensive tool holder series for various machine interfaces",
      specifications: "BT-LBK, SK-LBK, HSK-LBK, NT-LBK, MT-LBK, C-LBK",
      application: "Universal tool holder family including BT-LBK, SK-LBK, HSK-LBK, Straight Shank LBK, NT-LBK, MT-LBK, C-LBK, MT-DCK",
      pageNumber: "K38",
    },
    {
      id: "boring-004",
      name: "HBOR Fine Boring Head (System)",
      series: "HBOR Ultra-Precision Series",
      image: "/images/k39-1.png", 
      description: "Ultra-precision boring system with 0.002mm micro-adjustment",
      specifications: "Ø2-160mm",
      application: "Provides micro-adjustment of 0.002mm for high precision boring; boring range 2~160mm; includes reverse boring and small carbide boring cutters",
      pageNumber: "K39",
    },
    {
      id: "boring-005",
      name: "CBH Fine Boring Head (System)",
      series: "CBH High-Precision Series",
      image: "/images/k44-1.png",
      description: "High-precision boring system with dynamic balance features",
      specifications: "Ø20-1250mm",
      application: "Provides micro-adjustment of 0.01mm for precision boring; boring range 20~1250mm (large CBH); features dynamic balance and supports reverse boring",
      pageNumber: "K44",
    },
    {
      id: "boring-006",
      name: "CBI Small Diameter Fine Boring (System)",
      series: "CBI Small Diameter Series",
      image: "/images/k45-1.png",
      description: "Precision boring system for small diameter holes",
      specifications: "Ø3-50mm", 
      application: "Provides micro-adjustment of 0.01mm for small hole precision machining; boring range 3~50mm",
      pageNumber: "K45",
    },
    {
      id: "boring-007",
      name: "NBJ NBH Boring Set",
      series: "NBJ NBH Boring Set Series",
      image: "/images/k48-1.png",
      description: "Comprehensive boring set with NBJ16 and NBH2084 components",
      specifications: "NBJ16: Ø8-50mm, NBH2084: Ø8-280mm",
      application: "NBJ16 boring range 8~50mm; NBH2084 boring range 8~280mm",
      pageNumber: "K48",
    },
    {
      id: "boring-008", 
      name: "TWE Double Edge Rough Boring",
      series: "TWE Double-Edge Rough Boring Series",
      image: "/images/k50-1.png",
      description: "High-efficiency double edge rough boring system",
      specifications: "Ø20-810mm",
      application: "For rough boring; boring range 20~204mm (large TWE 200-810mm); suitable for single, double, and high-low level boring",
      pageNumber: "K50",
    },
    {
      id: "boring-009",
      name: "RBH Double Edge Rough Boring", 
      series: "RBH Double-Edge Rough Boring Series",
      image: "/images/k51-1.png",
      description: "Large-capacity double edge rough boring system",
      specifications: "Ø19-1200mm",
      application: "For rough boring; boring range 19~204mm (large RBH 200-1200mm)",
      pageNumber: "K51",
    },
    {
      id: "boring-010",
      name: "BSA/BSB Rough Boring",
      series: "BSA/BSB Rough Boring Series",
      image: "/images/k52-1.png",
      description: "Versatile rough boring tools for various hole types",
      specifications: "Ø20-190mm", 
      application: "For rough boring; boring range 20~190mm; suitable for both through hole and blind hole machining",
      pageNumber: "K52",
    },
    {
      id: "boring-011",
      name: "TBL Fixed Double Edge Rough Boring Bar",
      series: "TBL Fixed Boring Series",
      image: "/images/k56-1.png",
      description: "Fixed geometry double edge rough boring bar",
      specifications: "Ø8-50mm",
      application: "For fixed rough boring; boring range 8~50mm",
      pageNumber: "K56",
    },
    {
      id: "boring-012",
      name: "TBC Fixed Rough Boring Cutter Head",
      series: "TBC Fixed Boring Series",
      image: "/images/k56-2.png", 
      description: "Fixed geometry rough boring cutter head",
      specifications: "Ø45-130mm",
      application: "For fixed rough boring; boring range 45~130mm",
      pageNumber: "K56",
    },
  ]

  // Performance features
  const performanceFeatures = [
    {
      icon: "Shield",
      title: "Ultimate Precision and Fine Adjustment",
      description: "HBOR Fine Boring Head offers 0.002mm per division adjustment resolution, while CBH and NBJ/NBH systems provide 0.01mm fine-tuning accuracy for exceptional surface finishes and dimensional accuracy.",
    },
    {
      icon: "Zap", 
      title: "Robust and Powerful Roughing",
      description: "TWE Double-Edge Rough Boring Head utilizes serrated sliding seat design with opposing cutting forces for stable, vibration-free cutting under heavy loads at significantly higher feed rates.",
    },
    {
      icon: "Target",
      title: "Unmatched Modularity and Flexibility",
      description: "LBK Modular Boring System allows users to build perfect tools for any application by combining universal toolholders with various extension adapters, reducing adapters, and boring heads.",
    },
  ]

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Precision Boring Systems",
      description: "HBOR system: Ø2mm-160mm with 0.002mm adjustment precision. CBH system: Ø20mm-1250mm with 0.01mm adjustment precision. CBI system: Ø3mm-Ø50mm dedicated small-hole system with 0.01mm adjustment. All systems feature dynamic balance and reverse boring capability.",
    },
    {
      title: "Rough Boring Systems",
      description: "TWE system: Ø20mm-800mm with high/low level double-edge serrated design. RBH system: Ø19mm-1200mm for heavy-duty double-edge roughing. BSA/BSB systems: Ø20mm-190mm adjustable rough boring heads with 45° & 90° lead angles. TBL/TBC: Fixed double-edge boring bars/heads.",
    },
    {
      title: "Toolholder Interfaces",
      description: "Complete compatibility with all major machine spindles including BT, BBT (dual contact), SK, HSK, NT, CAT, ISO, R8, Morse Taper (MT/MTA/MTB), and Straight Shank (C-Type). LBK System includes toolholders, extension adapters, reducing adapters, and chamfering cutters.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Boring Machining System Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG Boring Machining System
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  As a senior industrial tooling expert at MZG Tool Machine Company, I am pleased to present a comprehensive introduction to our High-Efficiency Boring Machining System. This system represents the pinnacle of our hole-finishing technology, offering an integrated and modular ecosystem for precision machining. It is engineered to provide unparalleled accuracy, robust performance, and exceptional flexibility, covering a staggering range of diameters from micro-bores of 2mm up to massive industrial bores of 1200mm.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/Boring-Machining-System.png"
                    alt="MZG Professional Boring Machining System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">System Performance Analysis</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of our Boring Machining System is defined by its precision, power, modularity, and cost-effectiveness. For finishing operations, our system delivers peerless accuracy. The HBOR Fine Boring Head is the champion of precision, offering an incredibly fine adjustment resolution of 0.002mm per division, allowing operators to dial in tolerances with absolute confidence.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      When the objective is high-volume material removal, our rough boring systems excel. The TWE Double-Edge Rough Boring Head utilizes a new serrated sliding seat design. This creates an incredibly rigid lock-up, where the opposing cutting forces of the two inserts are mutually offset, resulting in stable, vibration-free cutting even under heavy loads.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The cornerstone of the system is the LBK Modular Boring System. This design philosophy allows users to "build" the perfect tool for any specific application. By combining a universal LBK toolholder with various extension adapters, reducing adapters, and boring heads, an almost infinite number of length and diameter combinations can be created.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      For challenging deep-hole finishing, our FC Integral Tungsten Steel Boring Bars deliver uncompromising performance. Constructed from superhard tungsten carbide, they possess extreme hardness and seismic resistance, capable of precision boring at depths up to 10 times the diameter (10D) while effectively damping vibration.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Total Boring Range: Ø2mm to Ø1200mm</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Precision Range: 0.002mm to 0.01mm</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Interface Standards: BT, SK, HSK, NT, MT, C</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Cooling System: Central coolant channels</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Special Features: Reverse boring capability</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                          <span className="text-gray-900 text-right">{product.series}</span>
                        </div>
                      )}
                      {product.application && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.application}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultBoringMachiningImages[0]}
                    alt="Boring Machining System Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                  </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultBoringMachiningImages[imageIndex % defaultBoringMachiningImages.length]
                  : defaultBoringMachiningImages[index % defaultBoringMachiningImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Boring Machining System Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                  </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Precision Boring Systems":
                      return <Target className="h-6 w-6 text-blue-600 mr-3" />
                    case "Rough Boring Systems":
                      return <Drill className="h-6 w-6 text-green-600 mr-3" />
                    case "Toolholder Interfaces":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                    </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios & Applicable Machining</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Precision Finishing:</strong> Primary application for HBOR, CBH, and CBI systems in mold & die, aerospace, and medical component manufacturing requiring tight tolerances (IT6 and better)</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Heavy-Duty Roughing:</strong> Domain of TWE and RBH systems used in energy, automotive, and heavy equipment manufacturing for rapid material removal from castings and forgings</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Deep Hole Machining:</strong> FC Tungsten Steel bars for boring deep, stable holes in components like hydraulic cylinders and landing gear</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Job Shops & Versatile Environments:</strong> Modular LBK system ideal for job shops facing wide variety of parts and hole sizes with rapid reconfiguration capability</span>
                  </li>
                </ul>
              </div>

              {/* Applicable Machining */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Applicable Machining
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Large Diameter Boring:</strong> Large-diameter versions of CBH, TWE, and RBH heads for machining large engine blocks, turbine housings, and gearbox casings</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Multi-Function Machining:</strong> TCAP drill pipe offers unique solution for CNC lathes, combining turning, boring, and drilling functions in single tool</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Precision Hole Machining:</strong> Micro-adjustment capabilities from 0.002mm to 0.01mm enabling manufacturers to meet stringent tolerance requirements</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Modular Tool Configuration:</strong> Universal machine compatibility through diverse tool holder options reducing inventory requirements and simplifying tool management</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Primary Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Deliver Ultimate Precision:</strong> Accurately machine holes to extremely tight dimensional and geometric tolerances, providing precise control down to the micron level</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Maximize Material Removal Rate:</strong> Execute rough boring operations with combination of high feed rates and cutting depths, drastically reducing cycle times</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Modular and Scalable Solution:</strong> Offer flexible platform configurable to any required diameter and depth, ensuring optimal tool assembly for any task</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  Performance Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Reduce Long-Term Operational Costs:</strong> Through indexable inserts, replaceable wear components, and modularity, lower total cost of ownership compared to solid tooling</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Enable Complex Boring Operations:</strong> Provide solutions for challenging tasks such as reverse boring, outer-diameter boring, deep-hole finishing, and chamfering</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Exceptional Rigidity and Deep-Hole Capability:</strong> FC Integral Tungsten Steel Boring Bars deliver precision boring at depths up to 10D while effectively damping vibration</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/hole-machining/boring-machining" />
          </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional Boring Machining Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal boring machining systems for specific precision finishing applications, from micro-adjustment fine boring to heavy-duty rough boring. We provide comprehensive solutions covering Ø2mm to Ø1200mm diameter range with ultimate precision and flexibility.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same hole-machining directory
                const allHoleMachiningCategories = [
                  {
                    title: "Fast Drilling",
                    image: "/images/k05-1.png",
                    description: "High-efficiency fast drilling systems",
                    url: "/standard-tools/hole-machining/fast-drilling",
                  },
                  {
                    title: "Drill Bit",
                    image: "/images/k07-1.png",
                    description: "Standard drill bits for various hole machining needs",
                    url: "/standard-tools/hole-machining/drill-bit",
                  },
                  {
                    title: "Rough Boring",
                    image: "/images/k50-1.png",
                    description: "Rough boring tools for large diameter hole machining",
                    url: "/standard-tools/hole-machining/rough-boring",
                  },
                  {
                    title: "Fine Boring",
                    image: "/images/k39-1.png",
                    description: "Fine boring tools for high precision hole machining",
                    url: "/standard-tools/hole-machining/fine-boring",
                  },
                  {
                    title: "Drill Bit Reamer",
                    image: "/images/k44-1.png",
                    description: "Precision reaming tools for hole finishing",
                    url: "/standard-tools/hole-machining/drill-bit-reamer",
                  },
                ];
                
                return allHoleMachiningCategories.slice(0, 5).map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Hole Machining" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
} 