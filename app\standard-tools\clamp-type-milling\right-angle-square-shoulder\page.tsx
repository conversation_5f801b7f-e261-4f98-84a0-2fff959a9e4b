"use client"

import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function RightAngleSquareShoulderPage() {
  // Right Angle Face Milling Cutters相关的默认图片
  const defaultRightAngleImages = [
    "/images/D03-1.png",
    "/images/D04-1.png", 
    "/images/D07-2.png",
    "/images/D09-1.png",
    "/images/D12-2.png",
    "/images/D14-1.png",
    "/images/D16-1.png",
    "/images/D18-2.png",
    "/images/D21-1.png",
    "/images/D22-1.png",
    "/images/D22-2.png",
    "/images/D24-1.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/clamp-type-milling/right-angle-square-shoulder");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认图片
          setGalleryImages(defaultRightAngleImages);
        }
      } else {
        // API请求失败，使用默认图片
        setGalleryImages(defaultRightAngleImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认图片
      setGalleryImages(defaultRightAngleImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认图片，避免显示无关图片
    setGalleryImages(defaultRightAngleImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Performance features based on user content
  const performanceFeatures = [
    {
      icon: "Shield",
      title: "Heavy-Duty and High-Efficiency Roughing Performance",
      description: "3PTF and AP1003 series deliver exceptional performance with rotary and feed volume three times that of normal cutters, featuring robust cutter body and super-strong inserts."
    },
    {
      icon: "Zap",
      title: "Extreme High-Feed Milling Performance",
      description: "R217.96 and MFWN series utilize high-feed milling strategy with shallow axial depth but aggressive feed per tooth, achieving phenomenal feed rates and maximum throughput."
    },
    {
      icon: "Target",
      title: "High-Precision and Finishing Performance",
      description: "R390 series and MA-AP Adjustable Face Milling Cutter with fine axial adjustment function achieve microscopic level runout between inserts for exceptional surface finishes."
    }
  ];

    // Product data based on user content about Right Angle Face Milling Cutters
  const products = [
     {
       id: "right-angle-001",
       name: "LN11R/LN16R Quasi-heavy cutting cutter head (Cutter head type)",
       image: "/images/D03-1.png",
       description: "Powerful, stable platform for quasi-heavy cutting operations",
       series: "LN11R/LN16R Series",
       insertType: "LMMU1107.., LMMU1609..",
       performance: "Quasi-heavy cutting capability",
       application: "准重切削加工",
       features: "Internal cooling holes, excellent process security",
       pageNumber: "D03",
     },
     {
       id: "right-angle-002",
       name: "4NKT Face milling cutter head",
       image: "/images/D04-1.png",
       description: "Double-sided four-corner milling cutter head",
       series: "4NKT Series",
       insertType: "4NK(H)T 0603..",
       performance: "Double-sided cutting efficiency",
       application: "双面四角铣削",
       features: "Four-corner insert design, high efficiency",
       pageNumber: "D04",
     },
     {
       id: "right-angle-003",
       name: "R217.96 Clip-Type Fast Feed End Milling Cutter Head",
       image: "/images/D07-2.png",
       description: "High-feed, high-efficiency milling solution",
       series: "R217.96 Series",
       insertType: "WNMU0404..",
       performance: "High feed rates, maximum efficiency",
       application: "高进给，高效率",
       features: "Fast feed technology, clip-type design",
       pageNumber: "D07",
     },
     {
       id: "right-angle-004",
       name: "MFWN Clip-Type Fast Feed End Milling Cutter",
       image: "/images/D09-1.png",
       description: "High rigidity with economical double-sided hexagonal inserts",
       series: "MFWN Series",
       insertType: "WNMU0806..",
       performance: "High rigidity, economical operation",
       application: "高刚性，经济型双面六角刀片",
       features: "Double-sided hexagonal inserts, cost-effective",
       pageNumber: "D09",
     },
     {
       id: "right-angle-005",
       name: "6NTF Right Angle Shoulder Milling Cutter",
       image: "/images/D12-2.png",
       description: "Economical and efficient double-sided hexagonal insert cutter",
       series: "6NTF Series",
       insertType: "M6NGU0604.., M6NGU0905..",
       performance: "Economic efficiency with double-sided inserts",
       application: "双面六角刀片，经济高效",
       features: "Hexagonal insert design, cost-effective operation",
       pageNumber: "D12",
     },
     {
       id: "right-angle-006",
       name: "3PTF Right Angle Shoulder Milling Cutter",
       image: "/images/D14-1.png",
       description: "High-efficiency heavy-duty milling cutter",
       series: "3PTF Series",
       insertType: "3PKT1004.., 3PKT1505.., 3PKT1906..",
       performance: "High-efficiency heavy-duty cutting",
       application: "高效强力铣削",
       features: "Heavy-duty construction, high cutting forces",
       pageNumber: "D14",
     },
     {
       id: "right-angle-007",
       name: "AHUB Right Angle Shoulder Shell Face Milling Cutter",
       image: "/images/D16-1.png",
       description: "Economical inserts with high applicability and excellent chip evacuation",
       series: "AHUB Series",
       insertType: "JDMT1505..",
       performance: "High applicability with excellent chip evacuation",
       application: "刀片经济，适用性高，排屑好",
       features: "Economic inserts, versatile application, superior chip removal",
       pageNumber: "D16",
     },
     {
       id: "right-angle-008",
       name: "AP1003 Right angle shoulder face mill",
       image: "/images/D18-2.png",
       description: "Heavy-duty cutting face mill for demanding applications",
       series: "AP1003 Series",
       insertType: "APKT1003.., APKX1003..",
       performance: "Heavy-duty cutting capability",
       application: "强力切削",
       features: "Robust design for heavy cutting operations",
       pageNumber: "D18",
     },
     {
       id: "right-angle-009",
       name: "AL-AP16 Machine-Clamped Right-Angle Shoulder End Milling Cutter Head-Aluminum Highlight",
       image: "/images/D21-1.png",
       description: "Lightweight aluminum body for high-speed, high-efficiency machining",
       series: "AL-AP16 Series",
       insertType: "AP..1604..",
       performance: "Lightweight, high-speed operation",
       application: "铝材壳体，重量轻，高光高效",
       features: "Aluminum construction, reduced weight, high-speed capability",
       pageNumber: "D21",
     },
     {
       id: "right-angle-010",
       name: "BAP Right Angle Shoulder Milling Cutter",
       image: "/images/D22-1.png",
       description: "Large rake angle for light cutting with low resistance",
       series: "BAP Series",
       insertType: "APMT1135.., APMT1604..",
       performance: "Light cutting with minimal resistance",
       application: "大前角，切削轻快，阻力小",
       features: "Large rake angle design, smooth cutting action",
       pageNumber: "D22",
     },
     {
       id: "right-angle-011",
       name: "RAP-75° Face Milling Cutter",
       image: "/images/D22-2.png",
       description: "Cost-saving solution utilizing recycled inserts",
       series: "RAP-75° Series",
       insertType: "APMT1604..",
       performance: "Cost-effective with recycled inserts",
       application: "使用废弃刀片，节约成本",
       features: "Utilizes waste inserts, cost-saving design",
       pageNumber: "D22",
     },
     {
       id: "right-angle-012",
       name: "R390 Right Angle Shoulder Milling Cutter",
       image: "/images/D24-1.png",
       description: "High-precision cutter compatible with precision-ground inserts",
       series: "R390 Series",
       insertType: "R390-11T3.., R390-1704..",
       performance: "High precision with ground inserts",
       application: "适配精磨刀片，精度高",
       features: "Precision-ground insert compatibility, high accuracy",
       pageNumber: "D24",
     },
     {
       id: "right-angle-013",
       name: "FMP02R-90° Face Milling Cutter",
       image: "/images/D28-1.png",
       description: "Low cutting force with large rake angle for smooth cutting",
       series: "FMP02R Series",
       insertType: "SEET (09T3, 1203)",
       performance: "Low cutting force, smooth operation",
       application: "低切削力，大前角，切削轻快",
       features: "Large rake angle, reduced cutting forces",
       pageNumber: "D28",
     },
     {
       id: "right-angle-014",
       name: "TP Right Angle Shoulder Milling Cutter",
       image: "/images/D31-2.png",
       description: "Traditional insert compatibility for low-cost cutting operations",
       series: "TP Series",
       insertType: "TPKN/TPMR (1103, 1603, 2204)",
       performance: "Low-cost traditional insert system",
       application: "配备传统刀片，切削成本低",
       features: "Traditional insert compatibility, cost-effective",
       pageNumber: "D31",
     },
     {
       id: "right-angle-015",
       name: "SSD90 Right Angle Step Face Milling Cutter",
       image: "/images/D32-1.png",
       description: "General purpose face milling cutter for versatile applications",
       series: "SSD90 Series",
       insertType: "SDMT1204, SDHW1204",
       performance: "General purpose versatility",
       application: "通用面铣 (General face milling)",
       features: "Versatile design for general face milling operations",
       pageNumber: "D32",
     },
     {
       id: "right-angle-016",
       name: "ASX400 Right Angle Step Face Milling Cutter",
       image: "/images/D32-2.png",
       description: "General face milling solution with step design",
       series: "ASX400 Series",
       insertType: "SPMT12T308",
       performance: "Step face milling capability",
       application: "通用面铣 (General face milling)",
       features: "Step design for general face milling applications",
       pageNumber: "D32",
     },
     {
       id: "right-angle-017",
       name: "WFX12 Right Angle Shoulder Face Mill",
       image: "/images/D32-3.png",
       description: "Low cutting force design for smooth and light cutting",
       series: "WFX12 Series",
       insertType: "SOMT120408PDER",
       performance: "Low cutting force operation",
       application: "低切削力，切削轻快",
       features: "Low cutting force design, smooth operation",
       pageNumber: "D32",
     },
     {
       id: "right-angle-018",
       name: "WFX17 Right Angle Shoulder Face Mill",
       image: "/images/D32-4.png",
       description: "Low cutting force face mill for efficient light cutting",
       series: "WFX17 Series",
       insertType: "AXMT170508PEER-G",
       performance: "Efficient light cutting capability",
       application: "低切削力，切削轻快",
       features: "Low cutting force technology, light cutting efficiency",
       pageNumber: "D32",
     },
  ];

  // Technical specifications based on user content
  const technicalSpecs = [
    {
      title: "Insert Systems & Compatibility",
      description: "Supports vast array of industry-standard inserts including LMMU, 4NK(H)T, WNMU (double-sided hexagonal), M6NGU, 3PKT, JDMT, APKT/APKX, APMT, R390, SEET, TP□N, SDMT, SPMT, SOMT, and AXMT. Provides extensive options for grade, geometry, and cost optimization.",
    },
    {
      title: "Cutting Diameter Range & Design Features",
      description: "Complete range from small-diameter end milling heads (Ø30mm on 4NKT) to large-scale face milling discs (up to Ø315mm on FMP02R). Features internal cooling holes, high-rake low-force geometry, high rigidity body design, and large chip gullets for superior performance.",
    },
    {
      title: "Precision & Performance Specifications",
      description: "Several cutters (3PTF, AP1003) boast exceptional insert runout accuracy within 5μm between blades. Key design features include tapered cone designs for maximum rigidity, screw-clamping for large chip spaces, and high-precision machining capabilities for smooth finishes.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Right Angle Face Milling Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG Right Angle Face Milling Cutters
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  MZG present a detailed introduction to our comprehensive portfolio of Right Angle Face Milling Cutters. These tools are the workhorses of the modern machine shop, fundamental for a vast array of operations ranging from initial roughing of raw stock to the creation of high-precision, perpendicular shoulders. Our system is meticulously engineered to offer a tiered solution for every conceivable need, balancing power, speed, precision, and economy to maximize productivity and part quality across all machining environments.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/right-angle-face-milling-system.png"
                    alt="MZG Professional Right Angle Face Milling Cutters System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">System Performance Analysis</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of our Right Angle Face Milling Cutter system is best understood by its application-specific strengths, which showcase a deep engineering focus on solving distinct manufacturing challenges.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      Heavy-Duty and High-Efficiency Roughing Performance: For applications demanding aggressive material removal, our 3PTF and AP1003 series deliver exceptional performance. These cutters are engineered for heavy cutting, capable of handling a rotary and feed volume that is three times that of a normal cutter. The performance is derived from an extremely robust cutter body, a highly secure insert clamping system, and super-strong inserts (3PKT/APKT) that can withstand immense cutting forces. Similarly, the LN11R/LN16R series are designed for "quasi-heavy cutting," providing a powerful, stable platform for significant stock removal with excellent process security.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      Extreme High-Feed Milling Performance: When the primary performance metric is the metal removal rate (MRR), our R217.96 and MFWN series are the definitive solution. These cutters utilize a high-feed milling strategy, which involves a shallow axial depth of cut (Ap) but an extremely aggressive feed per tooth (Fz). This directs the cutting forces axially up into the machine spindle, the most stable direction, allowing for phenomenal feed rates. The use of economical, double-sided hexagonal WNMU inserts provides multiple cutting edges per insert, drastically lowering the cost-per-part while maximizing machine throughput.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      Versatility and Multi-Application Performance: Many of our cutters are designed as "Swiss Army Knife" solutions, delivering excellent performance across a wide range of operations. The AHUB series is a prime example, capable of roughing, pocket milling, slotting, plunge milling, and plane finishing with a single tool. Its performance is enabled by a large chip space for unimpeded chip flow and a rigid, tapered body design.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Insert Systems: LMMU, 4NK(H)T, WNMU, 3PKT, APKT/APKX, R390</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Diameter Range: Ø30mm to Ø315mm</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Precision: Insert runout accuracy within 5μm</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Cooling: Internal cooling holes on high-performance models</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Performance: 3x normal cutting volume capability</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                          <span className="text-gray-900 text-right">{product.series}</span>
                        </div>
                      )}
                      {product.insertType && (
                        <div>
                          <span className="font-medium text-gray-700">Insert:</span>
                          <span className="text-gray-900 block mt-1">{product.insertType}</span>
                        </div>
                      )}
                      {product.description && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.description}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultRightAngleImages[0]}
                    alt="Right Angle Face Milling Cutter Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                    </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultRightAngleImages[imageIndex % defaultRightAngleImages.length]
                  : defaultRightAngleImages[index % defaultRightAngleImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Right Angle Face Milling Cutter Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                    </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Insert Systems & Compatibility":
                      return <Layers className="h-6 w-6 text-blue-600 mr-3" />
                    case "Cutting Diameter Range & Design Features":
                      return <Settings className="h-6 w-6 text-green-600 mr-3" />
                    case "Precision & Performance Specifications":
                      return <Target className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                    </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios and Applicable Machining</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Heavy Roughing:</strong> LN11R/16R and 3PTF cutters ideal for initial machining of large cast iron engine blocks, steel forgings, and pre-hardened mold bases</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Speed Face Milling:</strong> MFWN and R217.96 cutters used in high-production environments to rapidly machine flat surfaces where cycle time is critical</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>True 90° Shoulder Milling:</strong> R390, AHUB, and BAP series frequently used to create precise, perpendicular walls in pockets and part peripheries</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Fine Finishing:</strong> MA-AP and R390 tools of choice for final pass on critical surfaces like mold parting lines or machine-mating surfaces</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Aluminum Component Machining:</strong> AL-AP16 specifically applied in high-speed machining of aluminum alloys for automotive, electronic, and aerospace components</span>
                  </li>
                </ul>
              </div>

              {/* Machining Operations */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Machining Operations
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Versatile Job Shop Machining:</strong> FMP02R and AHUB invaluable in job shops where single tool must perform multiple tasks</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Plunge Milling Operations:</strong> Opening pockets followed by shoulder milling and face milling to finish parts</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Production Face Milling:</strong> Automotive and aerospace industries demanding rapid material removal with superior surface quality</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Pocket Milling and Slotting:</strong> Large chip space design prevents chip packing during heavy cutting operations</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Mirror Finish Operations:</strong> MA-AP with PCD finishing tools for critical surface applications</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Primary Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Efficiently Remove Material:</strong> Primary function to machine flat surfaces with highest possible metal removal rate through heavy cuts or high-feed strategies</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Create Precise 90-Degree Shoulders:</strong> Machine perfectly perpendicular interface between horizontal face and vertical wall</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Comprehensive Application Versatility:</strong> Function as multi-purpose tool for face milling, shoulder milling, slotting, and plunging</span>
                  </li>
                </ul>
                  </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  Performance Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Enhance Surface Quality and Precision:</strong> Deliver superior surface finishes and tight dimensional tolerances with specialized finishing operations</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Lower Overall Machining Costs:</strong> Reduce cost-per-part through economical multi-edge inserts and maximized machine productivity</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Maximum Tool Efficiency:</strong> Reduced tool changes and inventory through multi-functional capability and extended tool life</span>
                  </li>
                </ul>
                </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/clamp-type-milling/right-angle-square-shoulder" />
            </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional Right Angle Face Milling Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal right angle face milling cutters for specific applications from heavy roughing to precision finishing. We provide comprehensive solutions for all materials and machining requirements with tiered performance options.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same clamp-type-milling directory
                const allClampTypeMillingCategories = [
                  {
                    title: "Ball End Milling Cutters",
                    image: "/images/ball-end-milling-cutter.png",
                    description: "3D contouring and curved surface machining",
                    url: "/standard-tools/clamp-type-milling/ball-end-milling-cutters",
                  },
                  {
                    title: "Chamfering Cutters",
                    image: "/images/chamfering-cutter.png",
                    description: "Precision chamfering and beveling",
                    url: "/standard-tools/clamp-type-milling/chamfering-cutters",
                  },
                  {
                    title: "Corn Roughing",
                    image: "/images/corn-roughing-cutter.png",
                    description: "High material removal roughing",
                    url: "/standard-tools/clamp-type-milling/corn-roughing",
                  },
                {
                  title: "Face Milling Cutters",
                    image: "/images/face-milling-cutter.png",
                    description: "Large surface face milling",
                  url: "/standard-tools/clamp-type-milling/face-milling-cutters",
                },
                {
                  title: "High Feed Milling Cutter",
                    image: "/images/high-feed-milling-cutter.png",
                    description: "High-speed feed operations",
                  url: "/standard-tools/clamp-type-milling/high-feed-milling-cutter",
                },
                ];
                
                return allClampTypeMillingCategories.slice(0, 5).map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Clamp Type Milling" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
} 