'use client'

import { useEffect, useState } from 'react'

export function StagewiseInit() {
  const [enableStagewise, setEnableStagewise] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    // 只在开发模式下显示控制按钮
    if (process.env.NODE_ENV === 'development') {
      console.log('🚀 StagewiseInit: 开发模式检测到，初始化 Stagewise 控制按钮')
      
      // 检查是否已存在按钮，避免重复创建
      let controlButton = document.getElementById('stagewise-toggle') as HTMLButtonElement
      
      if (!controlButton) {
        // 创建控制按钮
        controlButton = document.createElement('button')
        controlButton.id = 'stagewise-toggle'
        controlButton.innerText = '启用 Stagewise'
        controlButton.style.cssText = `
          position: fixed;
          bottom: 20px;
          right: 20px;
          background: #3b82f6;
          color: white;
          border: none;
          padding: 10px 15px;
          border-radius: 6px;
          cursor: pointer;
          z-index: 999998;
          font-size: 12px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          box-shadow: 0 2px 8px rgba(0,0,0,0.15);
          transition: all 0.2s ease;
        `
        
        // 添加悬停效果
        controlButton.onmouseenter = () => {
          controlButton.style.transform = 'translateY(-1px)'
          controlButton.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)'
        }
        
        controlButton.onmouseleave = () => {
          controlButton.style.transform = 'translateY(0)'
          controlButton.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)'
        }
        
        controlButton.onclick = () => {
          if (!enableStagewise) {
            console.log('🎯 启用 Stagewise 工具栏')
            setEnableStagewise(true)
            controlButton.innerText = '禁用 Stagewise'
            controlButton.style.background = '#ef4444'
          } else {
            console.log('🛑 禁用 Stagewise 工具栏')
            setEnableStagewise(false)
            controlButton.innerText = '启用 Stagewise'
            controlButton.style.background = '#3b82f6'
            // 移除 stagewise 容器
            const container = document.getElementById('stagewise-root')
            if (container) {
              container.remove()
              console.log('🧹 Stagewise 容器已移除')
            }
            setIsLoaded(false)
          }
        }
        
        document.body.appendChild(controlButton)
        console.log('✅ Stagewise 控制按钮已创建')
      }

      return () => {
        const button = document.getElementById('stagewise-toggle')
        if (button) {
          button.remove()
          console.log('🧹 清理 Stagewise 控制按钮')
        }
      }
    }
  }, [])

  useEffect(() => {
    if (enableStagewise && process.env.NODE_ENV === 'development' && !isLoaded) {
      console.log('📦 开始加载 Stagewise 工具栏...')
      
      const timer = setTimeout(() => {
        import('@stagewise/toolbar-next')
          .then((module) => {
            console.log('✅ Stagewise 模块加载成功')
            const StagewiseToolbar = module.StagewiseToolbar
            
            // 移除旧容器
            const oldContainer = document.getElementById('stagewise-root')
            if (oldContainer) {
              oldContainer.remove()
              console.log('🧹 清理旧的 Stagewise 容器')
            }
            
            // 创建新容器
            const container = document.createElement('div')
            container.id = 'stagewise-root'
            container.style.cssText = `
              position: fixed !important;
              top: 0 !important;
              left: 0 !important;
              width: 100% !important;
              height: 100% !important;
              pointer-events: none !important;
              z-index: 999999 !important;
              isolation: isolate !important;
              contain: layout style paint !important;
              transform: translateZ(0) !important;
              will-change: transform !important;
            `
            document.body.appendChild(container)
            console.log('📍 Stagewise 容器已创建')
            
            import('react-dom/client').then(({ createRoot }) => {
              const root = createRoot(container)
              root.render(
                <div style={{ pointerEvents: 'auto' }}>
                  <StagewiseToolbar 
                    config={{ 
                      plugins: []
                    }} 
                  />
                </div>
              )
              setIsLoaded(true)
              console.log('🎉 Stagewise 工具栏已成功加载')
              console.log('💡 您现在可以点击页面元素进行选择和注释')
            }).catch((error) => {
              console.error('❌ React DOM 加载失败:', error)
            })
          })
          .catch((error) => {
            console.error('❌ Stagewise 模块加载失败:', error)
            console.log('🔧 请检查 @stagewise/toolbar-next 包是否正确安装')
            
            // 重置按钮状态
            const button = document.getElementById('stagewise-toggle') as HTMLButtonElement
            if (button) {
              button.innerText = '启用 Stagewise'
              button.style.background = '#3b82f6'
            }
            setEnableStagewise(false)
          })
      }, 800) // 稍微延迟确保页面完全加载

      return () => clearTimeout(timer)
    }
  }, [enableStagewise, isLoaded])

  // 显示开发模式提示
  if (process.env.NODE_ENV === 'development') {
    return (
      <div
        style={{
          position: 'fixed',
          bottom: '20px',
          left: '20px',
          background: 'rgba(59, 130, 246, 0.1)',
          border: '1px solid rgba(59, 130, 246, 0.3)',
          borderRadius: '6px',
          padding: '8px 12px',
          fontSize: '11px',
          color: '#3b82f6',
          zIndex: 999997,
          fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
          backdropFilter: 'blur(4px)',
          pointerEvents: 'none',
          userSelect: 'none'
        }}
      >
        🚀 开发模式 {enableStagewise ? '| Stagewise 已启用' : '| 右下角启用 Stagewise'}
      </div>
    )
  }

  return null
} 