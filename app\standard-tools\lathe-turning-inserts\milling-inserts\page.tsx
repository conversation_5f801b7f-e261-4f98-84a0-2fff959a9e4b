"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, RotateCw, Wrench } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function MillingInsertsPage() {
  // Milling Inserts相关的默认图片
  const defaultMillingImages = [
    "/images/apkt-1604-insert.png",
    "/images/rdkw-1204-insert.png", 
    "/images/ofkn-0804-insert.png",
    "/images/lnmu-0604-insert.png",
    "/images/sdkn-1204-insert.png",
    "/images/rckt-1204-insert.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/lathe-turning-inserts/milling-inserts");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认Milling Inserts图片
          setGalleryImages(defaultMillingImages);
        }
      } else {
        // API请求失败，使用默认Milling Inserts图片
        setGalleryImages(defaultMillingImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认Milling Inserts图片
      setGalleryImages(defaultMillingImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认Milling Inserts图片，避免显示无关图片
    setGalleryImages(defaultMillingImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data based on various milling insert geometries and grades
  const products = [
    {
      id: "mi-001",
      name: "WNMU",
      image: "/images/a75-1.png",
      description: "High Feed Milling Inserts",
      series: "WNMU Series",
      geometry: "High Feed",
      edges: "Multiple",
      application: "High Feed Milling",
      materials: "Steel, Stainless Steel, Cast Iron",
      pageNumber: "A75",
      specifications: "WNMU04046.35, WNMU080604EN-GM, WNMU080608EN-GM, WNMU080608EN-SM, WNMU080608EN-GH"
    },
    {
      id: "mi-002",
      name: "M6NGU",
      image: "/images/a75-2.png",
      description: "Right Angle Milling Inserts",
      series: "M6NGU Series",
      geometry: "Right Angle",
      edges: "Multiple",
      application: "Right Angle Milling",
      materials: "Steel, Cast Iron, Alloy Steel",
      pageNumber: "A75",
      specifications: "M6NGU060404R-M, M6NGU060405R-M, M6NGU060408R-M, M6NGU060410R-M, M6NGU060416R-M, M6NGU090504R-M, M6NGU090508R-M, M6NGU090516R-M"
    },
    {
      id: "mi-003",
      name: "3PKT",
      image: "/images/a75-3.png",
      description: "Right Angle Milling Inserts",
      series: "3PKT Series",
      geometry: "Right Angle",
      edges: "3",
      application: "Right Angle Milling",
      materials: "Steel, Cast Iron, General Purpose",
      pageNumber: "A75",
      specifications: "3PKT100404R-M, 3PKT100408R-M, 3PKT150508R-M, 3PKT190608R-M, 3PKT190616R-M, 3PKT100404R-ML, 3PKT150508R-ML, 3PKT190608R-ML"
    },
    {
      id: "mi-004",
      name: "3PHT",
      image: "/images/a75-4.png",
      description: "Right Angle Milling Inserts",
      series: "3PHT Series",
      geometry: "Right Angle",
      edges: "3",
      application: "Right Angle Milling",
      materials: "Aluminum, Non-ferrous Alloys",
      pageNumber: "A75",
      specifications: "3PHT100404R-AL, 3PHT100408R-AL, 3PHT150508R-AL, 3PHT190608R-AL"
    },
    {
      id: "mi-005",
      name: "JDMT",
      image: "/images/a75-5.png",
      description: "Right Angle Milling Inserts",
      series: "JDMT Series",
      geometry: "Right Angle",
      edges: "Multiple",
      application: "Right Angle Milling",
      materials: "Steel, Cast Iron, Hardened Materials",
      pageNumber: "A75",
      specifications: "JDMT100304R, JDMT100308R, JDMT100320R, JDMT100332R, JDMT150504R, JDMT150508R, JDMT150520R, JDMT150530R"
    },
    {
      id: "mi-006",
      name: "EDMT",
      image: "/images/a75-6.png",
      description: "Right Angle Milling Inserts",
      series: "EDMT Series",
      geometry: "Right Angle",
      edges: "Multiple",
      application: "Right Angle Milling",
      materials: "Steel, Stainless Steel, Cast Iron",
      pageNumber: "A75",
      specifications: "EDMT070202R, EDMT070204R, EDMT070208R, EDMT070220R-T, EDMT070220R"
    },
    {
      id: "mi-007",
      name: "APKT",
      image: "/images/a75-7.png",
      description: "Right Angle Milling Inserts",
      series: "APKT Series",
      geometry: "Square",
      edges: "4",
      application: "Right Angle Milling, Shoulder Milling",
      materials: "Steel, Stainless Steel, Aluminum",
      pageNumber: "A76",
      specifications: "APKT1003PDR-HM, APKT1003PDTR-76, APKT1003PDTR-RM, APKT100308PDTR-RM, APKT100310PDR-RM, APKT100312TR-RM, APKT100316PDTR-RM, APKT100320PDTR-RM, APKT1135PDER-G2, APKT1604PDER-G2, APKT160402-AL, APKT160404-AL, APKT160408-AL, APKT113502, APKT113504, APKT113508, APKT160402, APKT160404, APKT160408"
    },
    {
      id: "mi-008",
      name: "APMT",
      image: "/images/a76-1.png",
      description: "Right Angle Milling Inserts",
      series: "APMT Series",
      geometry: "Square",
      edges: "4",
      application: "Right Angle Milling, Heavy Duty",
      materials: "Steel, Cast Iron, Hardened Steel",
      pageNumber: "A76",
      specifications: "R390-11T304M-PM, R390-11T308M-PM, R390-11T316M-PM, R390-11T331M-PM, R390-170404M-PM, R390-170408M-PM, R390-170416M-PM, R390-170431M-PM, APMT1135PDER-M2, APMT1604PDER-M2, APMT1135PDER-DU, APMT1604PDER-DU, APMT1135PDER-KZ, APMT1604PDER-KZ, APMT1135PDER-ZM, APMT1604PDER-ZM, APMT1135PDER-H1, APMT1135PDER-H2, APMT1135PDER-H3, APMT1135PDER-H4, APMT1135PDER-H6, APMT1604PDER-H1, APMT1604PDER-H2, APMT1604PDER-H3, APMT1604PDER-H4, APMT1604PDER-H6, APMT1604PDER-H8"
    },
    {
      id: "mi-009",
      name: "RPGT",
      image: "/images/a78-1.png",
      description: "Fillet Milling Inserts",
      series: "RPGT Series",
      geometry: "Round",
      edges: "Multiple",
      application: "Fillet Milling",
      materials: "Aluminum, Non-ferrous Materials",
      pageNumber: "A78",
      specifications: "RPGT08T2-AL, RPGT1003-AL, RPGT10T3-AL, RPGT1204-AL"
    },
    {
      id: "mi-010",
      name: "RPEW",
      image: "/images/a78-2.png",
      description: "Fillet Milling Inserts",
      series: "RPEW Series",
      geometry: "Round",
      edges: "Multiple",
      application: "Fillet Milling",
      materials: "Steel, Cast Iron, General Purpose",
      pageNumber: "A78",
      specifications: "RPEW0802MO, RPEW1003MO, RPEW1003MO-DU, RPEW1204MOF"
    },
    {
      id: "mi-011",
      name: "RPKT",
      image: "/images/a78-3.png",
      description: "Fillet Milling Inserts",
      series: "RPKT Series",
      geometry: "Round",
      edges: "Multiple",
      application: "Fillet Milling",
      materials: "Steel, Stainless Steel, Cast Iron",
      pageNumber: "A78",
      specifications: "RPKT10T3MOF, RPKT1204MOF, RPKT10T3MOT, RPKT1204MOT"
    },
    {
      id: "mi-012",
      name: "RPMT",
      image: "/images/a78-4.png",
      description: "Fillet Milling Inserts",
      series: "RPMT Series",
      geometry: "Round",
      edges: "Multiple",
      application: "Fillet Milling",
      materials: "Steel, Stainless Steel, Hardened Steel",
      pageNumber: "A78",
      specifications: "RPMT08T2MOE-JS, RPMT1003MOE-JS, RPMT10T3MOE-JS, RPMT1204MOE-JS, RPMT10T3MO-DU, RPMT1204MO-DU, RPMT08T2MO-GH, RPMT10T3MO-GH, RPMT1204MO-GH"
    },
    {
      id: "mi-013",
      name: "RCGT",
      image: "/images/a78-5.png",
      description: "Fillet Milling Inserts",
      series: "RCGT Series",
      geometry: "Round",
      edges: "Multiple",
      application: "Fillet Milling",
      materials: "Aluminum, Non-ferrous Alloys",
      pageNumber: "A79",
      specifications: "RCGT0602-AL, RCGT0803-AL, RCGT1003-AL, RCGT10T3-AL, RCGT1204-AL"
    },
    {
      id: "mi-014",
      name: "RCKT",
      image: "/images/a78-6.png",
      description: "Fillet Milling Inserts",
      series: "RCKT Series",
      geometry: "Round",
      edges: "Multiple",
      application: "Fillet Milling, 3D Profiling",
      materials: "Steel, Cast Iron, Hardened Materials",
      pageNumber: "A79",
      specifications: "RCKT10T3MO-PM, RCKT1204MO-PM, RCKT1606MO-PM, RCKT2006MO-PM, RCKT10T3MO-PR, RCKT1204MO-PR, RCKT1606MO-PR, RCKT2006MO-PR"
    },
    {
      id: "mi-015",
      name: "TPKN",
      image: "/images/a77-8.png",
      description: "Surface Milling/Chamfering Inserts",
      series: "TPKN Series",
      geometry: "Triangular",
      edges: "3",
      application: "Surface Milling, Chamfering",
      materials: "Steel, Cast Iron, General Purpose",
      pageNumber: "A78",
      specifications: "TPKN110304, TPKN110308, TPKN160304, TPKN160308, TPKN220404, TPKN220408, TPKN220412, TPKN2204PDR"
    },
    {
      id: "mi-016",
      name: "TPMR",
      image: "/images/a77-9.png",
      description: "Surface Milling/Chamfering Inserts",
      series: "TPMR Series",
      geometry: "Triangular",
      edges: "3",
      application: "Surface Milling, Chamfering",
      materials: "Steel, Stainless Steel, Cast Iron",
      pageNumber: "A78",
      specifications: "TPMR090204, TPMR110304, TPMR110308, TPMR160304, TPMR160308, TPMR160312, TPMR220412, TPMR330916"
    },
    {
      id: "mi-017",
      name: "ZPFG",
      image: "/images/a80-3.png",
      description: "Ball Head Profiling Finish Milling Inserts",
      series: "ZPFG Series",
      geometry: "Ball Nose",
      edges: "Multiple",
      application: "Ball Head Profiling Finish Milling",
      materials: "Steel, Hardened Steel, Superalloys",
      pageNumber: "A80",
      specifications: "ZPFG080, ZPFG100, ZPFG120, ZPFG160, ZPFG200, ZPFG250, ZPFG300, ZPFG320"
    },
    {
      id: "mi-018",
      name: "BNM",
      image: "/images/a80-5.png",
      description: "Ball Head Profiling Finish Milling Inserts",
      series: "BNM Series",
      geometry: "Ball Nose",
      edges: "Multiple",
      application: "Ball Head Profiling Finish Milling",
      materials: "Steel, Stainless Steel, Hardened Steel",
      pageNumber: "A81",
      specifications: "BNM060, BNM070, BNM080, BNM100, BNM110, BNM120, BNM160, BNM200, BNM250, BNM300, BNM320"
    },
    {
      id: "mi-019",
      name: "JOMW",
      image: "/images/a82-3.png",
      description: "High Feed Milling Inserts",
      series: "JOMW Series",
      geometry: "High Feed",
      edges: "Multiple",
      application: "High Feed Milling",
      materials: "Steel, Cast Iron, Hardened Materials",
      pageNumber: "A82",
      specifications: "JOMW06T215ZZSR-FT, JOMW080320ZZSR-FT, JOMW09T320ZDSR-FT, JOMW120420ZDSR-FT, JOMW140520ZDSR-FT"
    },
    {
      id: "mi-020",
      name: "BLMP",
      image: "/images/a83-4.png",
      description: "High Feed Milling Inserts",
      series: "BLMP Series",
      geometry: "High Feed",
      edges: "Multiple",
      application: "High Feed Milling",
      materials: "Steel, Stainless Steel, Cast Iron",
      pageNumber: "A83",
      specifications: "BLMP0603R-M6.39, BLMP1205R-M"
    },
    {
      id: "mi-021",
      name: "LNMU",
      image: "/images/a83-5.png",
      description: "High Feed Milling Inserts",
      series: "LNMU Series",
      geometry: "High Feed",
      edges: "Multiple",
      application: "High Feed Milling",
      materials: "Steel, Stainless Steel, Cast Iron",
      pageNumber: "A83",
      specifications: "LNMU0303ZER-MJ, LNMU0303ZER-ML"
    },
    {
      id: "mi-022",
      name: "RT",
      image: "/images/a77-1.png",
      description: "Right Angle Milling Inserts",
      series: "RT Series",
      geometry: "Right Angle",
      edges: "Multiple",
      application: "Right Angle Milling",
      materials: "Steel, Cast Iron, General Purpose",
      pageNumber: "A77",
      specifications: "RT070204R-81, RT100304R-81, RT100304R-31, RT100308R-81"
    },
    {
      id: "mi-023",
      name: "XOMX",
      image: "/images/a77-2.png",
      description: "Right Angle Milling Inserts",
      series: "XOMX Series",
      geometry: "Right Angle",
      edges: "Multiple",
      application: "Right Angle Milling",
      materials: "Steel, Stainless Steel, Cast Iron",
      pageNumber: "A77",
      specifications: "XOMX060208FR-E03, XOMX060208R-M05, XOMX090308FR-E05, XOMX120408TR-M06"
    },
    {
      id: "mi-024",
      name: "AOMT",
      image: "/images/a77-3.png",
      description: "Right Angle Milling Inserts",
      series: "AOMT Series",
      geometry: "Right Angle",
      edges: "Multiple",
      application: "Right Angle Milling",
      materials: "Steel, Cast Iron, Hardened Materials",
      pageNumber: "A77",
      specifications: "AOMT123602PEER-M, AOMT123604PEER-M, AOMT123608PEER-M, AOMT123612PEER-M, AOMT123616PEER-M, AOMT123620PEER-M, AOMT123624PEER-M, AOMT123632PEER-M"
    },
    {
      id: "mi-025",
      name: "SEET",
      image: "/images/a77-4.png",
      description: "Right Angle Milling Inserts",
      series: "SEET Series",
      geometry: "Right Angle",
      edges: "Multiple",
      application: "Right Angle Milling",
      materials: "Steel, Cast Iron, General Purpose",
      pageNumber: "A77",
      specifications: "SEET09T308PER-PF, SEET09T308PER-PM, SEET09T308PER-PR, SEET120308PER-PF, SEET120308PER-PM, SEET120308PER-PR"
    },
    {
      id: "mi-026",
      name: "APET",
      image: "/images/a77-5.png",
      description: "Right Angle Milling Inserts",
      series: "APET Series",
      geometry: "Right Angle",
      edges: "Multiple",
      application: "Right Angle Milling",
      materials: "Steel, Stainless Steel, Cast Iron",
      pageNumber: "A77",
      specifications: "APET120202SR, APET120204SR, APET120208SR, APET160302SR, APET160304SR, APET160308SR"
    },
    {
      id: "mi-027",
      name: "AXMT",
      image: "/images/a77-6.png",
      description: "Right Angle Milling Inserts",
      series: "AXMT Series",
      geometry: "Right Angle",
      edges: "Multiple",
      application: "Right Angle Milling",
      materials: "Steel, Stainless Steel, Cast Iron",
      pageNumber: "A77",
      specifications: "AXMT0602PER-EM, AXMT060204R-EM, AXMT060208R-EM, AXMT060216R-EM, AXMT0903PER-ML, AXMT0903PER-EML, AXMT090308R-EML, AXMT090316R-ML, AXMT090320R-EML, AXMT090332R-EML"
    },
    {
      id: "mi-028",
      name: "AXCT",
      image: "/images/a77-7.png",
      description: "Right Angle Milling Inserts",
      series: "AXCT Series",
      geometry: "Right Angle",
      edges: "Multiple",
      application: "Right Angle Milling",
      materials: "Aluminum, Non-ferrous Alloys",
      pageNumber: "A77",
      specifications: "AXCT060202R-AL, AXCT060204R-AL, AXCT0903PER-AL"
    },
    {
      id: "mi-029",
      name: "RCMT",
      image: "/images/a79-1.png",
      description: "Fillet Milling Inserts",
      series: "RCMT Series",
      geometry: "Round",
      edges: "Multiple",
      application: "Fillet Milling",
      materials: "Steel, Cast Iron, General Purpose",
      pageNumber: "A79",
      specifications: "RCMT0803MO, RCMT1003MO, RCMT10T3MO, RCMT1204MO, RCMT1606MO, RCMT2006MO"
    },
    {
      id: "mi-030",
      name: "RCMX",
      image: "/images/a79-2.png",
      description: "Fillet Milling Inserts",
      series: "RCMX Series",
      geometry: "Round",
      edges: "Multiple",
      application: "Fillet Milling",
      materials: "Steel, Cast Iron, Hardened Materials",
      pageNumber: "A79",
      specifications: "RCMX0803MO, RCMX1003MO, RCMX10T3MO, RCMX1204MO, RCMX1606MO, RCMX2006MO, RCMX2507MO, RCMX3209MO"
    },
    {
      id: "mi-031",
      name: "RDKW",
      image: "/images/a79-3.png",
      description: "Fillet Milling Inserts",
      series: "RDKW Series",
      geometry: "Round",
      edges: "Multiple",
      application: "Fillet Milling",
      materials: "Steel, Cast Iron, Hardened Materials",
      pageNumber: "A79",
      specifications: "RDKW0501MO, RDKW0702MO, RDKW0803MO, RDKW10T3MO, RDKW1204MO, RDKW1604MO, RDKW1605MO, RDKW1606MO, RDKW2006MO"
    },
    {
      id: "mi-032",
      name: "TCMT",
      image: "/images/a81-5.png",
      description: "Chamfering Inserts",
      series: "TCMT Series",
      geometry: "Triangular",
      edges: "3",
      application: "Chamfering",
      materials: "Steel, Cast Iron, General Purpose",
      pageNumber: "A82",
      specifications: "TCMT090204, TCMT110204, TCMT16T304"
    },
    {
      id: "mi-033",
      name: "SPMG",
      image: "/images/a82-1.png",
      description: "Milling/Drilling Inserts",
      series: "SPMG Series",
      geometry: "Special",
      edges: "Multiple",
      application: "Milling, Drilling",
      materials: "Steel, Stainless Steel, Cast Iron",
      pageNumber: "A82, A85",
      specifications: "SPMG050204-TT, SPMG060204-TT, SPMG07T308-TT, SPMG090408-TT, SPMG110408-TT, SPMG140512-TT, SPMG050204-PM, SPMG060204-PM, SPMG07T308-PM, SPMG090408-PM, SPMG110408-PM, SPMG140512-PM"
    },
    {
      id: "mi-034",
      name: "HNGX",
      image: "/images/a83-3.png",
      description: "Surface Milling Inserts",
      series: "HNGX Series",
      geometry: "Hexagonal",
      edges: "6",
      application: "Surface Milling",
      materials: "Steel, Cast Iron, General Purpose",
      pageNumber: "A83",
      specifications: "HNGX0906ANSN-M, HNGX0906ANSN-R"
    },
    {
      id: "mi-035",
      name: "SEKT",
      image: "/images/a83-8.png",
      description: "Surface Milling Inserts",
      series: "SEKT Series",
      geometry: "Square",
      edges: "4",
      application: "Surface Milling",
      materials: "Steel, Cast Iron, General Purpose",
      pageNumber: "A83",
      specifications: "SEKT1204AFTN"
    },
    {
      id: "mi-036",
      name: "SEHT",
      image: "/images/a84-1.png",
      description: "Surface Milling Inserts",
      series: "SEHT Series",
      geometry: "Square",
      edges: "4",
      application: "Surface Milling",
      materials: "Steel, Cast Iron, Hardened Materials",
      pageNumber: "A84",
      specifications: "SEHT1204AFSN, SEHT1204AFTN, SEHT1204-AK, SEHT1204"
    },
    {
      id: "mi-037",
      name: "SDKN",
      image: "/images/a84-2.png",
      description: "Surface Milling Inserts",
      series: "SDKN Series",
      geometry: "Square",
      edges: "4",
      application: "Surface Milling",
      materials: "Steel, Cast Iron, General Purpose",
      pageNumber: "A84",
      specifications: "SDKN1203AETN, SDKN1504AETN"
    },
    {
      id: "mi-038",
      name: "ZCFW",
      image: "/images/a80-4.png",
      description: "Right Angle Finish Milling Inserts",
      series: "ZCFW Series",
      geometry: "Corner Radius",
      edges: "Multiple",
      application: "Right Angle Finish Milling",
      materials: "Steel, Stainless Steel, Hardened Steel",
      pageNumber: "A80",
      specifications: "ZCFW080-R0.3, ZCFW100-R0.3, ZCFW100-R0.5, ZCFW100-R1.0, ZCFW120-R0.3, ZCFW120-R0.5, ZCFW120-R1.0, ZCFW160-R0.3, ZCFW160-R0.5, ZCFW160-R1.0, ZCFW160-R1.5, ZCFW160-R2.0, ZCFW200-R0.3, ZCFW200-R0.5, ZCFW200-R1.0, ZCFW200-R1.5, ZCFW200-R2.0, ZCFW250-R0.3, ZCFW250-R0.5, ZCFW250-R1.0, ZCFW250-R1.5, ZCFW250-R2.0, ZCFW300-R0.3, ZCFW300-R0.5, ZCFW300-R1.0, ZCFW320-R0.3, ZCFW320-R0.5, ZCFW320-R1.0"
    },
    {
      id: "mi-039",
      name: "WPGT",
      image: "/images/a83-2.png",
      description: "High Feed Milling Inserts",
      series: "WPGT Series",
      geometry: "High Feed",
      edges: "Multiple",
      application: "High Feed Milling",
      materials: "Steel, Stainless Steel, Cast Iron",
      pageNumber: "A83",
      specifications: "WPGT050315ZSR, WPGT060415ZSR, WPGT080615ZSR, WPGT090725ZSR, WPGT050315ZSR-PM, WPGT060415ZSR-PM, WPGT080615ZSR-PM, WPGT090725ZSR-PM"
    },
    {
      id: "mi-040",
      name: "CCMT",
      image: "/images/a81-2.png",
      description: "Surface Milling/Chamfering Inserts",
      series: "CCMT Series",
      geometry: "Rhombic",
      edges: "4",
      application: "Surface Milling, Chamfering",
      materials: "Steel, Cast Iron, General Purpose",
      pageNumber: "A81",
      specifications: "CCMT060204, CCMT09T304, CCMT09T308, CCMT090308, CCMT120408, CCMT120430"
    }
  ]

  // Performance features
  const performanceFeatures = [
    {
      icon: "RotateCw",
      title: "Maximum Metal Removal Rate",
      description:
        "Innovative high-feed geometries, strong negative-rake inserts, and advanced coatings enable extreme feed rates and higher cutting speeds for faster chip making.",
    },
    {
      icon: "Shield",
      title: "Exceptional Process Security",
      description:
        "Ultra-fine grain carbide substrates with proprietary PVD/CVD coatings provide long, predictable tool life and reliable unattended machining capabilities.",
    },
    {
      icon: "Target",
      title: "Superior Surface Finish",
      description:
        "Specialized wiper flat geometries strategically positioned on inserts effectively smooth surfaces, allowing doubled feed rates while achieving better finishes.",
    },
  ]

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Insert Geometries & Shapes",
      description:
        "Complete range from square (4/8 edges) and round (strongest edge) to high-feed (extreme rates) and octagonal (economical) geometries for all applications.",
    },
    {
      title: "Cutting Edge Geometry",
      description:
        "Full range of positive-rake (lower forces, stainless/superalloys) and negative-rake (stronger edge, cast iron/interrupted cuts) geometries.",
    },
    {
      title: "Advanced Material Grades",
      description:
        "Five specialized series: ZC (CVD steel), ZP (PVD stainless), ZK (CVD cast iron), ZH (CBN hardened), ZL (PCD aluminum) for optimal performance.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  CNC Milling Inserts Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG CNC Milling Inserts
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  MZG CNC Milling Inserts. These inserts are the critical cutting element in all indexable milling systems, representing the culmination of advanced metallurgy, precision grinding, and application-specific geometric design. Our portfolio is logically structured to provide optimized solutions for every type of milling operation. By understanding the core application—be it creating a perfect 90° shoulder, achieving the highest possible metal removal rates, or sculpting a complex 3D surface—our customers can select the ideal insert geometry and grade to unlock maximum performance and economic efficiency.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/milling-inserts-hero.png"
                    alt="MZG Professional CNC Milling Inserts System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "RotateCw":
                    return <RotateCw className="h-6 w-6 text-blue-600 mr-3" />
                  case "Shield":
                    return <Shield className="h-6 w-6 text-green-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Performance</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of our CNC milling inserts is best understood when categorized by their primary milling application, as each family is engineered to excel in a specific machining strategy.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>High-Efficiency Right Angle (Shoulder) Milling Performance:</strong> This is the most common milling application, and our performance here is defined by precision and versatility. The <strong>APKT</strong> and <strong>APMT</strong> series are the industry workhorses, featuring a strong 11° relief angle and two cutting edges, delivering exceptional performance in creating true 90° shoulders, slotting, and ramping.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Maximum Productivity High-Feed Milling Performance:</strong> When the primary goal is maximizing Metal Removal Rate (MRR), our high-feed inserts offer world-class performance. This strategy utilizes inserts with a large corner radius and a low lead angle, such as the <strong>WNMU</strong>, <strong>JOMW/JDMT</strong>, <strong>BLMP</strong>, and <strong>LNMU</strong> series.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Versatile Fillet and Profile Milling Performance (Round Inserts):</strong> For copy milling, 3D contouring, and creating large corner radii, our family of round inserts delivers exceptional performance. The <strong>RPMT</strong>, <strong>RPKT</strong>, and <strong>RPEW</strong> series are positive rake inserts that offer a strong yet free-cutting action.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3 text-sm text-gray-700">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Insert Shapes:</strong> Square, Round, Octagonal, High-Feed geometries for all applications</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Cutting Edges:</strong> 4, 8, 16, Multiple edges per insert for economic efficiency</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Material Grades:</strong> ZC, ZP, ZK, ZH, ZL Series for optimal performance</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Advanced Coating:</strong> CVD, PVD, CBN, PCD coating technologies</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Applications:</strong> Face, shoulder, slot, high-feed milling operations</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                          <span className="text-gray-900 text-right">{product.series}</span>
                        </div>
                      )}
                      {product.application && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.application}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultMillingImages[0]}
                    alt="Milling Insert Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                  </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultMillingImages[imageIndex % defaultMillingImages.length]
                  : defaultMillingImages[index % defaultMillingImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Milling Insert Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                  </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Insert Geometries & Shapes":
                      return <Layers className="h-6 w-6 text-blue-600 mr-3" />
                    case "Cutting Edge Geometry":
                      return <Shield className="h-6 w-6 text-green-600 mr-3" />
                    case "Advanced Material Grades":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                    </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios & Applicable Machining</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Mold & Die Manufacturing:</strong> Heavy use of high-feed inserts (JOMW, BLMP) for roughing cavities, and a wide array of round (RPMT, RCKT) and ball-nose (ZPFG, BNM) inserts for semi-finishing and finishing complex 3D surfaces.</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Aerospace & Medical:</strong> Machining titanium and high-temperature alloys with sharp, positive geometry inserts (APKT, RCGT) to minimize cutting forces and heat input. High-feed milling is also used extensively.</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Automotive & Heavy Industry:</strong> High-volume production using robust and economical inserts for face milling engine blocks (SEKT, SDKN) and shoulder milling components (APMT).</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>General Engineering & Job Shops:</strong> The extreme versatility of the APKT/APMT family makes it a staple for a wide range of tasks including shoulder milling, slotting, ramping, and helical interpolation.</span>
                  </li>
                </ul>
              </div>

              {/* Machining Operations */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Machining Operations
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Execute Specific Milling Strategies:</strong> To provide an optimized tool for every milling operation, including Right Angle Milling, High-Feed Milling, Fillet/Profiling, Planar Milling, and Finishing.</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Maximize Productivity and Economy:</strong> To dramatically increase Metal Removal Rates through high-feed geometries and reduce cost-per-edge through multi-edge and double-sided insert designs.</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Deliver Precision and Surface Quality:</strong> To produce components with exacting dimensional tolerances and achieve surface finishes that can reduce or eliminate secondary operations.</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Ensure Process Security Across All Materials:</strong> To provide a specific grade and chipbreaker combination that offers reliable, predictable performance and excellent chip control in materials ranging from aluminum to hardened steel.</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Primary Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Execute Specific Milling Strategies:</strong> To provide an optimized tool for every milling operation, including Right Angle Milling, High-Feed Milling, Fillet/Profiling, Planar Milling, and Finishing.</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Maximize Productivity and Economy:</strong> To dramatically increase Metal Removal Rates through high-feed geometries and reduce cost-per-edge through multi-edge and double-sided insert designs.</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Deliver Precision and Surface Quality:</strong> To produce components with exacting dimensional tolerances and achieve surface finishes that can reduce or eliminate secondary operations.</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  Performance Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Ensure Process Security Across All Materials:</strong> To provide a specific grade and chipbreaker combination that offers reliable, predictable performance and excellent chip control in materials ranging from aluminum to hardened steel.</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide a Versatile and Universal System:</strong> To enable a single tool holder to perform multiple tasks and machine diverse materials simply by changing the indexable insert, offering maximum flexibility to the user.</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Economic Efficiency:</strong> Reduced machine downtime and tooling costs through extended tool life and higher speeds and feeds capabilities.</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/lathe-turning-inserts/milling-inserts" />
          </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Advanced Milling Insert Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our milling specialists can help you select the optimal insert geometries and material grades for your specific machining operations. From high-efficiency roughing to precision finishing and complex 3D profiling, we provide comprehensive solutions that maximize productivity, ensure process security, and deliver unparalleled component quality.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Milling Specialists
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Technical Consultation
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
              {(() => {
                // 从本分类产品中随机获取图片的函数
                const getRandomProductImage = () => {
                  const randomIndex = Math.floor(Math.random() * products.length);
                  return products[randomIndex].image;
                };
                
                // 定义同目录下的所有分类
                const allTurningCategories = [
                  {
                    title: "Turning Inserts",
                    image: getRandomProductImage(),
                    description: "Standard turning inserts for general applications",
                    url: "/standard-tools/lathe-turning-inserts/turning-inserts",
                  },
                  {
                    title: "Threading Inserts",
                    image: getRandomProductImage(),
                    description: "Precision inserts for thread cutting operations",
                    url: "/standard-tools/lathe-turning-inserts/threading-inserts",
                  },
                  {
                    title: "Grooving Cut-off Turning Inserts",
                    image: getRandomProductImage(),
                    description: "Specialized inserts for grooving and cut-off operations",
                    url: "/standard-tools/lathe-turning-inserts/grooving-cut-off-turning-inserts",
                  },
                  {
                    title: "Back Turning Inserts",
                    image: getRandomProductImage(),
                    description: "Specialized inserts for back turning operations",
                    url: "/standard-tools/lathe-turning-inserts/back-turning-inserts",
                  },
                  {
                    title: "Drilling Inserts",
                    image: getRandomProductImage(),
                    description: "High-performance drilling insert solutions",
                    url: "/standard-tools/lathe-turning-inserts/drilling-inserts",
                  },
                ];
                
                // 随机选择最多5个分类
                const shuffled = [...allTurningCategories].sort(() => 0.5 - Math.random());
                const selectedCategories = shuffled.slice(0, 5);
                
                return selectedCategories.map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Turning Inserts" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
}