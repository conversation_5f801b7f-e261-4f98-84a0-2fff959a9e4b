"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Eye, 
  Download, 
  Filter,
  Mail,
  Phone,
  Building,
  Calendar,
  FileText,
  ExternalLink
} from "lucide-react"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"

interface Consultation {
  id: number
  name: string
  email: string
  company: string
  phone: string
  part_details: string
  attachment_urls: string
  attachment_metadata: string
  source: string
  product_page_path?: string
  product_name?: string
  status: 'new' | 'processing' | 'completed' | 'closed'
  priority: 'low' | 'normal' | 'high' | 'urgent'
  assigned_to?: string
  notes?: string
  follow_up_date?: string
  email_status: 'not_sent' | 'sent' | 'failed' | 'sending'
  email_recipient?: string
  email_cc: string
  email_sent_at?: string
  email_content?: string
  email_error_message?: string
  created_at: string
  updated_at: string
}

interface AttachmentMetadata {
  originalName: string
  fileName: string
  size: number
  type: string
  url: string
}

const statusLabels = {
  new: '新建',
  processing: '处理中',
  completed: '已完成',
  closed: '已关闭'
}

const priorityLabels = {
  low: '低',
  normal: '普通', 
  high: '高',
  urgent: '紧急'
}

const emailStatusLabels = {
  not_sent: '未发送',
  sending: '发送中',
  sent: '已发送',
  failed: '发送失败'
}

const statusColors = {
  new: 'bg-blue-100 text-blue-800',
  processing: 'bg-yellow-100 text-yellow-800',
  completed: 'bg-green-100 text-green-800',
  closed: 'bg-gray-100 text-gray-800'
}

  const priorityColors = {
    low: 'bg-gray-100 text-gray-800',
    normal: 'bg-blue-100 text-blue-800',
    high: 'bg-orange-100 text-orange-800',
    urgent: 'bg-red-100 text-red-800'
  }

  const emailStatusColors = {
    not_sent: 'bg-gray-100 text-gray-800',
    sending: 'bg-yellow-100 text-yellow-800',
    sent: 'bg-green-100 text-green-800',
    failed: 'bg-red-100 text-red-800'
  }

export default function QuotesManagePage() {
  const [consultations, setConsultations] = useState<Consultation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [priorityFilter, setPriorityFilter] = useState<string>("all")
  const [selectedConsultation, setSelectedConsultation] = useState<Consultation | null>(null)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isEmailDialogOpen, setIsEmailDialogOpen] = useState(false)
  const [editForm, setEditForm] = useState<Partial<Consultation>>({})

  // 加载咨询数据
  const loadConsultations = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin-mzg/quotes')
      if (!response.ok) {
        throw new Error('加载失败')
      }
      const data = await response.json()
      setConsultations(data.consultations || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadConsultations()
  }, [])

  // 过滤数据
  const filteredConsultations = consultations.filter(consultation => {
    const matchesSearch = 
      (consultation.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (consultation.email || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (consultation.company || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (consultation.part_details || '').toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === "all" || consultation.status === statusFilter
    const matchesPriority = priorityFilter === "all" || consultation.priority === priorityFilter

    return matchesSearch && matchesStatus && matchesPriority
  })

  // 删除咨询
  const handleDelete = async (id: number) => {
    if (!confirm('确定要删除这条咨询记录吗？')) return

    try {
      const response = await fetch(`/api/admin-mzg/quotes/${id}`, {
        method: 'DELETE'
      })
      
      if (response.ok) {
        setConsultations(prev => prev.filter(c => c.id !== id))
      } else {
        throw new Error('删除失败')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '删除失败')
    }
  }

  // 更新咨询
  const handleUpdate = async () => {
    if (!selectedConsultation || !editForm) return

    try {
      const response = await fetch(`/api/admin-mzg/quotes/${selectedConsultation.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(editForm)
      })

      if (response.ok) {
        const updatedConsultation = await response.json()
        setConsultations(prev => 
          prev.map(c => c.id === selectedConsultation.id ? updatedConsultation.consultation : c)
        )
        setIsEditDialogOpen(false)
        setEditForm({})
      } else {
        throw new Error('更新失败')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新失败')
    }
  }

  // 查看详情
  const handleView = (consultation: Consultation) => {
    setSelectedConsultation(consultation)
    setIsViewDialogOpen(true)
  }

  // 编辑
  const handleEdit = (consultation: Consultation) => {
    setSelectedConsultation(consultation)
    setEditForm({
      status: consultation.status,
      priority: consultation.priority,
      assigned_to: consultation.assigned_to || '',
      notes: consultation.notes || '',
      follow_up_date: consultation.follow_up_date || ''
    })
    setIsEditDialogOpen(true)
  }

  // 查看邮件内容
  const handleViewEmail = (consultation: Consultation) => {
    setSelectedConsultation(consultation)
    setIsEmailDialogOpen(true)
  }

  // 预览邮件内容
  const handlePreviewEmail = (consultation: Consultation) => {
    setSelectedConsultation(consultation)
    setIsEmailDialogOpen(true)
  }

  // 解析附件信息
  const getAttachments = (consultation: Consultation): AttachmentMetadata[] => {
    try {
      return consultation.attachment_metadata ? JSON.parse(consultation.attachment_metadata) : []
    } catch {
      return []
    }
  }

  // 导出数据
  const handleExport = async () => {
    try {
      const response = await fetch('/api/admin-mzg/quotes/export')
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `consultations_${format(new Date(), 'yyyy-MM-dd')}.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (err) {
      setError('导出失败')
    }
  }

  if (loading) {
    return (
      <div className="w-full p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
          <span className="ml-2">加载中...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full p-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">客户咨询管理</h1>
          <p className="text-gray-600 mt-1">管理和跟进客户技术咨询记录</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleExport} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            导出数据
          </Button>
        </div>
      </div>

      {/* 错误信息 */}
      {error && (
        <Alert className="mb-4 border-red-200 bg-red-50">
          <AlertDescription className="text-red-700">{error}</AlertDescription>
        </Alert>
      )}

      {/* 搜索和过滤 */}
      <div className="bg-white p-4 rounded-lg border mb-6">
        <div className="grid md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="搜索客户、公司或需求..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger>
              <SelectValue placeholder="状态筛选" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="new">新建</SelectItem>
              <SelectItem value="processing">处理中</SelectItem>
              <SelectItem value="completed">已完成</SelectItem>
              <SelectItem value="closed">已关闭</SelectItem>
            </SelectContent>
          </Select>
          <Select value={priorityFilter} onValueChange={setPriorityFilter}>
            <SelectTrigger>
              <SelectValue placeholder="优先级筛选" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部优先级</SelectItem>
              <SelectItem value="urgent">紧急</SelectItem>
              <SelectItem value="high">高</SelectItem>
              <SelectItem value="normal">普通</SelectItem>
              <SelectItem value="low">低</SelectItem>
            </SelectContent>
          </Select>
          <div className="text-sm text-gray-500 flex items-center">
            共 {filteredConsultations.length} 条记录
          </div>
        </div>
      </div>

      {/* 数据表格 */}
      <div className="bg-white rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50">
              <TableHead>编号</TableHead>
              <TableHead>客户信息</TableHead>
              <TableHead>产品/需求</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>优先级</TableHead>
              <TableHead>负责人</TableHead>
              <TableHead>邮件状态</TableHead>
              <TableHead>提交时间</TableHead>
              <TableHead className="text-right">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredConsultations.map((consultation) => {
              const attachments = getAttachments(consultation)
              return (
                <TableRow key={consultation.id} className="hover:bg-gray-50">
                  <TableCell className="font-mono text-sm">
                    #{consultation.id}
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-medium">{consultation.name}</div>
                      <div className="text-sm text-gray-500 flex items-center">
                        <Building className="h-3 w-3 mr-1" />
                        {consultation.company}
                      </div>
                      <div className="text-sm text-gray-500 flex items-center">
                        <Mail className="h-3 w-3 mr-1" />
                        {consultation.email}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {consultation.product_name && (
                        <div className="font-medium text-sm text-blue-600">
                          {consultation.product_name}
                        </div>
                      )}
                      <div className="text-sm text-gray-600 line-clamp-2">
                        {(consultation.part_details || '').substring(0, 60)}...
                      </div>
                      {attachments.length > 0 && (
                        <div className="text-xs text-gray-500 flex items-center">
                          <FileText className="h-3 w-3 mr-1" />
                          {attachments.length} 个附件
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={statusColors[consultation.status]}>
                      {statusLabels[consultation.status]}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge className={priorityColors[consultation.priority]}>
                      {priorityLabels[consultation.priority]}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {consultation.assigned_to || '-'}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <Badge className={emailStatusColors[consultation.email_status || 'not_sent']}>
                        {emailStatusLabels[consultation.email_status || 'not_sent']}
                      </Badge>
                      {consultation.email_recipient && (
                        <div className="text-xs text-gray-500 flex items-center">
                          <Mail className="h-3 w-3 mr-1" />
                          {consultation.email_recipient}
                        </div>
                      )}
                      {consultation.email_sent_at && (
                        <div className="text-xs text-gray-500">
                          {format(new Date(consultation.email_sent_at), 'MM-dd HH:mm')}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {format(new Date(consultation.created_at), 'yyyy-MM-dd HH:mm', { locale: zhCN })}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleView(consultation)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleEdit(consultation)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleDelete(consultation.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>

        {filteredConsultations.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            没有找到匹配的咨询记录
          </div>
        )}
      </div>

      {/* 查看详情对话框 */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>咨询详情 #{selectedConsultation?.id}</DialogTitle>
          </DialogHeader>
          {selectedConsultation && (
            <div className="space-y-6">
              {/* 基本信息 */}
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700">客户姓名</label>
                    <div className="text-gray-900">{selectedConsultation.name}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">邮箱地址</label>
                    <div className="text-gray-900">{selectedConsultation.email}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">公司名称</label>
                    <div className="text-gray-900">{selectedConsultation.company}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">联系电话</label>
                    <div className="text-gray-900">{selectedConsultation.phone}</div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700">状态</label>
                    <div>
                      <Badge className={statusColors[selectedConsultation.status]}>
                        {statusLabels[selectedConsultation.status]}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">优先级</label>
                    <div>
                      <Badge className={priorityColors[selectedConsultation.priority]}>
                        {priorityLabels[selectedConsultation.priority]}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">信息来源</label>
                    <div className="text-gray-900">{selectedConsultation.source}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">提交时间</label>
                    <div className="text-gray-900">
                      {format(new Date(selectedConsultation.created_at), 'yyyy年MM月dd日 HH:mm:ss', { locale: zhCN })}
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">更新时间</label>
                    <div className="text-gray-900">
                      {format(new Date(selectedConsultation.updated_at), 'yyyy年MM月dd日 HH:mm:ss', { locale: zhCN })}
                    </div>
                  </div>
                </div>
              </div>

              {/* 产品信息 */}
              {(selectedConsultation.product_name || selectedConsultation.product_page_path) && (
                <div>
                  <label className="text-sm font-medium text-gray-700">产品信息</label>
                  {selectedConsultation.product_name && (
                    <div className="text-gray-900">{selectedConsultation.product_name}</div>
                  )}
                  {selectedConsultation.product_page_path && (
                    <div className="text-sm text-gray-500 mt-1">
                      来源页面: {selectedConsultation.product_page_path}
                    </div>
                  )}
                </div>
              )}

              {/* 详细需求 */}
              <div>
                <label className="text-sm font-medium text-gray-700">详细需求描述</label>
                <div className="mt-2 p-4 bg-gray-50 rounded-lg whitespace-pre-wrap text-gray-900">
                  {selectedConsultation.part_details}
                </div>
              </div>

              {/* 附件 */}
              {getAttachments(selectedConsultation).length > 0 && (
                <div>
                  <label className="text-sm font-medium text-gray-700">相关附件</label>
                  <div className="mt-2 space-y-2">
                    {getAttachments(selectedConsultation).map((attachment, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center flex-1">
                          <FileText className="h-4 w-4 text-gray-500 mr-2" />
                          <div className="flex-1">
                            <div className="text-sm font-medium">{attachment.originalName}</div>
                            <div className="text-xs text-gray-500">
                              大小: {(attachment.size / 1024 / 1024).toFixed(2)} MB | 类型: {attachment.type}
                            </div>
                            <div className="text-xs text-gray-400 font-mono break-all">
                              路径: {attachment.url}
                            </div>
                          </div>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => window.open(attachment.url, '_blank')}
                        >
                          <ExternalLink className="h-4 w-4 mr-1" />
                          查看
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 管理信息 */}
              <div className="grid md:grid-cols-2 gap-6 pt-4 border-t">
                <div>
                  <label className="text-sm font-medium text-gray-700">负责人</label>
                  <div className="text-gray-900">{selectedConsultation.assigned_to || '未分配'}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">跟进日期</label>
                  <div className="text-gray-900">
                    {selectedConsultation.follow_up_date 
                      ? format(new Date(selectedConsultation.follow_up_date), 'yyyy年MM月dd日', { locale: zhCN })
                      : '未设置'
                    }
                  </div>
                </div>
              </div>

              {/* 备注 */}
              {selectedConsultation.notes && (
                <div>
                  <label className="text-sm font-medium text-gray-700">备注信息</label>
                  <div className="mt-2 p-4 bg-gray-50 rounded-lg text-gray-900">
                    {selectedConsultation.notes}
                  </div>
                </div>
              )}

              {/* 邮件信息 */}
              <div className="space-y-4 pt-4 border-t">
                <h4 className="text-sm font-medium text-gray-700">邮件信息</h4>
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">邮件状态</label>
                    <div className="mt-1">
                      <Badge className={emailStatusColors[selectedConsultation.email_status]}>
                        {emailStatusLabels[selectedConsultation.email_status]}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">收件人邮箱</label>
                    <div className="text-gray-900">{selectedConsultation.email_recipient || selectedConsultation.email}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">发送时间</label>
                    <div className="text-gray-900">
                      {selectedConsultation.email_sent_at 
                        ? format(new Date(selectedConsultation.email_sent_at), 'yyyy年MM月dd日 HH:mm:ss', { locale: zhCN })
                        : '未发送'
                      }
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">抄送人邮箱</label>
                    <div className="text-gray-900 space-y-1">
                      {(() => {
                        try {
                          const ccList = JSON.parse(selectedConsultation.email_cc || '[]')
                          if (ccList.length > 0) {
                            return ccList.map((email, index) => (
                              <div key={index} className="text-sm">{email}</div>
                            ))
                          }
                          return <div className="text-gray-500">无抄送人</div>
                        } catch {
                          return selectedConsultation.email_cc ? 
                            <div>{selectedConsultation.email_cc}</div> : 
                            <div className="text-gray-500">无抄送人</div>
                        }
                      })()}
                    </div>
                  </div>
                </div>
                {selectedConsultation.email_error_message && (
                  <div>
                    <label className="text-sm font-medium text-gray-600">发送失败原因</label>
                    <div className="mt-1 p-3 bg-red-50 border border-red-200 rounded-lg text-red-800">
                      {selectedConsultation.email_error_message}
                    </div>
                  </div>
                )}
              </div>

              {/* 原始附件链接 */}
              {selectedConsultation.attachment_urls && (
                <div className="pt-4 border-t">
                  <label className="text-sm font-medium text-gray-700">原始附件链接</label>
                  <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                    <div className="text-xs text-gray-600 font-mono break-all">
                      {selectedConsultation.attachment_urls}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>编辑咨询 #{selectedConsultation?.id}</DialogTitle>
            <DialogDescription>
              更新咨询状态、优先级和管理信息
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-700">状态</label>
                <Select
                  value={editForm.status || ''}
                  onValueChange={(value) => setEditForm({...editForm, status: value as any})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="new">新建</SelectItem>
                    <SelectItem value="processing">处理中</SelectItem>
                    <SelectItem value="completed">已完成</SelectItem>
                    <SelectItem value="closed">已关闭</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">优先级</label>
                <Select
                  value={editForm.priority || ''}
                  onValueChange={(value) => setEditForm({...editForm, priority: value as any})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">低</SelectItem>
                    <SelectItem value="normal">普通</SelectItem>
                    <SelectItem value="high">高</SelectItem>
                    <SelectItem value="urgent">紧急</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-700">负责人</label>
                <Input
                  value={editForm.assigned_to || ''}
                  onChange={(e) => setEditForm({...editForm, assigned_to: e.target.value})}
                  placeholder="输入负责人姓名"
                />
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">跟进日期</label>
                <Input
                  type="date"
                  value={editForm.follow_up_date || ''}
                  onChange={(e) => setEditForm({...editForm, follow_up_date: e.target.value})}
                />
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">备注信息</label>
              <Textarea
                value={editForm.notes || ''}
                onChange={(e) => setEditForm({...editForm, notes: e.target.value})}
                placeholder="添加处理备注或跟进信息..."
                rows={4}
              />
            </div>
            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleUpdate}>
                保存更改
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 邮件内容查看对话框 */}
      <Dialog open={isEmailDialogOpen} onOpenChange={setIsEmailDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              {selectedConsultation?.email_content ? '邮件内容查看' : '邮件内容预览'}
            </DialogTitle>
            <DialogDescription>
              {selectedConsultation?.email_content ? '查看发送给客户的邮件详细内容' : '预览即将发送给客户的邮件样式'}
            </DialogDescription>
          </DialogHeader>
          {selectedConsultation && (
            <div className="space-y-6">
              {/* 邮件状态信息 */}
              <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                <div>
                  <label className="text-sm font-medium text-gray-600">发送状态</label>
                  <div className="mt-1">
                    <Badge className={emailStatusColors[selectedConsultation.email_status || 'not_sent']}>
                      {emailStatusLabels[selectedConsultation.email_status || 'not_sent']}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">发送时间</label>
                  <div className="mt-1 text-sm">
                    {selectedConsultation.email_sent_at 
                      ? format(new Date(selectedConsultation.email_sent_at), 'yyyy-MM-dd HH:mm:ss')
                      : '-'
                    }
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">收件人</label>
                  <div className="mt-1 text-sm">
                    {selectedConsultation.email_recipient || selectedConsultation.email}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">抄送人</label>
                  <div className="mt-1 text-sm">
                    {(() => {
                      try {
                        const ccList = JSON.parse(selectedConsultation.email_cc || '[]')
                        return ccList.length > 0 ? ccList.join(', ') : '-'
                      } catch {
                        return '-'
                      }
                    })()}
                  </div>
                </div>
              </div>

              {/* 邮件内容 */}
              {selectedConsultation.email_content ? (
                <div className="space-y-4">
                  {(() => {
                    try {
                      // 尝试解析JSON格式
                      const emailContent = JSON.parse(selectedConsultation.email_content)
                      const subject = typeof emailContent.subject === 'string' ? emailContent.subject : String(emailContent.subject || '无主题')
                      const htmlContent = typeof emailContent.html === 'string' ? emailContent.html : 
                                      typeof emailContent.body === 'string' ? emailContent.body : 
                                      String(emailContent.html || emailContent.body || emailContent.text || '无内容')
                      return (
                        <>
                          <div>
                            <label className="text-sm font-medium text-gray-600">邮件主题</label>
                            <div className="mt-1 p-3 bg-white border rounded-lg">
                              {subject}
                            </div>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-600">邮件正文预览</label>
                            <div className="mt-1 border rounded-lg overflow-hidden">
                              <div className="bg-gray-100 px-4 py-2 text-sm text-gray-600 border-b">
                                📧 邮件实际发送样式预览
                              </div>
                              <div className="max-h-96 overflow-y-auto bg-white">
                                <iframe 
                                  srcDoc={htmlContent}
                                  className="w-full h-96 border-0"
                                  title="邮件内容预览"
                                />
                              </div>
                            </div>
                          </div>
                          
                          {/* 原始HTML代码查看 */}
                          {htmlContent && htmlContent !== '无内容' && (
                            <div>
                              <label className="text-sm font-medium text-gray-600">原始HTML代码</label>
                              <div className="mt-1">
                                <details className="border rounded-lg">
                                  <summary className="px-4 py-2 bg-gray-50 cursor-pointer hover:bg-gray-100 text-sm">
                                    点击查看原始HTML代码
                                  </summary>
                                  <div className="p-4 bg-gray-900 text-gray-100 text-xs font-mono overflow-x-auto">
                                    <pre>{htmlContent}</pre>
                                  </div>
                                </details>
                              </div>
                            </div>
                          )}
                        </>
                      )
                    } catch {
                      // 如果不是JSON格式，直接显示为文本或HTML
                      const content = selectedConsultation.email_content || ''
                      const isHtml = typeof content === 'string' && (content.includes('<html') || content.includes('<HTML') || 
                                   content.includes('<!DOCTYPE') || content.includes('<!doctype'))
                      
                      return (
                        <>
                          <div>
                            <label className="text-sm font-medium text-gray-600">邮件内容</label>
                            <div className="mt-1 border rounded-lg overflow-hidden">
                              <div className="bg-gray-100 px-4 py-2 text-sm text-gray-600 border-b">
                                📧 邮件内容预览
                              </div>
                              <div className="max-h-96 overflow-y-auto bg-white">
                                {isHtml ? (
                                  <iframe 
                                    srcDoc={content}
                                    className="w-full h-96 border-0"
                                    title="邮件内容预览"
                                  />
                                ) : (
                                  <div className="p-4 whitespace-pre-wrap font-sans text-sm">
                                    {content}
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                          
                          {/* 原始内容查看 */}
                          <div>
                            <label className="text-sm font-medium text-gray-600">原始内容</label>
                            <div className="mt-1">
                              <details className="border rounded-lg">
                                <summary className="px-4 py-2 bg-gray-50 cursor-pointer hover:bg-gray-100 text-sm">
                                  点击查看原始内容
                                </summary>
                                <div className="p-4 bg-gray-900 text-gray-100 text-xs font-mono overflow-x-auto">
                                  <pre>{content}</pre>
                                </div>
                              </details>
                            </div>
                          </div>
                        </>
                      )
                    }
                  })()}
                </div>
              ) : (
                /* 邮件预览内容 */
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">邮件主题预览</label>
                    <div className="mt-1 p-3 bg-white border rounded-lg">
                      回复: 关于您的技术咨询 - #{selectedConsultation.id}
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">邮件正文预览</label>
                    <div className="mt-1 border rounded-lg overflow-hidden">
                      <div className="bg-gray-100 px-4 py-2 text-sm text-gray-600 border-b">
                        📧 邮件预览样式
                      </div>
                      <div className="max-h-96 overflow-y-auto bg-white">
                        <iframe 
                          srcDoc={(() => {
                            const escapeHtml = (unsafe: string | null | undefined) => {
                              if (!unsafe) return '';
                              return unsafe
                                .replace(/&/g, "&amp;")
                                .replace(/</g, "&lt;")
                                .replace(/>/g, "&gt;")
                                .replace(/"/g, "&quot;")
                                .replace(/'/g, "&#039;")
                                .replace(/\n/g, "<br>");
                            };
                            
                            const safeSubstring = (text: string | null | undefined, length: number) => {
                              if (!text) return '';
                              return text.length > length ? text.substring(0, length) + '...' : text;
                            };
                            
                            const id = selectedConsultation.id || 'N/A';
                            const name = escapeHtml(selectedConsultation.name || '尊敬的客户');
                            const company = escapeHtml(selectedConsultation.company || '未提供');
                            const phone = escapeHtml(selectedConsultation.phone || '未提供');
                            const partDetails = escapeHtml(safeSubstring(selectedConsultation.part_details, 200) || '未提供具体需求描述');
                            
                            return `
                              <!DOCTYPE html>
                              <html lang="zh-CN">
                              <head>
                                  <meta charset="UTF-8">
                                  <meta name="viewport" content="width=device-width, initial-scale=1.0">
                                  <title>技术咨询回复</title>
                                  <style>
                                      body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px; background-color: #f8f9fa; }
                                      .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; }
                                      .header { background: linear-gradient(135deg, #dc2626, #ef4444); color: white; padding: 30px; text-align: center; }
                                      .header h1 { margin: 0; font-size: 24px; font-weight: bold; }
                                      .content { padding: 30px; }
                                      .greeting { font-size: 16px; margin-bottom: 20px; color: #374151; }
                                      .details { background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 6px; padding: 20px; margin: 20px 0; }
                                      .details h3 { margin-top: 0; color: #111827; font-size: 18px; }
                                      .details p { margin: 8px 0; font-size: 14px; }
                                      .footer { background: #f9fafb; padding: 20px; text-align: center; border-top: 1px solid #e5e7eb; }
                                      .footer p { margin: 5px 0; font-size: 14px; color: #6b7280; }
                                      .highlight { background: #fef3c7; padding: 2px 6px; border-radius: 3px; }
                                  </style>
                              </head>
                              <body>
                                  <div class="container">
                                      <div class="header">
                                          <h1>技术咨询回复</h1>
                                          <p>咨询编号: #${id}</p>
                                      </div>
                                      <div class="content">
                                          <div class="greeting">
                                              尊敬的 ${name} 先生/女士，<br>
                                              您好！
                                          </div>
                                          <p>感谢您对我们产品的关注和咨询。我们已收到您关于以下技术需求的详细询问：</p>
                                          
                                          <div class="details">
                                              <h3>📋 咨询详情</h3>
                                              <p><strong>公司名称：</strong> ${company}</p>
                                              <p><strong>联系电话：</strong> ${phone}</p>
                                              <p><strong>产品需求：</strong></p>
                                              <p class="highlight">${partDetails}</p>
                                          </div>
                                          
                                          <p>我们的技术团队正在仔细分析您的需求，并将在24小时内为您提供：</p>
                                          <ul>
                                              <li>详细的技术方案建议</li>
                                              <li>产品规格和参数说明</li>
                                              <li>报价单和交货周期</li>
                                              <li>相关技术资料</li>
                                          </ul>
                                          
                                          <p>如有任何紧急需求，请随时联系我们：</p>
                                          <p>📞 技术热线：400-123-4567<br>
                                          📧 邮箱：<EMAIL><br>
                                          🌐 官网：www.example.com</p>
                                      </div>
                                      <div class="footer">
                                          <p><strong>MZG Tools 技术支持团队</strong></p>
                                          <p>此邮件为系统自动发送，请勿直接回复</p>
                                      </div>
                                  </div>
                              </body>
                              </html>
                            `;
                          })()}
                          className="w-full h-96 border-0"
                          title="邮件预览"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 错误信息 */}
              {selectedConsultation.email_error_message && (
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <label className="text-sm font-medium text-red-600">发送失败原因</label>
                  <div className="mt-1 text-sm text-red-800">
                    {selectedConsultation.email_error_message}
                  </div>
                </div>
              )}
            </div>
          )}
          <div className="flex justify-end pt-4">
            <Button variant="outline" onClick={() => setIsEmailDialogOpen(false)}>
              关闭
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
} 