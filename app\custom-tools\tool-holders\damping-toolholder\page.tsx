"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import ConsultationForm from "@/components/consultation-form"
import { useState, useEffect } from "react"

export default function DampingToolholderPage() {
  // Default images related to Damping Toolholder
  const defaultDampingImages = [
    "/images/tool-holder-damping-1.png",
    "/images/tool-holder-damping-2.png", 
    "/images/tool-holder-damping-3.png",
    "/images/tool-holder-damping-4.png",
    "/images/tool-holder-damping-5.png",
    "/images/tool-holder-damping-6.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/custom-tools/tool-holders/damping-toolholder");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API returned success but no images, use default Damping images
          setGalleryImages(defaultDampingImages);
        }
      } else {
        // API request failed, use default Damping images
        setGalleryImages(defaultDampingImages);
      }
    } catch (error) {
      console.error("Failed to load images:", error);
      // Network error or other exceptions, use default Damping images
      setGalleryImages(defaultDampingImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // First set default Damping images to avoid displaying unrelated images
    setGalleryImages(defaultDampingImages);
    setIsLoadingImages(false);
    
    // Then asynchronously load API images
    loadGalleryImages();
  }, []);

  // Separate useEffect to handle image carousel
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // Rotate every 20 seconds

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data for damping toolholders
  const products = [
    {
      id: "damping-001",
      name: "Anti-Vibration Boring Bar",
      image: "/images/boring-bar-damping.png",
      description: "Heavy-duty anti-vibration boring bar for stable machining",
      diameter: "12-50mm",
      length: "150-400mm",
      dampingRatio: "85%",
      application: "Deep hole boring with minimal chatter",
      series: "AVB",
      features: "Tungsten mass damper, carbide tip",
      material: "Steel with tungsten damper",
      page: "D21",
    },
    {
      id: "damping-002", 
      name: "Shock-Resistant End Mill Holder",
      image: "/images/endmill-holder-damping.png",
      description: "High-performance end mill holder with integrated vibration damping",
      diameter: "6-32mm",
      length: "100-200mm",
      dampingRatio: "90%",
      application: "High-speed milling operations",
      series: "SRH",
      features: "Viscous damper, precision balancing",
      material: "Hardened steel with polymer damper",
      page: "D22",
    },
    {
      id: "damping-003",
      name: "Tuned Mass Damper System",
      image: "/images/tmd-system.png", 
      description: "Advanced tuned mass damper for maximum stability",
      diameter: "16-80mm",
      length: "200-500mm",
      dampingRatio: "95%",
      application: "Heavy roughing and finishing operations",
      series: "TMD",
      features: "Tuned frequency response, modular design",
      material: "Steel with tungsten core",
      page: "D23",
    },
    {
      id: "damping-004",
      name: "Hydraulic Damping Chuck",
      image: "/images/hydraulic-chuck-damping.png",
      description: "Hydraulic damping system for precision machining",
      diameter: "8-25mm",
      length: "80-150mm", 
      dampingRatio: "88%",
      application: "Precision turning and milling",
      series: "HDC",
      features: "Hydraulic fluid damping, temperature stable",
      material: "Hardened steel with hydraulic chamber",
      page: "D24",
    },
    {
      id: "damping-005",
      name: "Composite Damping Bar",
      image: "/images/composite-damping-bar.png",
      description: "Lightweight composite damping solution",
      diameter: "10-40mm",
      length: "120-300mm",
      dampingRatio: "82%", 
      application: "Aerospace and medical machining",
      series: "CDB",
      features: "Carbon fiber reinforced, lightweight",
      material: "Carbon fiber composite with viscoelastic layer",
      page: "D25",
    },
    {
      id: "damping-006",
      name: "Variable Frequency Damper",
      image: "/images/variable-frequency-damper.png",
      description: "Adjustable frequency damping for versatile applications",
      diameter: "15-60mm",
      length: "180-450mm",
      dampingRatio: "92%",
      application: "Multi-material machining operations",
      series: "VFD",
      features: "Adjustable frequency, multi-mode damping",
      material: "Steel with adjustable mass system",
      page: "D26",
    }
  ]

  // Performance features
  const performanceFeatures = [
    {
      icon: "Shield",
      title: "Specialized Custom Solutions",
      description:
        "MZG excels at designing and manufacturing custom milling tools for unique and demanding requirements. We leverage our deep technical expertise to solve complex challenges, from processing difficult materials to achieving intricate geometries and extreme efficiency, going far beyond standard product capabilities.",
    },
    {
      icon: "Target",
      title: "Collaborative Design & Precision",
      description:
        "We guarantee the perfect tool through a rigorous, collaborative design process. Before production, clients review and approve detailed CAD blueprints or simulations, ensuring the final product precisely matches their specifications, machining strategies, and performance expectations from the very first cut.",
    },
    {
      icon: "Zap",
      title: "Rapid Delivery & In-House Control",
      description:
        "With complete end-to-end manufacturing capabilities in-house—including heat treatment and coating—we maintain full control over the production process. This integration enables us to offer one of the industry's most competitive delivery times for custom tools, minimizing your downtime and maximizing productivity.",
    },
  ]


  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Custom Damping Solutions
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  Custom Damping and Shockproof Toolholder
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  MZG's advanced damping and shockproof toolholders represent the pinnacle of vibration control technology in precision machining. These specialized toolholding solutions incorporate sophisticated damping mechanisms including tuned mass dampers, viscous dampers, and advanced composite materials to virtually eliminate machining vibrations. Our damping toolholders enable unprecedented cutting performance, allowing manufacturers to achieve higher material removal rates while maintaining exceptional surface finishes and dimensional accuracy.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                    onClick={() => {
                      document.getElementById("damping-toolholder-consultation")?.scrollIntoView({ behavior: "smooth" });
                    }}
                  >
                    Request Custom Quote
                  </Button>
                
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/damping-toolholder-hero.png"
                    alt="MZG Professional Damping and Shockproof Toolholder System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>



        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
        
                {/* Damping Technology Benefits - Single Container */}
            <div className="mb-16">
                 <div className="flex items-center mb-6">
                 <div className="w-12 h-1 bg-red-600 mr-4"></div>
                   <h2 className="text-3xl font-bold">Why Choose Us</h2>
            </div>
        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-green-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

               <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-100 text-sm">
  <p className="mb-4 font-normal">
    At MZG Tool Machine Company, we understand that standard tooling doesn't always meet every demanding machining requirement. In fact, the proportion of custom and specially designed milling inserts and tooling solutions we ship annually continues to grow, testifying not only to our exceptional capabilities in this area but also to our customers' pursuit of ultimate performance and unique solutions.
  </p>
  <p className="mb-4 font-normal">
    Therefore, if you cannot find a solution that perfectly matches your unique machining challenges within our extensive standard product catalog, please rest assured. We possess profound expertise and technical prowess to custom-design milling tools for you, ensuring they meet the same stringent quality standards and performance benchmarks as all our standard products. Whether it's tackling difficult material processing, achieving complex geometries in a single cut, or pursuing extreme machining efficiency, we are confident in providing you with the optimal solution.
  </p>
  <p className="mb-4 font-normal">
    Before commencing any custom milling tool manufacturing process, our professional engineering design team will first provide you with detailed Computer-Aided Design (CAD) blueprints or simulation proposals for your review and approval. This rigorous approval process ensures that the final custom tool delivered will perfectly match your precise specifications, machining strategies, and even your performance expectations in cutting. We value every detail, striving to ensure the exceptional performance of the tool right from the design stage.
  </p>
  <p className="font-normal">
    We deeply understand the importance of production efficiency to your operations, so you no longer need to worry about prolonged machine downtime. MZG Tool Machine possesses end-to-end, complete in-house manufacturing capabilities, including material selection, precision grinding, advanced coating applications, and crucial heat treatment processes. This highly integrated production model, combined with our industry-leading operational efficiency, allows us to offer one of the most competitive custom tool delivery times, ensuring your custom milling inserts can be rapidly put into production, minimizing downtime and helping you seize market opportunities. Choose MZG Tool Machine, and you choose an efficient, precise, and reliable custom machining partner.
  </p>
</div>
          </div>
          

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - Main carousel image */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultDampingImages[0]}
                    alt="Damping Toolholder Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                  </div>
                )}
              </div>

              {/* Middle section and Right section - Small image grid */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultDampingImages[imageIndex % defaultDampingImages.length]
                  : defaultDampingImages[index % defaultDampingImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Damping Toolholder Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                  </div>
                );
              })}
            </div>
          </div>


          {/* FAQ Section */}
          <div className="mb-12">
            <FAQSectionEn pageUrl="/custom-tools/tool-holders/damping-toolholder" />
          </div>

          {/* Technical Consultation Form */}
          <div id="damping-toolholder-consultation" className="mb-12 bg-gray-50 p-8 rounded-lg">
            
            <ConsultationForm 
              source="damping-toolholder-page"
              title="Damping Toolholder Technical Consultation"
              productInfo={{
                pagePath: "/custom-tools/tool-holders/damping-toolholder",
                productName: "Damping and Shockproof Toolholder System"
              }}
            />
          </div>

         

          
         
        </div>
      </div>
      
      <Footer />
    </>
  )
} 