import { NextRequest, NextResponse } from 'next/server'
import { sql } from '@/lib/database'

// 转义CSV字段
function escapeCsvField(field: any): string {
  if (field === null || field === undefined) {
    return ''
  }
  
  const str = String(field)
  
  // 如果包含逗号、引号或换行符，需要用引号包围并转义引号
  if (str.includes(',') || str.includes('"') || str.includes('\n') || str.includes('\r')) {
    return `"${str.replace(/"/g, '""')}"`
  }
  
  return str
}

// 导出咨询数据为CSV
export async function GET(request: NextRequest) {
  try {
    // 查询所有咨询记录
    const consultations = await sql`
      SELECT 
        id,
        name,
        email,
        company,
        phone,
        part_details,
        source,
        product_page_path,
        product_name,
        status,
        priority,
        assigned_to,
        notes,
        follow_up_date,
        created_at,
        updated_at,
        attachment_metadata
      FROM consultation 
      ORDER BY created_at DESC
    `

    // CSV 头部
    const headers = [
      'ID',
      '客户姓名',
      '邮箱地址',
      '公司名称',
      '联系电话',
      '详细需求',
      '信息来源',
      '产品页面',
      '产品名称',
      '状态',
      '优先级',
      '负责人',
      '备注',
      '跟进日期',
      '提交时间',
      '更新时间',
      '附件数量',
      '附件清单'
    ]

    // 状态和优先级映射
    const statusLabels: Record<string, string> = {
      new: '新建',
      processing: '处理中',
      completed: '已完成',
      closed: '已关闭'
    }

    const priorityLabels: Record<string, string> = {
      low: '低',
      normal: '普通',
      high: '高',
      urgent: '紧急'
    }

    // 构建CSV内容
    let csvContent = headers.map(h => escapeCsvField(h)).join(',') + '\n'
    
    for (const consultation of consultations as any[]) {
      // 解析附件信息
      let attachmentCount = 0
      let attachmentList = ''
      
      try {
        if (consultation.attachment_metadata) {
          const attachments = JSON.parse(consultation.attachment_metadata)
          attachmentCount = attachments.length
          attachmentList = attachments
            .map((att: any) => `${att.originalName} (${(att.size / 1024 / 1024).toFixed(2)}MB)`)
            .join('; ')
        }
      } catch {
        // 解析失败时保持默认值
      }

      const row = [
        consultation.id,
        consultation.name,
        consultation.email,
        consultation.company,
        consultation.phone,
        consultation.part_details,
        consultation.source,
        consultation.product_page_path || '',
        consultation.product_name || '',
        statusLabels[consultation.status] || consultation.status,
        priorityLabels[consultation.priority] || consultation.priority,
        consultation.assigned_to || '',
        consultation.notes || '',
        consultation.follow_up_date || '',
        consultation.created_at,
        consultation.updated_at,
        attachmentCount,
        attachmentList
      ]

      csvContent += row.map(field => escapeCsvField(field)).join(',') + '\n'
    }

    // 创建响应
    const response = new NextResponse(csvContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv; charset=utf-8',
        'Content-Disposition': `attachment; filename="consultations_${new Date().toISOString().split('T')[0]}.csv"`,
        'Cache-Control': 'no-cache'
      }
    })

    return response

  } catch (error) {
    console.error('导出咨询数据失败:', error)
    return NextResponse.json(
      { success: false, message: '导出失败' },
      { status: 500 }
    )
  }
} 