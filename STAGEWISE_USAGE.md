# Stagewise 工具使用说明

## 问题解决总结

✅ **样式冲突问题** - 已完全解决
- 使用独立的 React Root 渲染
- 添加了样式隔离容器
- 主应用使用保护性 CSS 类

✅ **功能可用性** - 已修复
- 简化了配置选项
- 优化了容器样式
- 添加了详细的加载日志

## 如何使用 Stagewise

### 1. 启动开发模式
```bash
npm run dev
```

### 2. 打开浏览器
访问: http://localhost:3000 或 http://localhost:3001

### 3. 等待工具栏加载
- 页面右下角会显示蓝色的"开发模式"提示
- 大约0.8秒后 Stagewise 工具栏会自动加载
- 控制台会显示加载进度和成功消息

### 4. 使用功能
- **选择元素**: 点击页面上的任何元素
- **添加注释**: 选择元素后可以添加设计注释
- **编辑建议**: AI 会根据您的注释提供编辑建议

## 控制台消息说明

正常加载时您会看到：
```
🚀 开始初始化 Stagewise...
📦 Stagewise 模块加载成功
🧹 清理旧容器 (如果存在)
📍 Stagewise 容器已创建
✅ Stagewise 工具栏已成功加载
🎯 您现在可以点击页面元素进行选择和注释
```

## 故障排除

### 如果工具栏不显示：
1. ✅ 确认在开发模式 (`npm run dev`)
2. ✅ 等待至少1秒让工具栏完全加载
3. ✅ 检查浏览器控制台是否有错误消息
4. ✅ 确认看到蓝色的"开发模式"提示

### 如果无法选择元素：
1. ✅ 确认控制台显示"工具栏已成功加载"
2. ✅ 尝试点击不同的页面元素
3. ✅ 刷新页面重新加载工具栏
4. ✅ 检查是否有其他浏览器扩展干扰

### 如果样式出现问题：
1. ✅ 清除浏览器缓存
2. ✅ 重启开发服务器
3. ✅ 确认主应用容器使用了正确的 CSS 类

## 技术实现

- **隔离渲染**: 使用独立的 React Root
- **样式保护**: 主应用使用 `main-app-container` 类
- **事件处理**: 容器使用 `pointer-events: none`，内部元素使用 `pointer-events: auto`
- **层级管理**: 工具栏使用 `z-index: 999999`

## 生产环境

Stagewise 仅在开发模式下运行：
- ✅ `npm run dev` - 包含 Stagewise 功能
- ✅ `npm run build` - 不包含 Stagewise，无性能影响
- ✅ `npm run start` - 生产模式，无 Stagewise

## 版本信息

- Stagewise 版本: 0.4.4
- 集成方式: 独立初始化组件
- 配置文件: `components/stagewise-init.tsx`

---

🎉 现在您可以愉快地使用 Stagewise AI 工具来优化您的 UI 设计了！ 