# Neon PostgreSQL 分步执行SQL脚本

由于Neon的SQL编辑器可能不支持一次执行大型脚本，请按以下步骤分别执行：

## 第1步：创建博客分类表
```sql
CREATE TABLE IF NOT EXISTS blog_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#3B82F6',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 第2步：创建博客标签表
```sql
CREATE TABLE IF NOT EXISTS blog_tags (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    slug VARCHAR(50) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 第3步：创建博客文章表
```sql
CREATE TABLE IF NOT EXISTS blog_posts (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    excerpt TEXT,
    content TEXT NOT NULL,
    category_id INTEGER REFERENCES blog_categories(id) ON DELETE SET NULL,
    author VARCHAR(100) NOT NULL,
    image_url VARCHAR(500),
    featured_image_alt VARCHAR(255),
    published BOOLEAN DEFAULT false,
    published_at TIMESTAMP,
    view_count INTEGER DEFAULT 0,
    meta_title VARCHAR(255),
    meta_description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 第4步：创建文章标签关联表
```sql
CREATE TABLE IF NOT EXISTS blog_post_tags (
    post_id INTEGER REFERENCES blog_posts(id) ON DELETE CASCADE,
    tag_id INTEGER REFERENCES blog_tags(id) ON DELETE CASCADE,
    PRIMARY KEY (post_id, tag_id)
);
```

## 第5步：创建索引（一个一个执行）
```sql
CREATE INDEX IF NOT EXISTS idx_blog_posts_published ON blog_posts(published);
```

```sql
CREATE INDEX IF NOT EXISTS idx_blog_posts_created_at ON blog_posts(created_at DESC);
```

```sql
CREATE INDEX IF NOT EXISTS idx_blog_posts_category ON blog_posts(category_id);
```

```sql
CREATE INDEX IF NOT EXISTS idx_blog_posts_slug ON blog_posts(slug);
```

## 第6步：插入默认分类（一个一个执行）
```sql
INSERT INTO blog_categories (name, slug, description, color) VALUES
('News', 'news', 'Latest company news and updates', '#EF4444')
ON CONFLICT (slug) DO NOTHING;
```

```sql
INSERT INTO blog_categories (name, slug, description, color) VALUES
('Careers', 'careers', 'Career opportunities and company culture', '#10B981')
ON CONFLICT (slug) DO NOTHING;
```

```sql
INSERT INTO blog_categories (name, slug, description, color) VALUES
('Employee Spotlights', 'employee-spotlights', 'Featured employee stories', '#8B5CF6')
ON CONFLICT (slug) DO NOTHING;
```

```sql
INSERT INTO blog_categories (name, slug, description, color) VALUES
('Exhibition', 'exhibition', 'Trade shows and industry events', '#F59E0B')
ON CONFLICT (slug) DO NOTHING;
```

```sql
INSERT INTO blog_categories (name, slug, description, color) VALUES
('Manufacturing 101', 'manufacturing-101', 'Educational content about manufacturing', '#3B82F6')
ON CONFLICT (slug) DO NOTHING;
```

## 第7步：创建触发器函数
```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';
```

## 第8步：创建触发器
```sql
CREATE TRIGGER update_blog_posts_updated_at 
    BEFORE UPDATE ON blog_posts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

```sql
CREATE TRIGGER update_blog_categories_updated_at 
    BEFORE UPDATE ON blog_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## 验证安装
执行完所有步骤后，运行以下查询验证表是否创建成功：

```sql
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE 'blog_%' 
ORDER BY table_name;
```

应该返回4个表：
- blog_categories
- blog_post_tags  
- blog_posts
- blog_tags

## 检查分类数据
```sql
SELECT * FROM blog_categories;
```

应该返回5个默认分类。

## 注意事项
1. 每次只复制一个SQL块到Neon编辑器
2. 确保每个步骤都成功执行后再进行下一步
3. 如果某个步骤失败，检查错误信息并重试
4. 表创建顺序很重要，请按步骤顺序执行