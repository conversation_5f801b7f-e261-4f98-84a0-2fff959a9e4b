import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { StagewiseInit } from "@/components/stagewise-init"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "MZG Tools - Precision Industrial Milling Tools",
  description: "Custom and standard industrial milling tools for manufacturing industries",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className} suppressHydrationWarning>
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange>
          <div className="main-app-container">
            {children}
          </div>
          {/* Stagewise 开发工具 - 仅在开发模式下加载 */}
          {process.env.NODE_ENV === 'development' && <StagewiseInit />}
        </ThemeProvider>
      </body>
    </html>
  )
}
