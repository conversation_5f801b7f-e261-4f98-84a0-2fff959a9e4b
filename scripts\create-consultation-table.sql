-- 创建客户咨询表
CREATE TABLE IF NOT EXISTS consultation (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  email VARCHAR(255) NOT NULL,
  company VARCHAR(200) NOT NULL,
  phone VARCHAR(50) NOT NULL,
  part_details TEXT NOT NULL,
  attachment_urls JSONB DEFAULT '[]'::jsonb,           -- JSON格式存储文件URL数组
  attachment_metadata JSONB DEFAULT '[]'::jsonb,       -- JSON格式存储文件元信息
  source VARCHAR(100) NOT NULL DEFAULT 'website',      -- 信息来源
  product_page_path VARCHAR(500),                      -- 产品页面路径
  product_name VARCHAR(200),                           -- 产品名称
  status VARCHAR(20) DEFAULT 'new' CHECK (status IN ('new', 'processing', 'completed', 'closed')),
  priority VARCHAR(10) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  assigned_to VARCHAR(100),                            -- 负责人
  notes TEXT,                                          -- 备注
  follow_up_date DATE,                                 -- 跟进日期
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_consultation_email ON consultation(email);
CREATE INDEX IF NOT EXISTS idx_consultation_company ON consultation(company);
CREATE INDEX IF NOT EXISTS idx_consultation_status ON consultation(status);
CREATE INDEX IF NOT EXISTS idx_consultation_priority ON consultation(priority);
CREATE INDEX IF NOT EXISTS idx_consultation_created_at ON consultation(created_at);
CREATE INDEX IF NOT EXISTS idx_consultation_source ON consultation(source);
CREATE INDEX IF NOT EXISTS idx_consultation_assigned_to ON consultation(assigned_to);

-- 创建更新时间戳的触发器
CREATE OR REPLACE FUNCTION update_consultation_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_consultation_updated_at
    BEFORE UPDATE ON consultation
    FOR EACH ROW
    EXECUTE FUNCTION update_consultation_updated_at();

-- 添加表注释
COMMENT ON TABLE consultation IS '客户技术咨询表';
COMMENT ON COLUMN consultation.id IS '主键ID';
COMMENT ON COLUMN consultation.name IS '客户姓名';
COMMENT ON COLUMN consultation.email IS '客户邮箱';
COMMENT ON COLUMN consultation.company IS '公司名称';
COMMENT ON COLUMN consultation.phone IS '联系电话';
COMMENT ON COLUMN consultation.part_details IS '详细需求描述';
COMMENT ON COLUMN consultation.attachment_urls IS '附件URL数组(JSON)';
COMMENT ON COLUMN consultation.attachment_metadata IS '附件元信息(JSON)';
COMMENT ON COLUMN consultation.source IS '信息来源';
COMMENT ON COLUMN consultation.product_page_path IS '产品页面路径';
COMMENT ON COLUMN consultation.product_name IS '产品名称';
COMMENT ON COLUMN consultation.status IS '状态: new, processing, completed, closed';
COMMENT ON COLUMN consultation.priority IS '优先级: low, normal, high, urgent';
COMMENT ON COLUMN consultation.assigned_to IS '负责人';
COMMENT ON COLUMN consultation.notes IS '管理员备注';
COMMENT ON COLUMN consultation.follow_up_date IS '跟进日期';
COMMENT ON COLUMN consultation.created_at IS '创建时间';
COMMENT ON COLUMN consultation.updated_at IS '最后更新时间'; 