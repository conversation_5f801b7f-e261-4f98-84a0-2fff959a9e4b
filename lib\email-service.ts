import fs from "fs/promises"
import path from "path"

// Postmark API 基础URL
const POSTMARK_API_BASE = "https://api.postmarkapp.com"

// 邮箱设置文件路径
const EMAIL_SETTINGS_FILE = path.join(process.cwd(), "data", "email-settings.json")

export interface EmailSettings {
  apiToken: string
  fromEmail: string
  stream: string
  recipients: {
    to: string
    cc: string
    bcc: string
  }
  testEmail: string
}

export interface EmailOptions {
  to: string | string[]
  cc?: string | string[]
  bcc?: string | string[]
  subject: string
  htmlBody?: string
  textBody?: string
  from?: string
  stream?: string
}

// 默认邮箱设置
const defaultSettings: EmailSettings = {
  apiToken: "da872e1f-a512-4f29-8936-c66aac191e2b",
  fromEmail: "<EMAIL>",
  stream: "outbound",
  recipients: {
    to: "<EMAIL>",
    cc: "",
    bcc: ""
  },
  testEmail: "<EMAIL>"
}

// 确保data目录存在
async function ensureDataDirectory() {
  const dataDir = path.join(process.cwd(), "data")
  try {
    await fs.access(dataDir)
  } catch {
    await fs.mkdir(dataDir, { recursive: true })
  }
}

// 获取邮箱设置
export async function getEmailSettings(): Promise<EmailSettings> {
  try {
    await ensureDataDirectory()
    const data = await fs.readFile(EMAIL_SETTINGS_FILE, "utf-8")
    return { ...defaultSettings, ...JSON.parse(data) }
  } catch (error) {
    // 文件不存在时返回默认设置
    return defaultSettings
  }
}

// 保存邮箱设置
export async function saveEmailSettings(settings: EmailSettings): Promise<void> {
  await ensureDataDirectory()
  await fs.writeFile(EMAIL_SETTINGS_FILE, JSON.stringify(settings, null, 2), "utf-8")
}

// 发送邮件
export async function sendEmail(options: EmailOptions): Promise<{
  success: boolean
  messageId?: string
  error?: string
}> {
  try {
    const settings = await getEmailSettings()
    
    if (!settings.apiToken) {
      return { success: false, error: "邮件服务未配置API Token" }
    }

    // 构建邮件数据
    const emailData: any = {
      From: options.from || settings.fromEmail,
      To: Array.isArray(options.to) ? options.to.join(",") : options.to,
      Subject: options.subject,
      MessageStream: options.stream || settings.stream || "outbound"
    }

    // 添加邮件内容
    if (options.htmlBody) {
      emailData.HtmlBody = options.htmlBody
    }
    if (options.textBody) {
      emailData.TextBody = options.textBody
    }

    // 添加抄送和密送
    if (options.cc) {
      emailData.Cc = Array.isArray(options.cc) ? options.cc.join(",") : options.cc
    }
    if (options.bcc) {
      emailData.Bcc = Array.isArray(options.bcc) ? options.bcc.join(",") : options.bcc
    }

    // 调用Postmark API
    const response = await fetch(`${POSTMARK_API_BASE}/email`, {
      method: "POST",
      headers: {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "X-Postmark-Server-Token": settings.apiToken
      },
      body: JSON.stringify(emailData)
    })

    if (response.ok) {
      const result = await response.json()
      return {
        success: true,
        messageId: result.MessageID
      }
    } else {
      const errorData = await response.json().catch(() => null)
      return {
        success: false,
        error: errorData?.Message || `发送失败: ${response.status}`
      }
    }
  } catch (error) {
    console.error("邮件发送失败:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "未知错误"
    }
  }
}

// 测试Postmark连接
export async function testPostmarkConnection(apiToken: string): Promise<{
  success: boolean
  serverInfo?: any
  error?: string
}> {
  try {
    const response = await fetch(`${POSTMARK_API_BASE}/server`, {
      method: "GET",
      headers: {
        "Accept": "application/json",
        "X-Postmark-Server-Token": apiToken
      }
    })

    if (response.ok) {
      const serverInfo = await response.json()
      return {
        success: true,
        serverInfo: {
          name: serverInfo.Name,
          id: serverInfo.ID,
          color: serverInfo.Color
        }
      }
    } else {
      const errorData = await response.json().catch(() => null)
      let errorMessage = "连接失败"
      
      if (response.status === 401) {
        errorMessage = "API Token无效或已过期"
      } else if (response.status === 403) {
        errorMessage = "没有访问权限"
      } else if (errorData?.Message) {
        errorMessage = errorData.Message
      }

      return {
        success: false,
        error: errorMessage
      }
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "网络连接失败"
    }
  }
}

// 发送通知邮件（系统通用）
export async function sendNotificationEmail(
  subject: string,
  content: string,
  recipient?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const settings = await getEmailSettings()
    const to = recipient || settings.recipients.to || settings.testEmail

    if (!to) {
      return { success: false, error: "未配置收件人" }
    }

    const htmlBody = `
      <html>
      <head>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #dc2626, #ef4444); color: white; padding: 30px; border-radius: 8px 8px 0 0; text-align: center; }
          .content { background: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
          .footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>MZG Tools 系统通知</h1>
          </div>
          <div class="content">
            ${content}
          </div>
          <div class="footer">
            <p>此邮件由 MZG Tools 管理系统自动发送</p>
            <p>发送时间: ${new Date().toLocaleString('zh-CN')}</p>
          </div>
        </div>
      </body>
      </html>
    `

    const textBody = `
MZG Tools 系统通知

${content.replace(/<[^>]*>/g, '')}

此邮件由 MZG Tools 管理系统自动发送
发送时间: ${new Date().toLocaleString('zh-CN')}
    `

    return await sendEmail({
      to,
      cc: settings.recipients.cc || undefined,
      bcc: settings.recipients.bcc || undefined,
      subject: `[MZG Tools] ${subject}`,
      htmlBody,
      textBody
    })
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "发送失败"
    }
  }
} 

// 发送客户咨询邮件
export interface ConsultationEmailData {
  id: number
  name: string
  email: string
  company: string
  phone: string
  partDetails: string
  source: string
  productPagePath?: string
  productName?: string
  attachments: Array<{
    originalName: string
    fileName: string
    size: number
    type: string
    url: string
  }>
  submittedAt: string
}

export async function sendConsultationEmail(
  consultationData: ConsultationEmailData,
  customRecipient?: string,
  ccEmails?: string[]
): Promise<{ success: boolean; error?: string; messageId?: string }> {
  // 导入数据库连接（动态导入以避免循环依赖）
  const { sql } = await import('./database')
  
  try {
    const settings = await getEmailSettings()
    const to = customRecipient || settings.recipients.to || settings.testEmail

    if (!to) {
      await updateEmailStatus(consultationData.id, 'failed', 'not_sent', null, null, null, '未配置收件人')
      return { success: false, error: "未配置收件人" }
    }

    // 更新邮件状态为发送中
    await updateEmailStatus(consultationData.id, 'sending', 'sending', to, ccEmails || null, null, null)

    // 格式化附件信息
    const attachmentsList = consultationData.attachments.length > 0 
      ? consultationData.attachments.map(att => 
          `<li><strong>${att.originalName}</strong> (${(att.size / 1024 / 1024).toFixed(2)} MB) - <a href="${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}${att.url}">下载</a></li>`
        ).join('')
      : '<li>无附件</li>'

    const htmlBody = `
      <html>
      <head>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 700px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #dc2626, #ef4444); color: white; padding: 30px; border-radius: 8px 8px 0 0; text-align: center; }
          .content { background: #ffffff; padding: 30px; border: 1px solid #e5e7eb; }
          .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px; }
          .info-item { padding: 15px; background: #f9fafb; border-radius: 6px; }
          .info-label { font-weight: bold; color: #374151; margin-bottom: 5px; }
          .info-value { color: #1f2937; }
          .details { background: #f0f9ff; padding: 20px; border-radius: 8px; border-left: 4px solid #3b82f6; margin: 20px 0; }
          .attachments { background: #fefce8; padding: 20px; border-radius: 8px; border-left: 4px solid #eab308; }
          .footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; border-top: 1px solid #e5e7eb; padding-top: 20px; }
          ul { margin: 10px 0; padding-left: 20px; }
          .urgent { background: #fef2f2; border-left-color: #ef4444; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔧 新的技术咨询</h1>
            <p>咨询编号: #${consultationData.id}</p>
          </div>
          <div class="content">
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">👤 客户姓名</div>
                <div class="info-value">${consultationData.name}</div>
              </div>
              <div class="info-item">
                <div class="info-label">📧 邮箱地址</div>
                <div class="info-value">${consultationData.email}</div>
              </div>
              <div class="info-item">
                <div class="info-label">🏢 公司名称</div>
                <div class="info-value">${consultationData.company}</div>
              </div>
              <div class="info-item">
                <div class="info-label">📞 联系电话</div>
                <div class="info-value">${consultationData.phone}</div>
              </div>
            </div>

            ${consultationData.productName ? `
            <div class="info-item" style="margin-bottom: 20px;">
              <div class="info-label">🛠️ 关注产品</div>
              <div class="info-value">${consultationData.productName}</div>
              <div style="color: #6b7280; font-size: 14px; margin-top: 5px;">
                来源页面: ${consultationData.productPagePath}
              </div>
            </div>
            ` : ''}

            <div class="details">
              <h3 style="margin-top: 0; color: #1e40af;">📝 详细需求描述</h3>
              <div style="white-space: pre-wrap; background: white; padding: 15px; border-radius: 6px; border: 1px solid #e5e7eb;">${consultationData.partDetails}</div>
            </div>

            <div class="attachments">
              <h3 style="margin-top: 0; color: #92400e;">📎 相关附件 (${consultationData.attachments.length}个)</h3>
              <ul style="margin: 0;">
                ${attachmentsList}
              </ul>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px;">
              <div class="info-item">
                <div class="info-label">📍 信息来源</div>
                <div class="info-value">${consultationData.source}</div>
              </div>
              <div class="info-item">
                <div class="info-label">⏰ 提交时间</div>
                <div class="info-value">${new Date(consultationData.submittedAt).toLocaleString('zh-CN')}</div>
              </div>
            </div>
          </div>
          <div class="footer">
            <p>📧 请及时回复客户咨询，建议在24小时内联系客户</p>
            <p>💼 此邮件由 MZG Tools 客户咨询系统自动发送</p>
          </div>
        </div>
      </body>
      </html>
    `

    const textBody = `
新的技术咨询 - 编号 #${consultationData.id}

客户信息:
姓名: ${consultationData.name}
邮箱: ${consultationData.email}  
公司: ${consultationData.company}
电话: ${consultationData.phone}
${consultationData.productName ? `关注产品: ${consultationData.productName}` : ''}

详细需求:
${consultationData.partDetails}

附件信息:
${consultationData.attachments.length > 0 
  ? consultationData.attachments.map(att => `- ${att.originalName} (${(att.size / 1024 / 1024).toFixed(2)} MB)`).join('\n')
  : '无附件'
}

来源: ${consultationData.source}
提交时间: ${new Date(consultationData.submittedAt).toLocaleString('zh-CN')}

请及时回复客户咨询，建议在24小时内联系客户。
此邮件由 MZG Tools 客户咨询系统自动发送。
    `

    const subject = `[MZG咨询] ${consultationData.productName || '技术咨询'} - ${consultationData.company} (${consultationData.name})`

    // 准备邮件内容对象
    const emailContent = {
      subject,
      body: htmlBody,
      text: textBody
    }

    // 发送邮件
    const result = await sendEmail({
      to,
      cc: ccEmails || (settings.recipients.cc ? [settings.recipients.cc] : undefined),
      bcc: settings.recipients.bcc || undefined,
      subject,
      htmlBody,
      textBody
    })

    if (result.success) {
      // 更新邮件状态为已发送
      await updateEmailStatus(
        consultationData.id, 
        'sent', 
        'sent', 
        to, 
        ccEmails || null, 
        emailContent, 
        null,
        result.messageId
      )
      
      return { 
        success: true, 
        messageId: result.messageId 
      }
    } else {
      // 更新邮件状态为发送失败
      await updateEmailStatus(
        consultationData.id, 
        'failed', 
        'failed', 
        to, 
        ccEmails || null, 
        emailContent, 
        result.error || '发送失败'
      )
      
      return { 
        success: false, 
        error: result.error 
      }
    }
  } catch (error) {
    // 更新邮件状态为发送失败
    await updateEmailStatus(
      consultationData.id, 
      'failed', 
      'failed', 
      null, 
      null, 
      null, 
      error instanceof Error ? error.message : "发送失败"
    )
    
    return {
      success: false,
      error: error instanceof Error ? error.message : "发送失败"
    }
  }
}

// 更新邮件状态到数据库
async function updateEmailStatus(
  consultationId: number,
  status: 'not_sent' | 'sending' | 'sent' | 'failed',
  emailStatus: string,
  recipient: string | null,
  ccEmails: string[] | null,
  emailContent: any | null,
  errorMessage: string | null,
  messageId?: string
) {
  try {
    const { sql } = await import('./database')
    
    // 构建更新数据
    const updateData: any = {
      email_status: emailStatus,
      email_recipient: recipient,
      email_cc: JSON.stringify(ccEmails || []),
      email_error_message: errorMessage,
      updated_at: new Date().toISOString()
    }

    if (emailContent) {
      updateData.email_content = JSON.stringify(emailContent)
    }

    if (status === 'sent') {
      updateData.email_sent_at = new Date().toISOString()
    }

    // 执行数据库更新
    await sql`
      UPDATE consultation 
      SET 
        email_status = ${updateData.email_status},
        email_recipient = ${updateData.email_recipient},
        email_cc = ${updateData.email_cc},
        email_content = ${updateData.email_content || null},
        email_sent_at = ${updateData.email_sent_at || null},
        email_error_message = ${updateData.email_error_message},
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ${consultationId}
    `

    console.log(`邮件状态已更新: ${consultationId} -> ${emailStatus}`)
  } catch (error) {
    console.error('更新邮件状态失败:', error)
  }
} 