"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function DrillChuckPage() {
  // Drill Chuck相关的默认图片
  const defaultDrillChuckImages = [
    "/images/C59-1.png",
    "/images/C60-1.png",
    "/images/C61-1.png",
    "/images/C62-1.png",
    "/images/C62-2.png",
    "/images/C63-1.png",
    "/images/C63-2.png",
    "/images/C63-3.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/milling-tool-holder/drill-chuck");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认Drill Chuck图片
          setGalleryImages(defaultDrillChuckImages);
        }
      } else {
        // API请求失败，使用默认Drill Chuck图片
        setGalleryImages(defaultDrillChuckImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认Drill Chuck图片
      setGalleryImages(defaultDrillChuckImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认Drill Chuck图片，避免显示无关图片
    setGalleryImages(defaultDrillChuckImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data based on the Drill Chuck Tool Holder system
  const products = [
    {
      id: "drill-chuck-001",
      name: "APU Milling Machine Tool Holder",
      image: "/images/C59-1.png",
      description: "An integrated drill chuck specifically for milling machine applications",
      series: "APU Series",
      connection: "Integrated Chuck",
      application: "Milling machine applications",
      pageNumber: "C59",
    },
    {
      id: "drill-chuck-002", 
      name: "JM Drill Chuck Milling Machine Tool Holder",
      image: "/images/C60-1.png",
      description: "An integrated drill chuck specifically for milling machine applications",
      series: "JM Series",
      connection: "Integrated Chuck",
      application: "Milling machine applications",
      pageNumber: "C60",
    },
    {
      id: "drill-chuck-003",
      name: "J Keyless Drill Chuck",
      image: "/images/C61-1.png", 
      description: "An automatic keyless drill chuck suitable for various clamping ranges",
      series: "J Series",
      connection: "Keyless Chuck",
      application: "Various clamping ranges",
      pageNumber: "C61",
    },
    {
      id: "drill-chuck-004",
      name: "J Spanner Drill Chuck",
      image: "/images/C62-1.png",
      description: "Suitable for medium and heavy duty drilling use. For milling machine, lathe, drill machine, CNC & wood working machine. High speed drilling can be performed for possessing high cutting power",
      series: "J Series",
      connection: "Spanner Chuck",
      application: "Medium & heavy duty drilling for CNC and woodworking machines",
      pageNumber: "C62",
    },
    {
      id: "drill-chuck-005",
      name: "Three-sided Milling Cutter Arbor (-XS)",
      image: "/images/C62-2.png",
      description: "An arbor specifically for three-sided milling cutters, including saw blade milling cutters",
      series: "Arbor Series",
      connection: "Three-sided Arbor",
      application: "Three-sided milling cutters and saw blade milling cutters",
      pageNumber: "C62",
    },
    {
      id: "drill-chuck-006",
      name: "Morse Taper Drill Chuck Arbors (MT/JT)",
      image: "/images/C63-1.png",
      description: "Arbors featuring a Morse taper, specifically designed for connecting drill chucks",
      series: "Arbor Series",
      connection: "MT/JT",
      application: "Morse taper drill chuck connections",
      pageNumber: "C63",
    },
    {
      id: "drill-chuck-007",
      name: "Cylindrical Drill Chuck Arbors (C/JT)",
      image: "/images/C63-2.png",
      description: "Arbors with cylindrical shanks, used for connecting drill chucks",
      series: "Arbor Series",
      connection: "C/JT",
      application: "Cylindrical shank drill chuck connections",
      pageNumber: "C63",
    },
    {
      id: "drill-chuck-008",
      name: "R8 Drill Chuck Arbors (R8/JT)",
      image: "/images/C63-3.png",
      description: "Arbors with an R8 taper, designed for connecting drill chucks",
      series: "Arbor Series",
      connection: "R8/JT",
      application: "R8 taper drill chuck connections",
      pageNumber: "C63",
    },
  ]

  // Performance features based on the provided content
  const performanceFeatures = [
    {
      icon: "Target",
      title: "High-Precision Clamping (APU Series)",
      description: "Integrated APU Drill Chuck Holder engineered for superior performance with exceptional concentricity. By integrating chuck mechanism directly into precision-ground shank, tolerance stacking is eliminated, resulting in extremely low runout.",
    },
    {
      icon: "Shield",
      title: "Robust, Heavy-Duty Performance (J/JM Series)",
      description: "J and JM series keyless drill chucks designed for robustness and high clamping force, ideal for medium and heavy-duty drilling applications. Many models feature self-tightening mechanism where chuck's grip increases as drilling torque rises.",
    },
    {
      icon: "Zap",
      title: "Unmatched Versatility and Modularity",
      description: "Combination of J/JM chuck heads with vast range of arbors represents pinnacle of versatility. Modular approach allows single chuck head to be used across multiple machines simply by changing the arbor.",
    },
  ]

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Integrated System (APU Series)",
      description: "One-piece, integrated chuck and shank design. Available in multiple capacities, such as 0.2-13mm (1/64\"-1/2\") and 0.2-16mm (1/64\"-5/8\"). Extremely comprehensive shank interface types including BT, SK, NT, HSK, CAT, NBT, ISO, C (Straight Shank), MTA, R8, and MT.",
    },
    {
      title: "Modular System (Chuck Heads and Arbors)",
      description: "Chuck Heads (J/JM Series): Keyless (self-tightening) and Spanner-tightened models. Wide range from micro (0.2-3mm) up to heavy-duty (0.2-25mm). Mounting interface connects via Jacobs Tapers (J0, J1, J2, J6, J33, etc.), Morse Tapers (B10, B12, B16, B18, etc.), or Female Threads (Metric or UNF). Precision typically ranging from 0.05mm to 0.18mm.",
    },
    {
      title: "Arbors (Adapters) System",
      description: "Function to connect chuck head (with JT mount) to machine spindle. Types include: Morse Taper Arbors (MT/JT) connecting JT chucks to MT spindles (e.g., MT2-JT6). R8 Arbors (R8/JT) connecting JT chucks to R8 spindles, with standard 7/16\"-20 drawbar thread. Cylindrical Arbors (C/JT) straight shank arbors to hold JT chuck in collet or side-lock holder.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Drill Chuck Tool Holder Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG Drill Chuck Tool Holder System
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  As a senior industrial tooling expert at MZG Tool Machine Company, I am pleased to present a comprehensive and detailed introduction to the Drill Chuck Tool Holder. This system is the quintessential solution for holding straight-shank drilling tools and is a foundational component in virtually every machine shop, from high-production CNC centers to conventional manual mills. The system is broadly divided into two main philosophies: fully integrated, high-precision holders (like the APU series) and a highly versatile modular system of chuck heads (like the J/JM series) combined with separate arbors.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/millingcutter1.png"
                    alt="MZG Professional Drill Chuck Tool Holder System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">System Performance Analysis</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of a Drill Chuck Tool Holder is judged by its clamping security, precision (runout), and adaptability to different machining environments. The integrated APU Drill Chuck Holder is engineered for superior performance with exceptional concentricity.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      By integrating the chuck mechanism directly into a precision-ground shank (BT, HSK, CAT, etc.), tolerance stacking is eliminated, resulting in extremely low runout. This precision is paramount for producing straight, accurately sized holes, especially with small-diameter drills.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The J and JM series keyless drill chucks are designed for robustness and high clamping force, making them ideal for medium and heavy-duty drilling applications. Many models feature self-tightening mechanism, where chuck's grip increases as drilling torque rises.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The combination of J/JM chuck heads with vast range of arbors represents pinnacle of versatility. This modular approach allows single chuck head to be used across multiple machines simply by changing the arbor.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">System Types: APU, JM, J Series, Arbor Series</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Clamping Range: 0.2-25mm (Micro to Heavy-duty)</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Precision: 0.05mm to 0.18mm</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Interfaces: BT, SK, NT, HSK, CAT, NBT, ISO, C, MTA, R8, MT</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Application: Straight-shank drilling tools</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                          <span className="text-gray-900 text-right">{product.series}</span>
                        </div>
                      )}
                      {product.connection && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Connection:</span>
                          <span className="text-gray-900 text-right">{product.connection}</span>
                        </div>
                      )}
                      {product.application && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.application}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultDrillChuckImages[0]}
                    alt="Drill Chuck Tool Holder Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                    </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultDrillChuckImages[imageIndex % defaultDrillChuckImages.length]
                  : defaultDrillChuckImages[index % defaultDrillChuckImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Drill Chuck Tool Holder Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                    </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Integrated System (APU Series)":
                      return <Target className="h-6 w-6 text-blue-600 mr-3" />
                    case "Modular System (Chuck Heads and Arbors)":
                      return <Zap className="h-6 w-6 text-green-600 mr-3" />
                    case "Arbors (Adapters) System":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                    </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios &amp; Applicable Machining</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Integrated APU Holders:</strong> Premier choice for modern CNC machining centers. High precision essential for applications in aerospace, mold & die, and medical manufacturing where hole accuracy and tool life are critical. Availability in high-speed HSK and other advanced shanks makes them perfect for high-RPM drilling cycles</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>General CNC Milling:</strong> Straight-shank arbor holding J-series chuck provides quick and robust solution for non-critical drilling operations</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Conventional Machining:</strong> R8 arbor with J-series chuck is standard setup for manual Bridgeport-style mills. Morse Taper arbor essential for use in larger manual mills and drill presses</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Lathe Operations:</strong> Morse Taper or straight-shank arbor allows these chucks to be used in tailstock of lathe for drilling operations</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Fabrication and Maintenance Shops:</strong> System's robustness and adaptability make it ideal for MRO (Maintenance, Repair, and Operations) and general fabrication work</span>
                  </li>
                </ul>
              </div>

              {/* Machining Operations */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Main Functions
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Securely Grip Straight-Shank Tools:</strong> Fundamental purpose is to provide concentric, high-force clamp on cylindrical shank of drill bit, reamer, or other tool</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide High-Precision Interface for CNC Machining (APU):</strong> Primary function in this context is to minimize runout, thereby guaranteeing hole accuracy and maximizing performance and life of cutting tools</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Offer Robust and Versatile Modular Solution (J/JM + Arbors):</strong> Main function is to deliver reliable, adaptable, and cost-effective system for drilling across widest possible range of new and legacy machinery</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Enable System Adaptability:</strong> Function of arbor system is to act as crucial bridge, allowing standardized chuck heads to seamlessly connect to various machine spindle interfaces, ensuring maximum utility of tooling assets</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Core Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Securely Grip Straight-Shank Tools:</strong> Fundamental purpose is to provide concentric, high-force clamp on cylindrical shank of drill bit, reamer, or other tool</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide High-Precision Interface for CNC Machining (APU):</strong> Primary function is to minimize runout, thereby guaranteeing hole accuracy and maximizing performance and life of cutting tools</span>
                  </li>
                </ul>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  System Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Offer Robust and Versatile Modular Solution (J/JM + Arbors):</strong> Main function is to deliver reliable, adaptable, and cost-effective system for drilling across widest possible range of new and legacy machinery</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Enable System Adaptability:</strong> Function of arbor system is to act as crucial bridge, allowing standardized chuck heads to seamlessly connect to various machine spindle interfaces, ensuring maximum utility of tooling assets</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/milling-tool-holder/drill-chuck" />
            </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional Drill Chuck Tool Holder Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal drill chuck tool holders for your straight-shank drilling applications. From high-precision APU integrated systems to versatile modular J/JM chuck solutions, we provide the foundational components for every machine shop.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
            </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same milling-tool-holder directory
                const allToolHolderCategories = [
                  {
                    title: "SK High-Speed Tool Holders",
                    image: "/images/C05-1.png",
                    description: "High-speed precision tool holders",
                  url: "/standard-tools/milling-tool-holder/sk-high-speed",
                },
                  {
                    title: "HM Hydraulic Tool Holders",
                    image: "/images/C09-1.png",
                    description: "Precision hydraulic clamping systems",
                    url: "/standard-tools/milling-tool-holder/hm-hydraulic",
                  },
                {
                  title: "Tapping Tool Holders",
                    image: "/images/C55-1.png",
                    description: "Specialized tapping protection systems",
                  url: "/standard-tools/milling-tool-holder/tapping-tool-holder",
                },
                {
                    title: "ER Tool Holders",
                    image: "/images/c41-1.png",
                    description: "Versatile collet chuck systems",
                    url: "/standard-tools/milling-tool-holder/er-tool-holder",
                  },
                  {
                    title: "Morse Taper Tool Holders",
                    image: "/images/c71-1.png",
                    description: "Foundational precision tool holders",
                    url: "/standard-tools/milling-tool-holder/morse-taper",
                  },
                ];
                
                return allToolHolderCategories.slice(0, 5).map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Tool Holders" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
} 