"use client"

import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function RightAngleEndMillPage() {
  // Right Angle End Mill相关的默认图片
  const defaultRightAngleEndMillImages = [
    "/images/D04-2.png",
    "/images/D07-1.png", 
    "/images/D11-1.png",
    "/images/D15-1.png",
    "/images/D17-1.png",
    "/images/D18-1.png",
    "/images/D19-1.png",
    "/images/D20-1.png",
    "/images/D23-1.png",
    "/images/D24-2.png",
    "/images/D25-2.png",
    "/images/D26-2.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/clamp-type-milling/right-angle-end-mill");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认图片
          setGalleryImages(defaultRightAngleEndMillImages);
        }
      } else {
        // API请求失败，使用默认图片
        setGalleryImages(defaultRightAngleEndMillImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认图片
      setGalleryImages(defaultRightAngleEndMillImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认图片，避免显示无关图片
    setGalleryImages(defaultRightAngleEndMillImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data for Right Angle End Mills (保持原有产品内容)
  const products = [
    {
      id: "right-angle-end-001",
      name: "4NKT End Mill",
      image: "/images/D04-2.png",
      description: "Double-sided four-corner milling cutter head",
      insertType: "4NK(H)T 0603..",
      application: "双面四角铣削",
      page: "D04",
      url: "/standard-tools/clamp-type-milling/right-angle-end-mill/4NKT",
    },
    {
      id: "right-angle-end-002",
      name: "R217.96 Clip-Type Fast Feed End Milling Cutter",
      image: "/images/D07-1.png",
      description: "High-feed, high-efficiency milling solution",
      insertType: "WNMU0404..",
      application: "高进给，高效率",
      page: "D07",
      url: "/standard-tools/clamp-type-milling/right-angle-end-mill/R217-96",
    },
    {
      id: "right-angle-end-003",
      name: "M6NTE90 直 Right Angle Shoulder Milling Cutter",
      image: "/images/D11-1.png",
      description: "Economical and efficient double-sided hexagonal insert cutter",
      insertType: "M6NGU0604.., M6NGU0905..",
      application: "双面六角刀片，经济高效",
      page: "D11",
      url: "/standard-tools/clamp-type-milling/right-angle-end-mill/M6NTE90",
    },
    {
      id: "right-angle-end-004",
      name: "3PTE Right Angle Shoulder Milling Cutter",
      image: "/images/D11-1.png",
      description: "High-efficiency heavy-duty milling cutter",
      insertType: "3PKT1004.., 3PKT1505.., 3PKT1906..",
      application: "高效强力铣削",
      page: "D13",
      url: "/standard-tools/clamp-type-milling/right-angle-end-mill/3PTE",
    },
    {
      id: "right-angle-end-005",
      name: "AHU Right Angle Shoulder Milling Cutter",
      image: "/images/D15-1.png",
      description: "Heavy-duty cutting with sharp and robust inserts",
      insertType: "JDMT1003.., JDMT1505..",
      application: "强力切削，锋利强固",
      page: "D15",
      url: "/standard-tools/clamp-type-milling/right-angle-end-mill/AHU",
    },
    {
      id: "right-angle-end-006",
      name: "ASM Right angle shoulder fine milling cutter",
      image: "/images/D17-1.png",
      description: "Suitable for heavy cutting with 3x rotation and feed rates",
      insertType: "JDMT0702..",
      application: "适合强力切削，回转及进刀量是一般铣刀的3倍",
      page: "D17",
      url: "/standard-tools/clamp-type-milling/right-angle-end-mill/ASM",
    },
    {
      id: "right-angle-end-007",
      name: "AP1003 Right Angle Shoulder Milling Cutter",
      image: "/images/D18-1.png",
      description: "Heavy-duty cutting face mill for demanding applications",
      insertType: "APKT1003.., APKX1003..",
      application: "强力切削",
      page: "D18",
      url: "/standard-tools/clamp-type-milling/right-angle-end-mill/AP1003",
    },
    {
      id: "right-angle-end-008",
      name: "AP10 integrated high strength square shoulder milling cutter",
      image: "/images/D19-1.png",
      description: "Integrated shank design with excellent high-speed performance",
      insertType: "APKT1003..",
      application: "整体式刀柄，高速性能好",
      page: "D19",
      url: "/standard-tools/clamp-type-milling/right-angle-end-mill/AP10",
    },
    {
      id: "right-angle-end-009",
      name: "BAP300R/BAP400R End Mill",
      image: "/images/D20-1.png",
      description: "Heavy-duty cutting with high cost-performance ratio",
      insertType: "APMT1135.., APMT1604..",
      application: "强力切削，性价比高",
      page: "D20",
      url: "/standard-tools/clamp-type-milling/right-angle-end-mill/BAP300R-BAP400R",
    },
    {
      id: "right-angle-end-010",
      name: "R390 Right angle shoulder fine milling cutter",
      image: "/images/D23-1.png",
      description: "High precision for fine machining applications",
      insertType: "R390-11T3.., R390-1704..",
      application: "精度高，适于精加工",
      page: "D23",
      url: "/standard-tools/clamp-type-milling/right-angle-end-mill/R390",
    },
    {
      id: "right-angle-end-011",
      name: "R390 Integrated High Strength Square Shoulder Milling Cutter",
      image: "/images/D24-2.png",
      description: "Integrated rigid structure with excellent high-speed performance",
      insertType: "R390-11T308",
      application: "整体式刚性结构，高速性能好",
      page: "D24",
      url: "/standard-tools/clamp-type-milling/right-angle-end-mill/R390-Integrated",
    },
    {
      id: "right-angle-end-012",
      name: "RT Right Angle Shoulder Fine Milling Cutter",
      image: "/images/D25-2.png",
      description: "High precision fine milling cutter for finishing operations",
      insertType: "RT0702.., RT1003..",
      application: "精度高，适于精加工",
      page: "D25",
      url: "/standard-tools/clamp-type-milling/right-angle-end-mill/RT",
    },
    {
      id: "right-angle-end-013",
      name: "T217.69 Right Angle Shoulder Fine Milling Cutter",
      image: "/images/D26-2.png",
      description: "Economical fine milling cutter solution",
      insertType: "XOMX (0602 to 1204)",
      application: "经济型精铣刀",
      page: "D26",
      url: "/standard-tools/clamp-type-milling/right-angle-end-mill/T217-69",
    },
    {
      id: "right-angle-end-014",
      name: "EPX3000 Right Angle Shoulder Fine Milling Cutter",
      image: "/images/D27-1.png",
      description: "Multi-functional for plunge milling, face milling, shoulder and slot machining",
      insertType: "AOMT1236..",
      application: "多功能，可用于插铣、平面、台阶面、槽加工",
      page: "D27",
      url: "/standard-tools/clamp-type-milling/right-angle-end-mill/EPX3000",
    },
    {
      id: "right-angle-end-015",
      name: "UEX Right Angle Shoulder Fine Milling Cutter",
      image: "/images/D29-1.png",
      description: "Universal end mill for general applications",
      insertType: "AP..T1202, AD..T1603",
      application: "通用型立铣刀",
      page: "D29",
      url: "/standard-tools/clamp-type-milling/right-angle-end-mill/UEX",
    },
    {
      id: "right-angle-end-016",
      name: "EDC10 Integrated High Strength Square Shoulder Milling Cutter",
      image: "/images/D29-2.png",
      description: "Integrated rigid structure with excellent high-speed performance",
      insertType: "EDCT10T308",
      application: "整体式刚性结构，高速性能好",
      page: "D29",
      url: "/standard-tools/clamp-type-milling/right-angle-end-mill/EDC10",
    },
    {
      id: "right-angle-end-017",
      name: "TE90 Right Angle Shoulder Fine Milling Cutter",
      image: "/images/D30-1.png",
      description: "Designed for precision finishing operations",
      insertType: "AXMT (0602, 0903)",
      application: "用于精加工",
      page: "D30",
      url: "/standard-tools/clamp-type-milling/right-angle-end-mill/TE90",
    },
         {
       id: "right-angle-end-018",
       name: "TP Shoulder End Mill",
       image: "/images/D31-1.png",
       description: "Traditional insert configuration with low cutting costs",
       insertType: "TPKN/TPMR (1103, 1603, 2204)",
       application: "配备传统刀片，切削成本低",
       page: "D31",
       url: "/standard-tools/clamp-type-milling/right-angle-end-mill/TP",
     },
  ]

  // Performance features
  const performanceFeatures = [
    {
      icon: "Shield",
      title: "Extreme Roughing and Heavy Cutting Performance",
      description: "AP1003 and ASM series deliver exceptional performance with rotation and feed rates three times that of normal cutters, featuring robust cutter body and super-strong inserts for maximum stock removal.",
    },
    {
      icon: "Zap", 
      title: "Ultimate High-Feed Milling Performance",
      description: "R217.96 and MFWN series utilize high-feed strategy with shallow depth of cut and aggressive feed rates, directing forces axially into the spindle for phenomenal metal removal rates.",
    },
    {
      icon: "Target",
      title: "High-Precision and Fine Finishing Performance",
      description: "R390 series and ASM series with incredible insert runout accuracy within 5μm, ensuring dimensional accuracy and superior surface finish for critical applications.",
    },
  ]

  // Helper function to render icons
  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case "Shield":
        return <Shield className="h-8 w-8 text-red-600" />
      case "Zap":
        return <Zap className="h-8 w-8 text-red-600" />
      case "Target":
        return <Target className="h-8 w-8 text-red-600" />
      default:
        return <Tool className="h-8 w-8 text-red-600" />
    }
  }

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Insert Systems & Physical Formats",
      description: "Supports vast range of industry-standard inserts including LMMU, 4NK(H)T, WNMU, M6NGU, 3PKT, JDMT, APKT/APKX, and R390. Available in integral shank end mill format for maximum rigidity and modular locking head format for exceptional flexibility and economy.",
    },
    {
      title: "Key Design Features",
      description: "Internal cooling holes on high-performance models deliver coolant directly to cutting zone. High-precision runout tolerance within 5μm on select models. Positive rake geometry with 11° angle reduces cutting forces and power consumption. High rigidity body with tapered cone designs minimize vibration.",
    },
    {
      title: "Material & Coating Technology",
      description: "Premium tungsten carbide substrate with advanced PVD coatings including TiAlN, AlCrN, and TiSiN. Available in both integral and modular tool formats to optimize performance and tooling costs for any application scenario.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Right Angle End Mill Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG Right Angle End Mill System
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  MZG provide a comprehensive, expert-level overview of our Right Angle End Mill systems. This is a critical and highly versatile category of indexable milling tools, engineered to bridge the gap between solid end mills and large face mills. Our portfolio provides a complete ecosystem of solutions, encompassing both integral shank ("handle type") and modular locking head designs, each optimized for specific performance criteria—from aggressive, heavy-duty material removal to the most delicate and precise finishing operations.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/millingcutter1.png"
                    alt="MZG Professional Right Angle End Mill System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">System Performance Analysis</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      As a senior industrial tooling expert at MZG Tool Machine Company, I am proud to present a detailed introduction to our comprehensive portfolio of Right Angle End Mills. These tools are the essential workhorses for creating precise 90-degree shoulders, pockets, and slots in a vast range of milling applications. Our system is not a monolithic offering; it is a sophisticated and highly engineered ecosystem comprising two primary physical formats: integral shank <strong>End Mills</strong> for maximum rigidity and reach, and modular <strong>Locking Toolholders</strong> for exceptional flexibility and economy.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      For applications demanding maximum stock removal, our <strong>AP1003 and ASM series</strong> are the designated powerhouses. Engineered for "strong cutting," these tools can sustain <strong>rotation and feed rates three times higher than normal cutters</strong>. Their performance stems from a combination of an incredibly robust body design, highly secure insert seating, and the use of strong, sharp APKT and JDMT inserts.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      When cycle time reduction is the absolute priority, our <strong>R217.96 and MFWN series</strong> deliver unparalleled performance. These cutters leverage a high-feed strategy, utilizing a shallow depth of cut with an exceptionally aggressive feed rate. The use of economical, <strong>double-sided hexagonal WNMU inserts</strong> provides up to 12 cutting edges per insert, dramatically reducing tooling costs.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      For operations where dimensional accuracy and surface quality are paramount, our system offers dedicated finishing tools. The <strong>R390 series</strong> is engineered to accept precision-ground inserts, and the <strong>ASM and AP1003 series</strong> boast an incredible <strong>insert runout accuracy of within 5μm</strong>, which is critical for eliminating witness marks and achieving superior surface finish.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3 text-sm text-gray-700">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Insert Systems:</strong> Supports vast range including LMMU, 4NK(H)T, WNMU, M6NGU, 3PKT, JDMT, APKT/APKX, R390, and TP□N inserts</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Physical Formats:</strong> Integral shank end mills for maximum rigidity and modular locking heads for flexibility</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Precision Performance:</strong> High-precision runout tolerance within 5μm on select models for superior finishing</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Advanced Features:</strong> Internal cooling holes, positive rake geometry, and high rigidity body designs</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Economic Solutions:</strong> Double-sided hexagonal inserts providing up to 12 cutting edges per insert</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.page}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.insertType && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Insert:</span>
                          <span className="text-gray-900 text-right">{product.insertType}</span>
                        </div>
                      )}
                      {product.description && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.description}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultRightAngleEndMillImages[0]}
                    alt="Right Angle End Mill Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                  </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultRightAngleEndMillImages[imageIndex % defaultRightAngleEndMillImages.length]
                  : defaultRightAngleEndMillImages[index % defaultRightAngleEndMillImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Right Angle End Mill Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                  </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Specifications</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Insert Systems & Physical Formats":
                      return <Layers className="h-6 w-6 text-blue-600 mr-3" />
                    case "Key Design Features":
                      return <Shield className="h-6 w-6 text-green-600 mr-3" />
                    case "Material & Coating Technology":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                    </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios & Processing</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Pocket and Slot Milling:</strong> Primary application with AHU series ideal for plunging and opening up pockets, while high-feed cutters clear material at incredible speeds</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Heavy Shoulder Roughing:</strong> AP1003 and LN11R series deployed for deep, aggressive cuts to rough out perpendicular shoulders on steel blocks and mold components</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Speed Dynamic Milling:</strong> Lightweight, rigid integrated cutters perfectly suited for modern high-speed machining toolpaths, including trochoidal milling</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Precision Shoulder Finishing:</strong> R390 and ASM cutters are tools of choice for final finishing pass on critical 90-degree shoulders</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Versatile Job Shop Operations:</strong> AHU and BAP series provide flexibility needed where a single tool must efficiently machine variety of features</span>
                  </li>
                </ul>
              </div>

              {/* Machining Operations */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Machining Operations
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Create Precise 90-Degree Shoulders:</strong> Fundamental purpose is to machine a geometrically true, perpendicular interface between two surfaces</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Execute Versatile Milling Operations:</strong> Efficiently perform a wide range of tasks including pocketing, slotting, plunging, and contouring with a single tool family</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Maximize Metal Removal Rate:</strong> Reduce cycle times through either heavy, deep cuts or advanced high-feed milling strategies</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Primary Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Create Precise 90-Degree Shoulders:</strong> Fundamental purpose is to machine a geometrically true, perpendicular interface between two surfaces</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Execute Versatile Milling Operations:</strong> Efficiently perform a wide range of tasks including pocketing, slotting, plunging, and contouring with a single tool family</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Maximize Metal Removal Rate:</strong> Reduce cycle times through either heavy, deep cuts or advanced high-feed milling strategies</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  Performance Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Deliver High Precision and Surface Finish:</strong> Produce parts with tight dimensional tolerances and superior surface quality with dedicated fine milling cutters</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Flexible and Economic Solutions:</strong> Offer both integral and modular tool formats and wide range of insert options to optimize performance</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Advanced Engineering Solutions:</strong> Sophisticated ecosystem bridging the gap between solid end mills and large face mills for maximum versatility</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/clamp-type-milling/right-angle-end-mill" />
          </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional Right Angle End Mill Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal right angle end mills for specific precision machining, heavy-duty material removal, and high-feed milling applications. From integral shank designs to modular locking heads, we provide comprehensive cutting solutions for all your 90-degree shoulder machining needs.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same clamp-type-milling directory
                const allClampTypeCategories = [
                  {
                    title: "Ball End Milling Cutters",
                    image: "/images/2F45CRB.png",
                    description: "3D contouring and curved surface machining",
                    url: "/standard-tools/clamp-type-milling/ball-end-milling-cutters",
                  },
                  {
                    title: "Right Angle Square Shoulder",
                    image: "/images/D03-1.png",
                    description: "Square shoulder face milling cutters",
                    url: "/standard-tools/clamp-type-milling/right-angle-square-shoulder",
                  },
                  {
                    title: "Face Milling Cutters",
                    image: "/images/D04-1.png",
                    description: "High-efficiency face milling operations",
                    url: "/standard-tools/clamp-type-milling/face-milling-cutters",
                  },
                  {
                    title: "High Feed Milling Cutter",
                    image: "/images/D07-2.png",
                    description: "High-efficiency material removal",
                    url: "/standard-tools/clamp-type-milling/high-feed-milling-cutter",
                  },
                  {
                    title: "Chamfering Cutters",
                    image: "/images/2F45C.png",
                    description: "Precision chamfering and deburring",
                    url: "/standard-tools/clamp-type-milling/chamfering-cutters",
                  },
                ];
                
                return allClampTypeCategories.map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Clamp Type Milling" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
} 