"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function HMHydraulicPage() {
  // HM Hydraulic相关的默认图片
  const defaultHMImages = [
    "/images/C09-1.png",
    "/images/C10-1.png", 
    "/images/C11-1.png",
    "/images/C12-1.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/milling-tool-holder/hm-hydraulic");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认HM图片
          setGalleryImages(defaultHMImages);
        }
      } else {
        // API请求失败，使用默认HM图片
        setGalleryImages(defaultHMImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认HM图片
      setGalleryImages(defaultHMImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认HM图片，避免显示无关图片
    setGalleryImages(defaultHMImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data for HM Hydraulic Tool Holders
  const products = [
    {
      id: "hm-001",
      name: "BT30 Hydraulic Chucks",
      image: "/images/C09-1.png",
      description: "BT30 interface hydraulic chuck for precise tool holding and superior clamping force distribution",
      series: "BT30-HM",
      application: "Medium-duty precision machining, compact tool holders",
      runout: "≤ 3µm",
      pageNumber: "C09",
    },
    {
      id: "hm-002",
      name: "BT50 Hydraulic Chucks",
      image: "/images/C10-1.png",
      description: "BT50 interface hydraulic chuck designed for heavy-duty applications with exceptional rigidity",
      series: "BT50-HM",
      application: "Heavy-duty machining, high-torque applications",
      runout: "≤ 3µm",
      pageNumber: "C10",
    },
    {
      id: "hm-003",
      name: "HSK63A Hydraulic Chucks",
      image: "/images/C11-1.png",
      description: "HSK63A interface hydraulic chuck for high-speed applications with superior balance and precision",
      series: "HSK63A-HM",
      application: "High-speed machining, precision applications",
      runout: "≤ 3µm",
      pageNumber: "C11",
    },
    {
      id: "hm-004",
      name: "HSK100A Hydraulic Chucks",
      image: "/images/C12-1.png",
      description: "HSK100A interface hydraulic chuck for heavy-duty high-speed machining with maximum stability",
      series: "HSK100A-HM",
      application: "Heavy-duty high-speed machining, large tool applications",
      runout: "≤ 3µm",
      pageNumber: "C12",
    },
  ]

  // Performance features based on the provided content
  const performanceFeatures = [
    {
      icon: "Shield",
      title: "Superior Vibration Damping",
      description: "Internal hydraulic fluid acts as a shock absorber, absorbing micro-vibrations for remarkably stable cutting action and superior surface finishes that often eliminate secondary polishing operations.",
    },
    {
      icon: "Zap", 
      title: "Unparalleled Precision and Concentricity",
      description: "Hydraulic system provides 360-degree uniform pressure with guaranteed runout of ≤3µm, ensuring exceptional centering accuracy and near-perfect concentricity for superior dimensional accuracy.",
    },
    {
      icon: "Target",
      title: "High-Speed Machining Capability",
      description: "Precision balanced to G2.5 grade for stable operation up to 25,000 RPM, enabling high-speed finishing strategies while maintaining exceptional surface quality and process stability.",
    },
  ]

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Clamping Mechanism & Precision",
      description: "Internal hydraulic expansion system actuated by single pressure screw. Runout accuracy ≤3µm at tool holder nose with h6 tolerance bore machining. 360-degree uniform hydraulic pressure provides exceptional centering accuracy and repeatability.",
    },
    {
      title: "Dynamic Balance & Speed Performance",
      description: "Precision balanced to G2.5 grade, certified for safe operation up to 25,000 RPM. Superior balance minimizes centrifugal forces and harmonic resonance at high rotational speeds for stable, efficient machining processes.",
    },
    {
      title: "Interface Types & Compatibility",
      description: "Available in BT30/BT50 (JIS B6339-AD) and HSK63A/HSK100A configurations. Clamping diameter range: 6-32mm with projection lengths 50-200mm. Includes specialized hex wrenches and reduction collets for enhanced versatility.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Hydraulic Tool Holder Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG Hydraulic Tool Holder System
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                   MZG Tool Machine Company offer a comprehensive and authoritative overview of the Hydraulic Tool Holder system. This technology represents a significant advancement in precision tool clamping, engineered specifically for applications where ultimate accuracy, superior surface finish, and vibration-free machining are non-negotiable.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/millingcutter1.png"
                    alt="MZG Professional Hydraulic Tool Holder System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">System Performance Analysis</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of Hydraulic Tool Holders is defined by a unique combination of high precision, exceptional vibration damping, and operational simplicity. The core performance feature is its inherent ability to dampen vibrations through a sealed hydraulic fluid reservoir that acts as a highly effective shock absorber, absorbing high-frequency micro-vibrations generated during machining.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      Our HM hydraulic series guarantees a runout of ≤3 micrometers (µm) through 360-degree uniform hydraulic pressure that centers the tool shank with near-perfect concentricity. Unlike mechanical collet systems, the hydraulic system self-centers the tool every time, ensuring consistent ultra-low runout critical for finishing and precision applications.
                  </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      All models are dynamically balanced to G2.5 grade for speeds up to 25,000 RPM. This superior balance minimizes centrifugal forces and harmonic resonance at high rotational speeds, making them perfectly suited for high-speed finishing strategies that reduce cycle times while enhancing surface quality.
                  </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The hydraulic system provides consistent and reliable clamping force with simple hex wrench actuation, ensuring the same clamping pressure is applied with every tool change, eliminating operator variability and providing excellent process security for high-value machining operations.
                  </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3 text-sm text-gray-700">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Runout Accuracy: ≤3µm (0.00012")</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Balance Grade: G2.5 precision</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Maximum Speed: 25,000 RPM</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Tool Tolerance: h6 precision bore</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Clamping Range: 6-32mm diameter</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {products.map((product) => (
              <div
                key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
              >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                  <Image
                    src={product.image || "/placeholder.svg"}
                    alt={product.name}
                    fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                  />
                </div>
                                    <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                          <span className="text-gray-900 text-right">{product.series}</span>
                    </div>
                      )}
                      {product.description && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.description}</p>
                    </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
                    </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultHMImages[0]}
                    alt="Hydraulic Tool Holder Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultHMImages[imageIndex % defaultHMImages.length]
                  : defaultHMImages[index % defaultHMImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Hydraulic Tool Holder Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                  </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Clamping Mechanism & Precision":
                      return <Layers className="h-6 w-6 text-blue-600 mr-3" />
                    case "Dynamic Balance & Speed Performance":
                      return <Shield className="h-6 w-6 text-green-600 mr-3" />
                    case "Interface Types & Compatibility":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                    </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                  </div>
                )
              })}
                </div>
              </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios & Applicable Machining</h2>
                  </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Finishing and Super-Finishing Operations:</strong> Primary application for producing mirror-like surface finishes in mold and die manufacturing</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Speed Milling (HSM):</strong> Particularly effective for machining aluminum, graphite, copper, and alloy steels where surface quality is critical</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Precision Reaming and Drilling:</strong> Exceptional concentricity ensures perfectly round, straight holes for aerospace and automotive applications</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Electrode Machining:</strong> Critical for machining graphite or copper electrodes for EDM processes with required smooth surfaces</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Medical and Dental Industries:</strong> Manufacturing of surgical instruments, orthopedic implants requiring supreme accuracy and flawless surfaces</span>
                  </li>
                </ul>
              </div>

              {/* Machining Operations */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Machining Operations
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Ultra-Smooth Surface Finishing:</strong> Vibration damping provides mirror-like finishes that often eliminate manual polishing</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Precision Boring:</strong> ≤3µm runout accuracy ensures exceptional hole quality and dimensional accuracy</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Complex 3D Contouring:</strong> Stable cutting action enables intricate surface machining with superior finish quality</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Speed Semi-Finishing:</strong> G2.5 balance enables productive operation up to 25,000 RPM with maintained precision</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Vibration-Sensitive Operations:</strong> Internal fluid damping eliminates chatter for stable machining of critical components</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Primary Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Maximize Surface Quality:</strong> Provides ultra-smooth cutting action by actively damping vibration, resulting in superior part finishes</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Ensure Ultimate Tool Concentricity:</strong> Grips cutting tool with near-perfect alignment to spindle axis, guaranteeing highest dimensional accuracy</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Enable Stable High-Speed Machining:</strong> Facilitates productive operation at high RPMs through precision dynamic balancing</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  Performance Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Simple and Repeatable Clamping:</strong> User-friendly hex wrench actuation ensures consistent clamping force and process reliability</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Extended Tool Life:</strong> Uniform pressure distribution and vibration damping significantly prolong cutting tool service life</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Process Security:</strong> Eliminates operator variability and provides consistent performance for high-value machining operations</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/milling-tool-holder/hm-hydraulic" />
          </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional Hydraulic Tool Holder Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal hydraulic tool holders for ultra-precision machining, vibration-sensitive operations, and demanding surface finish requirements. From ≤3µm runout accuracy to 25,000 RPM performance, we provide comprehensive hydraulic clamping solutions.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same milling-tool-holder directory
                const allToolHolderCategories = [
                  {
                    title: "SK High-Speed Tool Holders",
                    image: "/images/C05-1.png",
                    description: "High-speed precision tool holders",
                  url: "/standard-tools/milling-tool-holder/sk-high-speed",
                },
                {
                    title: "ER Tool Holders",
                    image: "/images/C01-1.png",
                    description: "Versatile collet chuck systems",
                    url: "/standard-tools/milling-tool-holder/er-tool-holder",
                  },
                  {
                    title: "Shrink Fit Tool Holders",
                    image: "/images/C03-1.png",
                    description: "Maximum rigidity and precision",
                  url: "/standard-tools/milling-tool-holder/sr-shrink-fit",
                },
                {
                    title: "Side Lock Tool Holders",
                    image: "/images/C04-1.png",
                    description: "Side clamping mechanism",
                    url: "/standard-tools/milling-tool-holder/side-lock",
                  },
                  {
                    title: "Face Milling Tool Holders",
                    image: "/images/C13-1.png",
                    description: "Heavy-duty face milling applications",
                    url: "/standard-tools/milling-tool-holder/face-milling",
                  },
                ];
                
                return allToolHolderCategories.slice(0, 5).map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Tool Holders" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
} 