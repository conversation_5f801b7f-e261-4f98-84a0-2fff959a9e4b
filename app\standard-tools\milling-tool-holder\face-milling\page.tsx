"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function FaceMillingPage() {
  // Face Milling相关的默认图片
  const defaultFaceMillingImages = [
    "/images/c65-1.png",
    "/images/c66-1.png",
    "/images/c67-1.png",
    "/images/c68-1.png",
    "/images/c69-1.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/milling-tool-holder/face-milling");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认Face Milling图片
          setGalleryImages(defaultFaceMillingImages);
        }
      } else {
        // API请求失败，使用默认Face Milling图片
        setGalleryImages(defaultFaceMillingImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认Face Milling图片
      setGalleryImages(defaultFaceMillingImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认Face Milling图片，避免显示无关图片
    setGalleryImages(defaultFaceMillingImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data based on the Face Milling Tool Holder system
  const products = [
    {
      id: "face-mill-001",
      name: "BT-FMA Milling Machine Tool Holder",
      image: "/images/c65-1.png",
      description: "Designed for face milling operations, featuring h6 tolerance, side locking screws, and central cooling. Dynamically balanced to G6.3, 8000RPM (higher speeds on request).",
      series: "BT-FMA Series",
      interface: "BT",
      tolerance: "h6",
      balance: "G6.3 @ 8000 RPM",
      features: "Side locking screws, Central cooling",
      application: "Face milling operations with high precision requirements",
      pageNumber: "C65",
    },
    {
      id: "face-mill-002",
      name: "BT-FMB Milling Machine Tool Holder",
      image: "/images/c66-1.png",
      description: "Intended for face milling operations, with h6 tolerance, side locking screws, and central cooling. Dynamically balanced to G6.3, 8000RPM (higher speeds on request).",
      series: "BT-FMB Series",
      interface: "BT",
      tolerance: "h6",
      balance: "G6.3 @ 8000 RPM",
      features: "Side locking screws, Central cooling",
      application: "High-performance face milling with enhanced cooling",
      pageNumber: "C66",
    },
    {
      id: "face-mill-003",
      name: "HSK-FMB Milling Machine Tool Holder",
      image: "/images/c67-1.png",
      description: "Used for face milling operations, featuring h6 tolerance and balanced to G6.3, 3000RPM.",
      series: "HSK-FMB Series",
      interface: "HSK",
      tolerance: "h6",
      balance: "G6.3 @ 3000 RPM",
      application: "High-speed face milling operations on HSK machines",
      pageNumber: "C67",
    },
    {
      id: "face-mill-004",
      name: "DAT-FMB Milling Machine Tool Holder",
      image: "/images/c68-1.png",
      description: "Applicable for face milling operations, with h6 tolerance and a 7/24 taper tolerance of AT3. Dynamically balanced to G6.3, 8000RPM.",
      series: "DAT-FMB Series",
      interface: "DAT",
      tolerance: "h6, 7/24 Taper: AT3",
      balance: "G6.3 @ 8000 RPM",
      application: "Ultra-precision face milling with strict tolerance requirements",
      pageNumber: "C68",
    },
    {
      id: "face-mill-005",
      name: "NT-FMA Milling Machine Tool Holder",
      image: "/images/c69-1.png",
      description: "Utilized for face milling operations, featuring h6 tolerance, and includes a plastic ring at the shank end to protect the spindle.",
      series: "NT-FMA Series",
      interface: "NT",
      tolerance: "h6",
      features: "Plastic ring for spindle protection",
      application: "Traditional face milling with spindle protection",
      pageNumber: "C69",
    },
  ]

  // Performance features based on the provided content
  const performanceFeatures = [
    {
      icon: "Shield",
      title: "Exceptional Rigidity and Stability",
      description: "Core performance characteristic achieved through large-diameter flange providing broad, stable contact face with back of milling cutter. Cutter precisely located by pilot bore and pulled tightly against face with central locking screw.",
    },
    {
      icon: "Target",
      title: "Secure Dual-Locking System",
      description: "Robust dual-locking system handles high rotational torque. Central screw provides axial clamping force, while drive keys on pilot engage with slots in cutter. Two side locking screws firmly secure drive keys.",
    },
    {
      icon: "Zap",
      title: "Guaranteed Precision and Surface Finish",
      description: "Manufactured to exacting tolerances, including pilot diameter tolerance of h6 and taper tolerance of AT3 on dual-contact models. Ensures face mill runs true with insert plane perfectly perpendicular to spindle axis.",
    },
  ]

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Cutter Mounting System & Precision Tolerances",
      description: "FMA Type: Features imperial-standard pilot diameters for cutters with inch-based mounting bores (e.g., 25.4mm for 1\", 31.75mm for 1.25\", 38.10mm for 1.5\"). FMB Type: Features metric-standard pilot diameters for cutters with metric mounting bores (e.g., 22mm, 27mm, 32mm, 40mm). Pilot Diameter (D1): h6. 7/24 Taper: AT3 (for DAT models). Inner Hole: H5 (for DAT models).",
    },
    {
      title: "Dynamic Balance & Locking Mechanism",
      description: "Standard G6.3 at 8,000 RPM. HSK models standard G6.3 at 3,000 RPM. Higher grades (e.g., G2.5) and speeds available upon request. Central locking screw (for axial pull) combined with two side locking screws on pilot drive keys (for rotational lock).",
    },
    {
      title: "Shank Interface Types & Critical Ordering Information",
      description: "Available in complete range: BT (versatile and robust workhorse for general CNC machining), HSK (for modern high-speed machining centers), DAT (Dual Contact for high-precision machines with dual-contact spindles), NT (for older, heavy-duty conventional milling machines). When ordering NT-style holders, essential to confirm required pull rod (drawbar) thread specification (metric or imperial) to ensure compatibility.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Face Mill Tool Holder Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG Face Mill Tool Holder System
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  MZG present a comprehensive and detailed introduction to our range of Face Mill Tool Holders. These holders are not merely adapters; they are the critical foundation for all face milling operations. Engineered with extreme rigidity and precision, their sole purpose is to securely mount large-diameter face milling cutters and transmit the immense torque required for high-volume metal removal, all while maintaining the stability needed for exceptional surface finishes. The design is based on a robust, multi-point contact system that ensures the cutter is held immovably against the immense forces of the cut.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/millingcutter1.png"
                    alt="MZG Professional Face Mill Tool Holder System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">System Performance Analysis</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of a Face Mill Tool Holder is defined by its rigidity, the security of its clamping mechanism, and its ability to maintain precision under heavy loads. The core performance characteristic is unyielding rigidity achieved through large-diameter flange.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      This provides broad, stable contact face with the back of the milling cutter. The cutter is precisely located by pilot bore and pulled tightly against this face with central locking screw. This rigid, face-to-face contact dampens vibration, prevents chatter.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      To handle high rotational torque, our face mill holders employ robust dual-locking system. In addition to central screw providing axial clamping force, holders feature drive keys on pilot that engage with slots in cutter. Two side locking screws firmly secure these drive keys.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The precision of the holder is paramount for achieving perfectly flat, smooth milled surface. Our holders are manufactured to exacting tolerances, including pilot diameter tolerance of h6 and taper tolerance of AT3 on dual-contact models.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Mounting Types: FMA (Imperial), FMB (Metric)</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Tolerances: Pilot Diameter h6, 7/24 Taper AT3</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Dynamic Balance: G6.3 @ 8,000/3,000 RPM</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Interfaces: BT, HSK, DAT, NT</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Application: Large-diameter face milling cutters</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                          <span className="text-gray-900 text-right">{product.series}</span>
                        </div>
                      )}
                      {product.interface && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Interface:</span>
                          <span className="text-gray-900 text-right">{product.interface}</span>
                        </div>
                      )}
                      {product.application && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.application}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultFaceMillingImages[0]}
                    alt="Face Mill Tool Holder Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                  </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultFaceMillingImages[imageIndex % defaultFaceMillingImages.length]
                  : defaultFaceMillingImages[index % defaultFaceMillingImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Face Mill Tool Holder Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                  </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Cutter Mounting System & Precision Tolerances":
                      return <Target className="h-6 w-6 text-blue-600 mr-3" />
                    case "Dynamic Balance & Locking Mechanism":
                      return <Zap className="h-6 w-6 text-green-600 mr-3" />
                    case "Shank Interface Types & Critical Ordering Information":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                    </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios &amp; Applicable Machining</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Primary Application:</strong> Their sole purpose is face milling. This includes Heavy Roughing (rapidly removing large volumes of material from face of workpiece), Fine Finishing (creating smooth, flat, and precise surfaces on large areas), and Datum Creation (machining primary flat reference surfaces on part)</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Industries:</strong> Essential in any industry that machines large metal components, including mold & die, aerospace (machining structural components), automotive (engine blocks, transmission cases), and general heavy engineering</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Machine Compatibility:</strong> The specific holder (BT, HSK, DAT, NT) is chosen to match the spindle interface of the CNC or manual milling machine being used</span>
                  </li>
                </ul>
              </div>

              {/* Machining Operations */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Main Functions
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Ultra-Rigid Mount for Face Mills:</strong> Core function is to create immovable connection between machine spindle and face milling cutter, capable of withstanding extreme cutting forces without vibration or deflection</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Ensure Positive Rotational Drive:</strong> Functions to transmit full torque of machine spindle to cutter without any slippage, primarily through use of robust drive keys secured by side-locking screws</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Guarantee Perpendicular Alignment:</strong> Functions to hold face mill in precise perpendicular alignment with spindle's axis of rotation, which is absolute requirement for producing flat machined surface</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Enable High-Performance Machining:</strong> Functions to support high-speed and high-feed milling operations by incorporating features like dynamic balancing and through-spindle coolant delivery, maximizing productivity and part quality</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Core Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Ultra-Rigid Mount for Face Mills:</strong> Core function is to create immovable connection between machine spindle and face milling cutter, capable of withstanding extreme cutting forces without vibration or deflection</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Ensure Positive Rotational Drive:</strong> Functions to transmit full torque of machine spindle to cutter without any slippage, primarily through use of robust drive keys secured by side-locking screws</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  System Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Guarantee Perpendicular Alignment:</strong> Functions to hold face mill in precise perpendicular alignment with spindle's axis of rotation, which is absolute requirement for producing flat machined surface</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Enable High-Performance Machining:</strong> Functions to support high-speed and high-feed milling operations by incorporating features like dynamic balancing and through-spindle coolant delivery, maximizing productivity and part quality</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/milling-tool-holder/face-milling" />
          </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional Face Mill Tool Holder Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal face mill tool holders for your large-diameter face milling operations. From ultra-rigid mounting to secure dual-locking systems, we provide the critical foundation for all face milling operations with extreme rigidity and precision.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same milling-tool-holder directory
                const allToolHolderCategories = [
                  {
                    title: "SK High-Speed Tool Holders",
                    image: "/images/C05-1.png",
                    description: "High-speed precision tool holders",
                    url: "/standard-tools/milling-tool-holder/sk-high-speed",
                  },
                  {
                    title: "HM Hydraulic Tool Holders",
                    image: "/images/C09-1.png",
                    description: "Precision hydraulic clamping systems",
                    url: "/standard-tools/milling-tool-holder/hm-hydraulic",
                  },
                  {
                    title: "Drill Chuck Tool Holders",
                    image: "/images/C59-1.png",
                    description: "Foundational straight-shank drilling solutions",
                    url: "/standard-tools/milling-tool-holder/drill-chuck",
                  },
                  {
                    title: "ER Tool Holders",
                    image: "/images/c41-1.png",
                    description: "Versatile collet chuck systems",
                    url: "/standard-tools/milling-tool-holder/er-tool-holder",
                  },
                  {
                    title: "Morse Taper Tool Holders",
                    image: "/images/c71-1.png",
                    description: "Foundational precision tool holders",
                    url: "/standard-tools/milling-tool-holder/morse-taper",
                  },
                ];
                
                return allToolHolderCategories.slice(0, 5).map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Tool Holders" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
} 