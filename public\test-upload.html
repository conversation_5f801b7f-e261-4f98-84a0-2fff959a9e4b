<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Upload</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>Upload Test</h1>
    <p>Test the upload API functionality</p>

    <button onclick="testAPI()" style="margin-bottom: 20px;">Test API Connection</button>
    
    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
        <p>Click here to select a file or drag and drop</p>
        <input type="file" id="fileInput" style="display: none;" accept="image/*">
    </div>
    
    <button id="uploadBtn" onclick="uploadFile()" disabled>Upload File</button>
    
    <div id="result"></div>

    <script>
        let selectedFile = null;
        
        document.getElementById('fileInput').addEventListener('change', function(e) {
            selectedFile = e.target.files[0];
            if (selectedFile) {
                document.getElementById('uploadBtn').disabled = false;
                document.getElementById('result').innerHTML = `<p>Selected: ${selectedFile.name} (${(selectedFile.size / 1024).toFixed(2)} KB)</p>`;
            }
        });
        
        async function uploadFile() {
            if (!selectedFile) {
                showResult('No file selected', 'error');
                return;
            }
            
            const uploadBtn = document.getElementById('uploadBtn');
            uploadBtn.disabled = true;
            uploadBtn.textContent = 'Uploading...';
            
            try {
                const formData = new FormData();
                formData.append('file', selectedFile);
                formData.append('type', 'blog-content');
                
                console.log('Uploading to /api/upload...');
                
                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Error response:', errorText);
                    throw new Error(`Upload failed: ${response.status} - ${errorText}`);
                }
                
                const result = await response.json();
                console.log('Upload result:', result);
                
                showResult(`Upload successful! File URL: <a href="${result.url}" target="_blank">${result.url}</a>`, 'success');
                
            } catch (error) {
                console.error('Upload error:', error);
                showResult(`Upload failed: ${error.message}`, 'error');
            } finally {
                uploadBtn.disabled = false;
                uploadBtn.textContent = 'Upload File';
            }
        }
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        async function testAPI() {
            try {
                showResult('Testing API connection...', 'success');

                // Test with a simple GET request first
                const response = await fetch('/api/upload', {
                    method: 'OPTIONS'
                });

                console.log('API test response:', response.status, response.statusText);

                if (response.status === 405) {
                    showResult('API endpoint is accessible (Method Not Allowed is expected for OPTIONS)', 'success');
                } else {
                    showResult(`API response: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
                }

            } catch (error) {
                console.error('API test error:', error);
                showResult(`API test failed: ${error.message}`, 'error');
            }
        }
        
        // Drag and drop functionality
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#007bff';
            uploadArea.style.backgroundColor = '#f8f9fa';
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = 'transparent';
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = 'transparent';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                selectedFile = files[0];
                document.getElementById('uploadBtn').disabled = false;
                document.getElementById('result').innerHTML = `<p>Selected: ${selectedFile.name} (${(selectedFile.size / 1024).toFixed(2)} KB)</p>`;
            }
        });
    </script>
</body>
</html>
