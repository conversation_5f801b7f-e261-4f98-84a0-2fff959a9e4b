// 加载环境变量
require('dotenv').config({ path: '.env.local' });

const { neon } = require("@neondatabase/serverless");

// 创建数据库连接
const sql = neon(process.env.DATABASE_URL);

async function migrateConsultationEmailFields() {
  try {
    console.log("🔧 开始为Consultation表添加邮件相关字段...");

    // 检查环境变量
    if (!process.env.DATABASE_URL) {
      console.error("❌ DATABASE_URL 环境变量未设置");
      console.log("请在项目根目录创建 .env.local 文件并添加:");
      console.log("DATABASE_URL=\"your-neon-database-url\"");
      return;
    }

    // 测试数据库连接
    const timeResult = await sql`SELECT NOW() as current_time`;
    console.log("✅ 数据库连接成功，当前时间:", timeResult[0].current_time);

    // 添加邮件发送状态字段
    await sql`
      ALTER TABLE consultation ADD COLUMN IF NOT EXISTS email_status VARCHAR(20) DEFAULT 'not_sent' 
      CHECK (email_status IN ('not_sent', 'sent', 'failed', 'sending'))
    `;
    console.log("✅ 添加email_status字段");

    // 添加收件人邮箱字段
    await sql`
      ALTER TABLE consultation ADD COLUMN IF NOT EXISTS email_recipient VARCHAR(255)
    `;
    console.log("✅ 添加email_recipient字段");

    // 添加抄送人邮箱字段
    await sql`
      ALTER TABLE consultation ADD COLUMN IF NOT EXISTS email_cc JSONB DEFAULT '[]'::jsonb
    `;
    console.log("✅ 添加email_cc字段");

    // 添加邮件发送时间字段
    await sql`
      ALTER TABLE consultation ADD COLUMN IF NOT EXISTS email_sent_at TIMESTAMP
    `;
    console.log("✅ 添加email_sent_at字段");

    // 添加邮件内容字段
    await sql`
      ALTER TABLE consultation ADD COLUMN IF NOT EXISTS email_content JSONB
    `;
    console.log("✅ 添加email_content字段");

    // 添加邮件错误信息字段
    await sql`
      ALTER TABLE consultation ADD COLUMN IF NOT EXISTS email_error_message TEXT
    `;
    console.log("✅ 添加email_error_message字段");

    // 创建邮件相关索引
    await sql`CREATE INDEX IF NOT EXISTS idx_consultation_email_status ON consultation(email_status)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_consultation_email_recipient ON consultation(email_recipient)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_consultation_email_sent_at ON consultation(email_sent_at)`;
    console.log("✅ 创建邮件相关索引");

    // 添加字段注释
    await sql`COMMENT ON COLUMN consultation.email_status IS '邮件发送状态: not_sent, sent, failed, sending'`;
    await sql`COMMENT ON COLUMN consultation.email_recipient IS '收件人邮箱地址'`;
    await sql`COMMENT ON COLUMN consultation.email_cc IS '抄送人邮箱地址数组(JSON)'`;
    await sql`COMMENT ON COLUMN consultation.email_sent_at IS '邮件发送时间'`;
    await sql`COMMENT ON COLUMN consultation.email_content IS '邮件内容(JSON: subject, body)'`;
    await sql`COMMENT ON COLUMN consultation.email_error_message IS '邮件发送失败错误信息'`;
    console.log("✅ 添加字段注释");

    // 为现有记录设置默认的email_recipient值（使用客户的email）
    await sql`
      UPDATE consultation 
      SET email_recipient = email 
      WHERE email_recipient IS NULL AND email IS NOT NULL
    `;
    console.log("✅ 为现有记录设置默认收件人邮箱");

    // 验证新字段
    const columns = await sql`
      SELECT 
        column_name, 
        data_type, 
        is_nullable, 
        column_default
      FROM information_schema.columns 
      WHERE table_schema = 'public' AND table_name = 'consultation'
      AND column_name IN ('email_status', 'email_recipient', 'email_cc', 'email_sent_at', 'email_content', 'email_error_message')
      ORDER BY column_name
    `;

    console.log("\n📋 新增字段详情:");
    columns.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'YES' ? '(可空)' : '(非空)'} ${col.column_default ? `默认: ${col.column_default}` : ''}`);
    });

    // 检查现有数据
    const rowCount = await sql`SELECT COUNT(*) as count FROM consultation`;
    console.log(`\n📊 当前记录数: ${rowCount[0].count}`);

    console.log("\n🎉 Consultation表邮件字段迁移完成！");
    console.log("\n📌 新增功能:");
    console.log("1. 邮件发送状态跟踪");
    console.log("2. 自定义收件人和抄送人");
    console.log("3. 邮件内容存储和查看");
    console.log("4. 发送失败原因记录");

  } catch (error) {
    console.error("❌ 邮件字段迁移失败:", error);
    console.error("错误详情:", error.message);
  }
}

// 执行迁移
if (require.main === module) {
  migrateConsultationEmailFields();
}

module.exports = { migrateConsultationEmailFields }; 