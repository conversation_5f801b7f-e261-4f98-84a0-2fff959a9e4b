"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Thermometer, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function SRShrinkFitPage() {
  // SR Shrink Fit相关的默认图片
  const defaultSRImages = [
    "/images/C13-1.png",
    "/images/C14-1.png", 
    "/images/C15-1.png",
    "/images/C16-1.png",
    "/images/C17-1.png",
    "/images/C18-1.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/milling-tool-holder/sr-shrink-fit");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认SR图片
          setGalleryImages(defaultSRImages);
        }
      } else {
        // API请求失败，使用默认SR图片
        setGalleryImages(defaultSRImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认SR图片
      setGalleryImages(defaultSRImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认SR图片，避免显示无关图片
    setGalleryImages(defaultSRImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data for SR Shrink Fit Tool Holders
  const products = [
    {
      id: "sr-001",
      name: "BT30-SR Shrink Fit Chuck (Thick Wall Type)",
      image: "/images/C13-1.png",
      description: "A robust, thick-walled shrink fit tool holder applicable for various cutting tools such as drills, reamers, PCD milling cutters, and general milling cutters.",
      series: "BT30-SR",
      pageNumber: "C13",
    },
    {
      id: "sr-002",
      name: "BT30-SRS Shrink Fit Chuck (Thin Wall Type)",
      image: "/images/C13-2.png",
      description: "A lightweight, thin-walled shrink fit tool holder suitable for drills, reamers, PCD milling cutters, and milling cutters.",
      series: "BT30-SRS",
      pageNumber: "C13",
    },
    {
      id: "sr-003",
      name: "BT40-SR Milling Machine Tool Holder (Thick Wall Type)",
      image: "/images/C14-1.png",
      description: "A thick-walled shrink fit tool holder for milling applications with a BT40 interface.",
      series: "BT40-SR",
      pageNumber: "C14",
    },
    {
      id: "sr-004",
      name: "BT40-SRS Milling Machine Tool Holder (Thin Wall Type)",
      image: "/images/C14-2.png",
      description: "A thin-walled shrink fit tool holder for milling applications with a BT40 interface.",
      series: "BT40-SRS",
      pageNumber: "C14",
    },
    {
      id: "sr-005",
      name: "BT50-SR Milling Machine Tool Holder (Thick Wall Type)",
      image: "/images/C15-1.png",
      description: "A thick-walled shrink fit tool holder for milling applications with a BT50 interface.",
      series: "BT50-SR",
      pageNumber: "C15",
    },
    {
      id: "sr-006",
      name: "BBT40R-SR Milling Machine Tool Holder (Thick Wall Type)",
      image: "/images/C16-1.png",
      description: "A thick-walled shrink fit tool holder with a BBT40R interface.",
      series: "BBT40R-SR",
      pageNumber: "C16",
    },
    {
      id: "sr-007",
      name: "BBT40R-SRS Milling Machine Tool Holder (Thin Wall Type)",
      image: "/images/C16-2.png",
      description: "A thin-walled shrink fit tool holder with a BBT40R interface.",
      series: "BBT40R-SRS",
      pageNumber: "C16",
    },
    {
      id: "sr-008",
      name: "ISO20-SRS Shrink Fit Chuck (Thin Wall Type)",
      image: "/images/C17-1.png",
      description: "A thin-walled shrink fit tool holder with an ISO20 interface.",
      series: "ISO20-SRS",
      pageNumber: "C17",
    },
    {
      id: "sr-009",
      name: "ISO25-SRS Shrink Fit Chuck (Thin Wall Type)",
      image: "/images/C17-2.png",
      description: "A thin-walled shrink fit tool holder with an ISO25 interface.",
      series: "ISO25-SRS",
      pageNumber: "C17",
    },
    {
      id: "sr-010",
      name: "HSK32E-SR/SRS Shrink Fit Chuck (Thick/Thin Wall Type)",
      image: "/images/C18-1.png",
      description: "A versatile shrink fit tool holder available in both thick and thin wall configurations for the HSK32E interface.",
      series: "HSK32E-SR/SRS",
      pageNumber: "C18",
    },
    {
      id: "sr-011",
      name: "HSK40E-SR Shrink Fit Chuck (Thick Wall Type)",
      image: "/images/C19-1.png",
      description: "A thick-walled shrink fit tool holder with an HSK40E interface.",
      series: "HSK40E-SR",
      pageNumber: "C19",
    },
    {
      id: "sr-012",
      name: "HSK40E-SRS Shrink Fit Chuck (Thin Wall Type)",
      image: "/images/C19-2.png",
      description: "A thin-walled shrink fit tool holder with an HSK40E interface.",
      series: "HSK40E-SRS",
      pageNumber: "C19",
    },
    {
      id: "sr-013",
      name: "HSK50E-SRS Shrink Fit Chuck (Thin Wall Type)",
      image: "/images/C19-3.png",
      description: "A thin-walled shrink fit tool holder with an HSK50E interface.",
      series: "HSK50E-SRS",
      pageNumber: "C19",
    },
    {
      id: "sr-014",
      name: "HSK50E-SR Shrink Fit Chuck (Thick Wall Type)",
      image: "/images/C20-1.png",
      description: "A thick-walled shrink fit tool holder with an HSK50E interface.",
      series: "HSK50E-SR",
      pageNumber: "C20",
    },
    {
      id: "sr-015",
      name: "HSK50A-SR Milling Machine Tool Holder (Thick Wall Type)",
      image: "/images/C20-2.png",
      description: "A thick-walled shrink fit tool holder with an HSK50A interface.",
      series: "HSK50A-SR",
      pageNumber: "C20",
    },
    {
      id: "sr-016",
      name: "HSK50A-SRS Milling Machine Tool Holder (Thin Wall Type)",
      image: "/images/C21-1.png",
      description: "A thin-walled shrink fit tool holder with an HSK50A interface.",
      series: "HSK50A-SRS",
      pageNumber: "C21",
    },
    {
      id: "sr-017",
      name: "HSK63A-SRS Milling Machine Tool Holder (Thin Wall Type)",
      image: "/images/C22-1.png",
      description: "A thin-walled shrink fit tool holder with an HSK63A interface.",
      series: "HSK63A-SRS",
      pageNumber: "C22",
    },
    {
      id: "sr-018",
      name: "HSK100A-SR Milling Machine Tool Holder (Thick Wall Type)",
      image: "/images/C22-2.png",
      description: "A thick-walled shrink fit tool holder with an HSK100A interface.",
      series: "HSK100A-SR",
      pageNumber: "C22",
    },
    {
      id: "sr-019",
      name: "BT40-SRC Milling Machine Tool Holder (Strong Type)",
      image: "/images/C23-1.png",
      description: "A strong-type shrink fit tool holder designed for the BT40 interface.",
      series: "BT40-SRC",
      pageNumber: "C23",
    },
    {
      id: "sr-020",
      name: "BT40-SRV Milling Machine Tool Holder (Enhanced Type)",
      image: "/images/C23-2.png",
      description: "An enhanced-type shrink fit tool holder designed for the BT40 interface.",
      series: "BT40-SRV",
      pageNumber: "C23",
    },
    {
      id: "sr-021",
      name: "BT40-SW Anti-Vibration Extension Rod",
      image: "/images/C24-1.png",
      description: "An anti-vibration extension rod made from tungsten steel for the BT40 interface.",
      series: "BT40-SW",
      pageNumber: "C24",
    },
    {
      id: "sr-022",
      name: "BT40-ST Extension Rod",
      image: "/images/C24-2.png",
      description: "A standard extension rod for the BT40 interface.",
      series: "BT40-ST",
      pageNumber: "C24",
    },
    {
      id: "sr-023",
      name: "H2800 Shrink Fit Machine (Air Cooled)",
      image: "/images/C25-1.png",
      description: "A cost-effective, compact, air-cooled shrink fit machine suitable for tool diameters up to φ12mm.",
      series: "H2800",
      pageNumber: "C25",
    },
    {
      id: "sr-024",
      name: "H2800A Shrink Fit Machine (Air Cooled)",
      image: "/images/C25-2.png",
      description: "An upgraded air-cooled shrink fit machine suitable for tool diameters up to φ20mm.",
      series: "H2800A",
      pageNumber: "C25",
    },
    {
      id: "sr-025",
      name: "FAX-15 Fully Automatic Intelligent Water Cooling Machine",
      image: "/images/C26-1.png",
      description: "A high-performance, fully automatic water-cooled shrink fit machine with intelligent heating and cooling.",
      series: "FAX-15",
      pageNumber: "C26",
    },
  ]

  // Performance features based on the provided content
  const performanceFeatures = [
    {
      icon: "Shield",
      title: "Unsurpassed Rigidity and Clamping Force",
      description: "The shrink fit process creates a powerful, uniform 360-degree grip on the tool shank, resulting in a tool-holder assembly that behaves almost as a single piece of metal with maximum rigidity.",
    },
    {
      icon: "Target",
      title: "Exceptional Concentricity and Dynamic Balance",
      description: "Single, symmetrical piece of steel with no moving parts provides inherent balance. Runout accuracy is typically 3µm or better, ensuring superior surface finishes and extended tool life.",
    },
    {
      icon: "Zap",
      title: "Design Versatility for Optimized Machining",
      description: "Distinct designs for different applications: SRS (Thin Wall) for accessibility in 5-axis machining, SR (Thick Wall) for maximum rigidity, and specialized SRC/SRV types for extreme operations.",
    },
  ]

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Clamping Principle & Holder Types",
      description: "Thermal expansion via induction heating and subsequent contraction through cooling. Available in SR (Thick Wall, 4.5° nose angle), SRS (Thin Wall, 3° nose angle), SRC (Strong Type, 3° nose angle), and SRV (Enhanced Type, 5° nose angle) configurations.",
    },
    {
      title: "Interface Types & Compatibility",
      description: "Broad machine compatibility with BT/BBT (BT30, BT40, BT50, BBT40R), HSK (HSK32E, HSK40E, HSK50E, HSK50A, HSK63A, HSK100A), and ISO (ISO20, ISO25) interfaces. Clamping diameter range from 3mm to 25mm.",
    },
    {
      title: "Shrink Fit Machines & Extensions",
      description: "Complete system including H2800/H2800A air-cooled machines for moderate usage, FAX-15 fully automatic water-cooled machine for high-production environments, plus standard steel (ST) and tungsten steel anti-vibration (SW) extension rods.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Shrink Fit Tool Holder Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG Shrink Fit Tool Holder System
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  MZG Tool Machine Company present a comprehensive and technical introduction to the Shrink Fit Tool Holder system. This technology represents the pinnacle of tool holding, offering a quasi-monolithic connection between the tool and the holder. It is engineered for the most demanding high-speed, high-precision, and high-rigidity machining applications.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/millingcutter1.png"
                    alt="MZG Professional Shrink Fit Tool Holder System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-green-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">System Performance Analysis</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of Shrink Fit Tool Holders is defined by their unparalleled rigidity, exceptional balance, and extreme precision, derived directly from their unique clamping principle. The shrink fit process creates a powerful, uniform 360-degree grip on the tool shank by heating the holder's bore using an induction machine, expanding it just enough to insert the tool.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      As the holder cools, it contracts with tremendous force, creating a solid, interference fit that results in a tool-holder assembly that behaves almost as a single piece of metal. This monolithic union provides the highest possible rigidity, minimizing tool deflection, virtually eliminating pull-out risk even under heavy axial loads, and enabling aggressive cutting parameters for maximum material removal rates.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      Because a shrink fit holder is a single, symmetrical piece of steel with no moving parts like collets, screws, or nuts, it is inherently balanced. The runout accuracy is typically 3µm or better, ensuring that every cutting edge of the tool engages the workpiece evenly, leading to superior surface finishes, predictable tool wear, and significantly extended tool life.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The outstanding balance makes these holders ideal for High-Speed Machining (HSM), allowing them to run at very high RPMs without generating harmful vibrations that could damage the spindle or compromise part quality. The system includes specialized anti-vibration extension rods made from tungsten steel for enhanced stability.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3 text-sm text-gray-700">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Runout Accuracy: ≤3µm (0.00012")</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Clamping Range: 3-25mm diameter</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Interface Types: BT/BBT, HSK, ISO</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Wall Types: Thick (SR), Thin (SRS)</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Special Types: SRC (Strong), SRV (Enhanced)</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.slice(0, 12).map((product) => (
              <div
                key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
              >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                  <Image
                    src={product.image || "/placeholder.svg"}
                    alt={product.name}
                    fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                  />
                </div>
                                    <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                          <span className="text-gray-900 text-right">{product.series}</span>
                    </div>
                      )}
                      {product.description && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.description}</p>
                    </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultSRImages[0]}
                    alt="Shrink Fit Tool Holder Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                    </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultSRImages[imageIndex % defaultSRImages.length]
                  : defaultSRImages[index % defaultSRImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Shrink Fit Tool Holder Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                    </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Clamping Principle & Holder Types":
                      return <Thermometer className="h-6 w-6 text-blue-600 mr-3" />
                    case "Interface Types & Compatibility":
                      return <Settings className="h-6 w-6 text-green-600 mr-3" />
                    case "Shrink Fit Machines & Extensions":
                      return <Layers className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                    </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios &amp; Applicable Machining</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Mold and Die Manufacturing:</strong> SRS holders perfect for finishing complex 3D surfaces and deep ribs, while SR holders ideal for roughing out cavities</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Aerospace Industry:</strong> Essential for machining tough alloys like titanium and Inconel, where high clamping force prevents tool pull-out</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Speed Machining (HSM):</strong> Inherent balance and concentricity make shrink fit the default choice for high-RPM finishing strategies</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Precision Drilling and Reaming:</strong> Near-zero runout guarantees perfectly straight, on-size holes for critical applications</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Deep Pocket Milling:</strong> Long-reach holders with anti-vibration tungsten steel extensions enable stable deep cavity machining</span>
                  </li>
                </ul>
              </div>

              {/* Machining Operations */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Machining Operations
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Monolithic Rigidity:</strong> Tool-holder assembly behaves as single piece of metal, maximizing stiffness and torque transmission</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Superior Surface Quality:</strong> ≤3µm runout accuracy ensures even cutting edge engagement for superior finishes</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Speed Capability:</strong> Inherent balance enables high-RPM operation without harmful vibrations</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Aggressive Cutting Parameters:</strong> Maximum rigidity enables heavy material removal rates with minimal deflection</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Extended Tool Life:</strong> Uniform 360-degree grip and vibration damping significantly prolong cutting tool service life</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Primary Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Create Solid, Monolithic Connection:</strong> Unifies cutting tool and holder into single rigid assembly, maximizing stiffness and torque transmission</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Enable Ultimate Precision and Balance:</strong> Provides platform for highest levels of concentricity and balance for high-RPM machining</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Offer Strategic Profile Options:</strong> Choice between slim profiles (SRS) for accessibility and robust profiles (SR, SRC) for maximum rigidity</span>
                  </li>
                </ul>
                </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  Performance Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Secure Clamping for Demanding Operations:</strong> Reliably secures wide range of cutting tools for challenging roughing and finishing applications</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Anti-Vibration Capabilities:</strong> Specialized tungsten steel extensions provide superior vibration damping for long-reach applications</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Complete System Solution:</strong> Includes holders, extension rods, and dedicated shrink fit machines for comprehensive tooling solution</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/milling-tool-holder/sr-shrink-fit" />
          </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional Shrink Fit Tool Holder Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal shrink fit tool holders for high-speed, high-precision, and high-rigidity machining applications. From ≤3µm runout accuracy to complete system solutions including machines and extensions, we provide comprehensive shrink fit technology.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same milling-tool-holder directory
                const allToolHolderCategories = [
                  {
                    title: "HM Hydraulic Tool Holders",
                    image: "/images/C09-1.png",
                    description: "Precision hydraulic clamping systems",
                    url: "/standard-tools/milling-tool-holder/hm-hydraulic",
                  },
                  {
                    title: "SK High-Speed Tool Holders",
                    image: "/images/C05-1.png",
                    description: "High-speed precision tool holders",
                  url: "/standard-tools/milling-tool-holder/sk-high-speed",
                },
                {
                    title: "ER Tool Holders",
                    image: "/images/C01-1.png",
                    description: "Versatile collet chuck systems",
                  url: "/standard-tools/milling-tool-holder/er-tool-holder",
                },
                {
                    title: "Side Lock Tool Holders",
                    image: "/images/C04-1.png",
                    description: "Side clamping mechanism",
                    url: "/standard-tools/milling-tool-holder/side-lock",
                  },
                  {
                    title: "Face Milling Tool Holders",
                    image: "/images/C13-1.png",
                    description: "Heavy-duty face milling applications",
                    url: "/standard-tools/milling-tool-holder/face-milling",
                  },
                ];
                
                return allToolHolderCategories.slice(0, 5).map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Tool Holders" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
} 