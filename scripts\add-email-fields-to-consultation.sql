-- 为consultation表添加邮件相关字段
-- 邮件发送状态
ALTER TABLE consultation ADD COLUMN IF NOT EXISTS email_status VARCHAR(20) DEFAULT 'not_sent' CHECK (email_status IN ('not_sent', 'sent', 'failed', 'sending'));

-- 收件人邮箱（通常是客户邮箱，但可能需要重写）
ALTER TABLE consultation ADD COLUMN IF NOT EXISTS email_recipient VARCHAR(255);

-- 抄送人邮箱（JSON数组格式存储多个抄送邮箱）
ALTER TABLE consultation ADD COLUMN IF NOT EXISTS email_cc JSONB DEFAULT '[]'::jsonb;

-- 邮件发送时间
ALTER TABLE consultation ADD COLUMN IF NOT EXISTS email_sent_at TIMESTAMP;

-- 邮件内容（JSON格式存储邮件主题和正文）
ALTER TABLE consultation ADD COLUMN IF NOT EXISTS email_content JSONB;

-- 邮件发送失败原因
ALTER TABLE consultation ADD COLUMN IF NOT EXISTS email_error_message TEXT;

-- 创建邮件状态索引
CREATE INDEX IF NOT EXISTS idx_consultation_email_status ON consultation(email_status);
CREATE INDEX IF NOT EXISTS idx_consultation_email_recipient ON consultation(email_recipient);
CREATE INDEX IF NOT EXISTS idx_consultation_email_sent_at ON consultation(email_sent_at);

-- 添加字段注释
COMMENT ON COLUMN consultation.email_status IS '邮件发送状态: not_sent, sent, failed, sending';
COMMENT ON COLUMN consultation.email_recipient IS '收件人邮箱地址';
COMMENT ON COLUMN consultation.email_cc IS '抄送人邮箱地址数组(JSON)';
COMMENT ON COLUMN consultation.email_sent_at IS '邮件发送时间';
COMMENT ON COLUMN consultation.email_content IS '邮件内容(JSON: subject, body)';
COMMENT ON COLUMN consultation.email_error_message IS '邮件发送失败错误信息'; 