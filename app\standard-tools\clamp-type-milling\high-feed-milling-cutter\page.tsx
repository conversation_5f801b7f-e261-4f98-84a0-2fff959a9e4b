"use client"

import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function HighFeedMillingCutterPage() {
  // High Feed Milling Cutter相关的默认图片
  const defaultHighFeedImages = [
    "/images/D86-1.png",
    "/images/D92-1.png",
    "/images/D88-1.png",
    "/images/D93-1.png",
    "/images/D101-1.png",
    "/images/D103-1.png",
    "/images/D105-1.png",
    "/images/D107-1.png",
    "/images/D85-1.png",
    "/images/D89-1.png",
    "/images/D91-1.png",
    "/images/D94-1.png",
    "/images/D96-1.png",
    "/images/D99-1.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/clamp-type-milling/high-feed-milling-cutter");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认图片
          setGalleryImages(defaultHighFeedImages);
        }
      } else {
        // API请求失败，使用默认图片
        setGalleryImages(defaultHighFeedImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认图片
      setGalleryImages(defaultHighFeedImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认图片，避免显示无关图片
    setGalleryImages(defaultHighFeedImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data based on the High Feed Milling Cutter system (保持原有产品数据)
  const products = [
    {
      id: "hf-001",
      name: "ASRM Super Radius Mill (Screwed)",
      image: "/images/D86-1.png",
      description: "High feed milling with super radius design for efficient machining",
      series: "ASRM Series",
      insertType: "EPNW0603TN-8, EPEW0803TN-10, EDEW10T3TN-10",
      application: "高进给铣削",
      pageNumber: "D86",
    },
    {
      id: "hf-002",
      name: "AJX Fast Feed Face Milling Cutter / Head",
      image: "/images/D92-1.png",
      description: "Fast feed face milling with high efficiency performance",
      series: "AJX Series",
      insertType: "JDMW/JOMW",
      application: "高进给，高效率",
      pageNumber: "D92",
    },
    {
      id: "hf-003",
      name: "ASR Fast Feed Shell Milling Cutter",
      image: "/images/D88-1.png",
      description: "Economic 4-corner type with exceptional cutting feed speed and high deep cutting efficiency",
      series: "ASR Series",
      insertType: "EPNW0603TN-8, SDNW1205.., SDNW1505ZDTN-R15",
      application: "经济的4角型，超常的切削进给速度，深切削加工效率高",
      pageNumber: "D88",
    },
    {
      id: "hf-004",
      name: "SKS type fast feed milling cutter disc",
      image: "/images/D93-1.png",
      description: "Low speed, high feed, high efficiency milling solution",
      series: "SKS Series",
      insertType: "WDMW080520ZTR, WDMT10X6202TR",
      application: "低转速,高进给，高效率",
      pageNumber: "D93",
    },
    {
      id: "hf-005",
      name: "Double sided 10 corner milling cutter / disc",
      image: "/images/D101-1.png",
      description: "Double-sided 10-corner design for high feed face milling",
      series: "Double Sided Series",
      insertType: "PNMU..JL",
      application: "平面高进给",
      pageNumber: "D101",
    },
    {
      id: "hf-006",
      name: "TEBL/TFB Clip-Type Fast Feed End Mill/Head",
      image: "/images/D103-1.png",
      description: "Clip-type fast feed end mill for high efficiency machining",
      series: "TEBL/TFB Series",
      insertType: "BLMP0603.., BLMP0904..",
      application: "高进给，高效率",
      pageNumber: "D103",
    },
    {
      id: "hf-007",
      name: "EXN03R Clip-Type Fast Feed End Mill/Head",
      image: "/images/D105-1.png",
      description: "Clip-type fast feed end mill with robust performance",
      series: "EXN03R Series",
      insertType: "LNMU0303..",
      application: "高进给，高效率",
      pageNumber: "D105",
    },
    {
      id: "hf-008",
      name: "MFH03R Clip-Type Fast Feed End Mill/Head",
      image: "/images/D107-1.png",
      description: "Clip-type fast feed end mill with modular design",
      series: "MFH03R Series",
      insertType: "LOGU0303..",
      application: "高进给，高效率",
      pageNumber: "D107",
    },
    {
      id: "hf-009",
      name: "ASR Super Radius Mill (End Mill)",
      image: "/images/D85-1.png",
      description: "Super radius end mill for roughing, excavation, grooving, shoulder and face profiling",
      series: "ASR Series",
      insertType: "EPNW0603TN-8, EPNW0803TN-10, EDEW10T3TN-10, ED..13T4TN-15",
      application: "粗加工, 挖掘加工, 槽/切入加工, 台阶面加工, 平面仿形加工",
      pageNumber: "D85",
    },
    {
      id: "hf-010",
      name: "MFH Fast Feed Milling Cutter",
      image: "/images/D89-1.png",
      description: "Fast feed milling cutter for high efficiency machining",
      series: "MFH Series",
      insertType: "SOMT1004, SOMT1405",
      application: "高进给铣削",
      pageNumber: "D89",
    },
    {
      id: "hf-011",
      name: "AJX High Feed End Mill",
      image: "/images/D91-1.png",
      description: "High feed end mill with exceptional performance",
      series: "AJX Series",
      insertType: "JOMW/JDMW",
      application: "高进给，高效率",
      pageNumber: "D91",
    },
    {
      id: "hf-012",
      name: "SKS fast feed milling cutter",
      image: "/images/D94-1.png",
      description: "Most suitable for large feed cutting applications",
      series: "SKS Series",
      insertType: "WOMW/WDMW",
      application: "最适合大进给切削",
      pageNumber: "D94",
    },
    {
      id: "hf-013",
      name: "TXP High Feedrate End Mill / Face Milling Cutter",
      image: "/images/D96-1.png",
      description: "Uses 11° positive angle inserts with double clamping mechanism for excellent rigidity, most suitable for large feed cutting",
      series: "TXP Series",
      insertType: "WPMW06X415ZPR, WPMT080615ZSR",
      application: "使用11°正角刀片，双重夹紧机构刚性好，最适合大进给切削。",
      pageNumber: "D96",
    },
    {
      id: "hf-014",
      name: "MFPN66 Double sided 10 corner milling cutter / disc",
      image: "/images/D99-1.png",
      description: "Double-sided 10-corner design for high feed face milling",
      series: "MFPN66 Series",
      insertType: "PNMU..GM",
      application: "平面高进给",
      pageNumber: "D99",
    },
  ]

  // Performance features
  const performanceFeatures = [
    {
      icon: "Shield",
      title: "Maximum Productivity & Multi-Edge Economy",
      description: "MFPN66, TEBL, and EXN03R series utilize advanced double-sided, multi-corner inserts providing exceptional number of cutting edges for maximum economy in high-volume production, dramatically lowering tooling costs while pushing machine productivity to its absolute limits.",
    },
    {
      icon: "Zap", 
      title: "Ultimate Rigidity & High-Feed Stability",
      description: "AJX, ASR, and TXP series are engineered with extreme rigidity through double clamping mechanisms, high-rigidity conical body designs, and holistically heat-treated arbors to 48HRC, effectively suppressing vibration for confident high-feed machining even in challenging conditions.",
    },
    {
      icon: "Target",
      title: "Advanced Machining & Process Versatility",
      description: "MFH series executes complex machining processes including inclined descent milling (ramping), deep hole helical interpolation, and vertical plunge milling, allowing single tool to perform operations that typically require multiple setups, drastically reducing tool changes.",
    },
  ]

  // Helper function to render icons
  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case "Shield":
        return <Shield className="h-8 w-8 text-red-600" />
      case "Zap":
        return <Zap className="h-8 w-8 text-red-600" />
      case "Target":
        return <Target className="h-8 w-8 text-red-600" />
      default:
        return <Tool className="h-8 w-8 text-red-600" />
    }
  }

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Insert Systems & Technical Parameters",
      description: "Portfolio supports vast range of highly engineered inserts: Multi-Edge Inserts (PNMU double-sided 10-corner, BLMP0603 & LNMU0303 double-sided 4-corner), High-Strength Inserts (JDMW, JOMW, WPMW special 3-edge angle design; EPNW, EPMT, SDNW unique R-shape and 4-edge designs), and Specialized Inserts (SOMT & LOGU03 for advanced ramping and vertical plunging capabilities).",
    },
    {
      title: "Cutter Body & Clamping Technology",
      description: "Systems range from High-Precision V-Grooves (MFPN66) for accurate insert location to robust Double Clamping Mechanisms (AJX, ASR, TXP) for maximum insert security. Features include Conical Body Designs for inherent rigidity and holistically heat-treated arbors (48HRC) preventing deformation and ensuring long-term accuracy.",
    },
    {
      title: "Advanced Design Features",
      description: "Internal cooling holes deliver coolant directly to cutting zone enhancing tool life and chip evacuation. Select models like TXP manufactured to runout tolerance within 5μm, enabling high-feed strategies for semi-finishing passes. MFH series has defined parameters for specialized operations ensuring process security.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  High Feed Milling Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG High Feed Milling System
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  MZG tooling system is not merely an incremental improvement; it is a strategic technology designed to redefine productivity in milling. The core principle of high-feed milling is to utilize a shallow axial depth of cut with an exceptionally aggressive feed rate. This technique masterfully redirects the majority of cutting forces axially up into the machine spindle, maximizing stability and allowing for metal removal rates that are 3 to 5 times higher than conventional methods. Our portfolio is a highly engineered ecosystem of integral shank, modular head, and face mill solutions, each optimized for specific performance criteria, from extreme rigidity to multi-edge economy.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/high-feed-milling-hero.png"
                    alt="MZG Professional High Feed Milling System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
              <div
                key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
              </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Performance</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of our High-Feed Milling system is best understood through its specialized capabilities, engineered to conquer distinct manufacturing challenges and deliver unparalleled efficiency.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Maximum Productivity & Multi-Edge Economy:</strong> For applications where throughput and cost-per-edge are paramount, our <strong>MFPN66, TEBL, and EXN03R series</strong> are the undisputed leaders. The <strong>MFPN66</strong> utilizes an advanced <strong>double-sided, 10-corner insert</strong>, providing an exceptional number of cutting edges for maximum economy in high-volume production. The <strong>TEBL and EXN03R series</strong> further this philosophy with robust <strong>double-sided, four-corner inserts</strong>, delivering a powerful combination of high-feed capability and cost-effectiveness.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Ultimate Rigidity & High-Feed Stability:</strong> When machining with long tool overhangs or in demanding materials, stability is critical. Our <strong>AJX, ASR, and TXP series</strong> are engineered for this exact purpose. The performance of these cutters is built on a foundation of extreme rigidity, achieved through features like a <strong>double clamping mechanism</strong>, <strong>high-rigidity conical body designs</strong>, and <strong>arbors that are holistically heat-treated to 48HRC</strong>. The <strong>TXP series</strong> further elevates this by ensuring a <strong>runout accuracy within 5μm</strong>, blending high-feed roughing with high-precision capability.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Advanced Machining & Process Versatility:</strong> High-feed milling extends beyond simple facing. The <strong>MFH series</strong> is the quintessential example of a multi-functional high-feed system. Its performance is defined by its ability to execute complex and advanced machining processes, including <strong>inclined descent milling (ramping), deep hole helical interpolation, and even vertical plunge milling</strong>. The <strong>MFH03R</strong> series offers specialized performance for direct Z-axis machining, optimized for use with <strong>LOGU03 type inserts</strong> specifically for <strong>vertical plunge (insert milling) operations</strong>.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3 text-sm text-gray-700">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Metal Removal Rate:</strong> 3-5 times higher than conventional methods</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Feed Rate:</strong> 0.08-0.60 mm/t with shallow depth of cut</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Precision:</strong> TXP series runout accuracy within 5μm</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Economy:</strong> Up to 14 cutting edges per insert (XDMT heptagonal)</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Stability:</strong> Axial force direction minimizes vibration and deflection</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                          <span className="text-gray-900 text-right">{product.series}</span>
                        </div>
                      )}
                      {product.insertType && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Insert:</span>
                          <span className="text-gray-900 text-right">{product.insertType}</span>
                        </div>
                      )}
                      {(product as any).cuttingEdges && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Edges:</span>
                          <span className="text-gray-900 text-right">{(product as any).cuttingEdges}</span>
                        </div>
                      )}
                      {(product as any).feedRate && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Feed Rate:</span>
                          <span className="text-gray-900 text-right">{(product as any).feedRate}</span>
                        </div>
                      )}
                      {(product as any).runoutAccuracy && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Accuracy:</span>
                          <span className="text-gray-900 text-right">{(product as any).runoutAccuracy}</span>
                        </div>
                      )}
                      {product.description && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.description}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultHighFeedImages[0]}
                    alt="High Feed Milling Cutter Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                    </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultHighFeedImages[imageIndex % defaultHighFeedImages.length]
                  : defaultHighFeedImages[index % defaultHighFeedImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`High Feed Milling Cutter Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                    </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Insert Systems & Technical Parameters":
                      return <Layers className="h-6 w-6 text-blue-600 mr-3" />
                    case "Cutter Body & Clamping Technology":
                      return <Shield className="h-6 w-6 text-green-600 mr-3" />
                    case "Advanced Design Features":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                    </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios & Processing</h2>
            </div>
              <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Speed Face Milling:</strong> Primary application for rapidly removing material from large surfaces on mold bases, blocks, and structural components</span>
                  </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Deep Pocket and Cavity Roughing:</strong> Ideal for roughing deep pockets in mold & die and aerospace applications, minimizing vibration and tool deflection</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Helical Interpolation and Ramping:</strong> Exceptionally efficient at opening up large holes from solid material or ramping into a pocket</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Plunge Milling (Z-Axis Milling):</strong> MFH and MFH03R series specifically designed to be plunged vertically into material to quickly create slots</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Contour Roughing:</strong> Low radial engagement allows these tools to effectively follow 3D contours for roughing passes</span>
                    </li>
                  </ul>
                </div>

              {/* Main Functions */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Main Functions
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Maximize Metal Removal Rate (MRR):</strong> Remove maximum volume of material in shortest possible time</span>
                  </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Reduce Machining Cycle Time & Cost:</strong> Deliver significant financial and productivity gains by shortening processing times</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Enhance Process Stability:</strong> Enable reliable, chatter-free machining with long tool overhangs or difficult materials</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Execute Versatile Machining Processes:</strong> Perform facing, pocketing, ramping, helical interpolation, and plunging operations</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Economical Solutions:</strong> Lower overall machining costs through multi-edge indexable insert technology</span>
                    </li>
                  </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/clamp-type-milling/high-feed-milling-cutter" />
            </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional High Feed Milling Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal high feed milling cutters for maximum productivity, cost-effectiveness, and process stability. From face milling to deep pocket roughing, helical interpolation to plunge milling, we provide comprehensive high-feed cutting solutions engineered to redefine your machining productivity.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same clamp-type-milling directory
                const allClampTypeCategories = [
                  {
                    title: "Face Milling Cutters",
                    image: "/images/D04-1.png",
                    description: "High-performance indexable face milling solutions",
                    url: "/standard-tools/clamp-type-milling/face-milling-cutters",
                  },
                  {
                    title: "Right Angle Face Milling Cutters",
                    image: "/images/D03-1.png",
                    description: "Precise 90-degree shoulder machining solutions",
                    url: "/standard-tools/clamp-type-milling/right-angle-square-shoulder",
                  },
                  {
                    title: "Ball End Milling Cutters",
                    image: "/images/D46-1.png",
                    description: "3D contouring and surface finishing solutions",
                    url: "/standard-tools/clamp-type-milling/ball-end-milling-cutters",
                  },
                  {
                    title: "Chamfering Cutters",
                    image: "/images/2F45C.png",
                    description: "Precision chamfering and edge preparation",
                    url: "/standard-tools/clamp-type-milling/chamfering-cutters",
                  },
                  {
                    title: "Fillet Corner Rounding",
                    image: "/images/D34-1.png",
                    description: "Round nose milling for corner rounding",
                    url: "/standard-tools/clamp-type-milling/fillet-corner-rounding",
                  },
                ];
                
                return allClampTypeCategories.map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Clamp Type Milling" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
}