"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function TurningInsertsPage() {
  // Turning Inserts相关的默认图片
  const defaultTurningImages = [
    "/images/a01-1.png",
    "/images/a03-1.png",
    "/images/a05-1.png",
    "/images/a06-1.png",
    "/images/a07-1.png",
    "/images/a09-1.png",
    "/images/a13-1.png",
    "/images/a15-1.png",
    "/images/a19-1.png",
    "/images/a21-1.png",
    "/images/a23-1.png",
    "/images/a24-1.png",
    "/images/a25-1.png",
    "/images/a27-1.png",
    "/images/a29-1.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/lathe-turning-inserts/turning-inserts");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认Turning Inserts图片
          setGalleryImages(defaultTurningImages);
        }
      } else {
        // API请求失败，使用默认Turning Inserts图片
        setGalleryImages(defaultTurningImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认Turning Inserts图片
      setGalleryImages(defaultTurningImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认Turning Inserts图片，避免显示无关图片
    setGalleryImages(defaultTurningImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

    // Product data based on the Turning Inserts series
  const products = [
    {
      id: "ti-001",
      name: "CN.. Double Sided Cutting Edge Inserts",
      image: "/images/a01-1.png",
      description: "Cutting Operations: Finishing, Semi-finishing, Roughing, Near-heavy cutting, Semi-finishing ~ Roughing",
      series: "CN Series",
      shape: "80° Rhombic",
      chipbreaker: "Multi-operation",
      application: "Finishing, Semi-finishing, Roughing, Near-heavy cutting, Semi-finishing ~ Roughing",
      inserts: "CNGA, CNMG, CNGW, CNMM, CNMA",
      pageNumber: "A01-A02",
    },
    {   
      id: "ti-002",
      name: "DN.. Double Sided Cutting Edge Inserts",
      image: "/images/a03-1.png",
      description: "Cutting Operations: Finishing, Semi-finishing, Roughing, Near-heavy cutting, Semi-finishing ~ Roughing",
      series: "DN Series",
      shape: "55° Rhombic",
      chipbreaker: "Multi-operation",
      application: "Finishing, Semi-finishing, Roughing, Near-heavy cutting, Semi-finishing ~ Roughing",
      inserts: "DNGA, DNMG, DNGW, DNMA",
      pageNumber: "A03-A04",
    },
    {
      id: "ti-003",
      name: "KN Lathe Turning Inserts",
      image: "/images/a05-1.png",
      description: "Cutting Operations: Semi-finishing ~ Roughing",
      series: "KN Series",
      shape: "Parallelogram",
      chipbreaker: "Semi-finishing ~ Roughing",
      application: "Semi-finishing ~ Roughing",
      inserts: "KNUX",
      pageNumber: "A05",
    },
    {
      id: "ti-004",
      name: "KN/RN.. Double Sided Cutting Edge Inserts",
      image: "/images/a06-1.png",
      description: "Cutting Operations: Semi-finishing ~ Roughing",
      series: "KN/RN Series",
      shape: "Round",
      chipbreaker: "Semi-finishing ~ Roughing",
      application: "Semi-finishing ~ Roughing",
      inserts: "RNMG",
      pageNumber: "A06",
    },
    {
      id: "ti-005",
      name: "SN.. Double Sided Cutting Edge Inserts",
      image: "/images/a07-1.png",
      description: "Cutting Operations: Finishing, Semi-finishing, Roughing, Near-heavy cutting, Finishing ~ Semi-finishing, Semi-finishing ~ Roughing",
      series: "SN Series",
      shape: "90° Square",
      chipbreaker: "Multi-operation",
      application: "Finishing, Semi-finishing, Roughing, Near-heavy cutting, Finishing ~ Semi-finishing, Semi-finishing ~ Roughing",
      inserts: "SNGA, SNMG, SNGW, SNMM, SNMA",
      pageNumber: "A07-A08",
    },
    {
      id: "ti-006",
      name: "TN.. Double Sided Cutting Edge Inserts",
      image: "/images/a09-1.png",
      description: "Cutting Operations: Finishing, Semi-finishing ~ Roughing, Roughing",
      series: "TN Series",
      shape: "60° Triangular",
      chipbreaker: "Multi-operation",
      application: "Finishing, Semi-finishing ~ Roughing, Roughing",
      inserts: "TNGA, TNGG, TNMG, TNGW, TNMM",
      pageNumber: "A09-A11",
    },
    {
      id: "ti-007",
      name: "VN.. Double Sided Cutting Edge Inserts",
      image: "/images/a13-1.png",
      description: "Cutting Operations: Finishing, Finishing ~ Semi-finishing, Semi-finishing, Roughing, Semi-finishing ~ Roughing",
      series: "VN Series",
      shape: "35° Rhombic",
      chipbreaker: "Multi-operation",
      application: "Finishing, Finishing ~ Semi-finishing, Semi-finishing, Roughing, Semi-finishing ~ Roughing",
      inserts: "VNGA, VNGG, VNMG, VNGW, VNMA",
      pageNumber: "A13-A14",
    },
    {
      id: "ti-008",
      name: "WN.. Double Sided Cutting Edge Inserts",
      image: "/images/a15-1.png",
      description: "Cutting Operations: Finishing, Semi-finishing, Roughing, Finishing ~ Semi-finishing, Semi-finishing ~ Roughing",
      series: "WN Series",
      shape: "Trigon 80°",
      chipbreaker: "Multi-operation",
      application: "Finishing, Semi-finishing, Roughing, Finishing ~ Semi-finishing, Semi-finishing ~ Roughing",
      inserts: "WNGA, WNMG, WNGW, WNMA",
      pageNumber: "A15-A17",
    },
    {
      id: "ti-009",
      name: "CC.. Single Side Cutting Edge Inserts",
      image: "/images/a19-1.png",
      description: "Cutting Operations: Finishing, Finishing ~ Semi-finishing, Semi-finishing ~ Roughing",
      series: "CC Series",
      shape: "80° Rhombic",
      chipbreaker: "Positive rake",
      application: "Finishing, Finishing ~ Semi-finishing, Semi-finishing ~ Roughing",
      inserts: "CCGT, CCGW, CCMT",
      pageNumber: "A19-A20",
    },
    {
      id: "ti-010",
      name: "DC.. Single Side Cutting Edge Inserts",
      image: "/images/a21-1.png",
      description: "Cutting Operations: Finishing, Semi-finishing, Roughing, Finishing ~ Semi-finishing, Semi-finishing ~ Roughing",
      series: "DC Series",
      shape: "55° Rhombic",
      chipbreaker: "Positive rake",
      application: "Finishing, Semi-finishing, Roughing, Finishing ~ Semi-finishing, Semi-finishing ~ Roughing",
      inserts: "DCGT, DCMT",
      pageNumber: "A21-A22",
    },
    { 
      id: "ti-011",
      name: "WB Lathe Turning Inserts",
      image: "/images/a23-1.png",
      description: "Cutting Operations: Finishing",
      series: "WB Series",
      shape: "Trigon 80°",
      chipbreaker: "Finishing",
      application: "Finishing",
      inserts: "WBGT, WBMT",
      pageNumber: "A23",
    },
    {
      id: "ti-012",
      name: "RC/WB.. Single Side Cutting Edge Inserts",
      image: "/images/a24-1.png",
      description: "Cutting Operations: Finishing ~ Semi-finishing, Semi-finishing ~ Roughing",
      series: "RC/WB Series",
      shape: "Round/Trigon",
      chipbreaker: "Positive rake",
      application: "Finishing ~ Semi-finishing, Semi-finishing ~ Roughing",
      inserts: "RCGT, RCMX",
      pageNumber: "A24",
    },
    {
      id: "ti-013",
      name: "SC Lathe Turning Inserts",
      image: "/images/a25-1.png",
      description: "Cutting Operations: Finishing, Semi-finishing, Roughing, Finishing ~ Semi-finishing, Semi-finishing ~ Roughing",
      series: "SC Series",
      shape: "90° Square",
      chipbreaker: "Multi-operation",
      application: "Finishing, Semi-finishing, Roughing, Finishing ~ Semi-finishing, Semi-finishing ~ Roughing",
      inserts: "SCGT, SCGW, SCMT",
      pageNumber: "A25",
    },
    {
      id: "ti-014",
      name: "TB/TC/TP.. Single Side Cutting Edge Inserts",
      image: "/images/a27-1.png",
      description: "Cutting Operations: Finishing, Semi-finishing ~ Roughing",
      series: "TB/TC/TP Series",
      shape: "60° Triangular",
      chipbreaker: "Positive rake",
      application: "Finishing, Semi-finishing ~ Roughing",
      inserts: "TBGT, TCGT, TPGT, TCMT, TPMT, TBMT",
      pageNumber: "A27-A28",
    },
    {
      id: "ti-015",
      name: "VB/VC.. Single Side Cutting Edge Inserts",
      image: "/images/a29-1.png",
      description: "Cutting Operations: Finishing, Semi-finishing, Roughing, Finishing ~ Semi-finishing",
      series: "VB/VC Series",
      shape: "35° Rhombic",
      chipbreaker: "Positive rake",
      application: "Finishing, Semi-finishing, Roughing, Finishing ~ Semi-finishing",
      inserts: "VBGT, VCGT, VPGT, VBMT, VCMT",
      pageNumber: "A29-A31",
    },
  ]

  // Performance features
  const performanceFeatures = [
    {
      icon: "Shield",
      title: "Unmatched Strength & Economy",
      description: "Negative angle double-sided inserts deliver immense strength and exceptional economic value through double-sided design, effectively doubling cutting edges per insert and drastically reducing cost-per-edge.",
    },
    {
      icon: "Zap", 
      title: "Superior Precision & Surface Finish",
      description: "Positive angle single-sided inserts produce exceptional surface finishes with low cutting forces, creating sharp cutting edges for clean shearing action and reduced heat generation.",
    },
    {
      icon: "Target",
      title: "Ultimate Material Versatility",
      description: "Advanced grade and coating performance with comprehensive range of materials including PCD, CBN, Cermet grades, and coated carbide for every material from steel to aluminum.",
    },
  ]

  // Helper function to render icons
  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case "Shield":
        return <Shield className="h-8 w-8 text-red-600" />
      case "Zap":
        return <Zap className="h-8 w-8 text-red-600" />
      case "Target":
        return <Target className="h-8 w-8 text-red-600" />
      default:
        return <Tool className="h-8 w-8 text-red-600" />
    }
  }

  // Technical specifications
  const technicalSpecs = [
    {
      title: "ISO Geometry & Rake Angle",
      description: "Insert shapes include S (Square 90°), T (Triangular 60°), C (Rhombic 80°), D (Rhombic 55°), V (Rhombic 35°), W (Trigon 80°), and R (Round). Negative inserts have 0° side clearance for double-sided use, while positive inserts have built-in clearance angles for sharper cutting edges.",
    },
    {
      title: "Insert Grade & Coating",
      description: "Advanced materials include PCD for non-ferrous alloys, CBN for hardened steels (HRC46-60+), Cermet grades (ZN60, ZN90) for high-speed finishing, and coated carbide grades with CVD and PVD coating technologies for material-specific applications.",
    },
    {
      title: "Chipbreaker Geometry",
      description: "Molded chipbreaker geometries range from sharp geometries for light-finishing cuts producing fine chips, to very strong, open geometries for breaking heavy, thick chips during deep roughing cuts. Critical parameter for optimal chip control and surface finish.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Turning Inserts Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG Turning Inserts System
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  As a senior industrial tooling expert at MZG Tool Machine Company, I am proud to present a detailed introduction to our comprehensive portfolio of Turning Inserts. These inserts are the fundamental element of all modern turning operations, representing the pinnacle of material science and geometric design. Each insert in our catalog is the result of rigorous development, engineered to provide the optimal combination of toughness, wear resistance, and cutting-edge geometry for its intended application.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/millingcutter1.png"
                    alt="MZG Professional Turning Inserts System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Performance</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of our turning inserts is best understood by classifying them into two primary families—<strong>Negative and Positive rake inserts</strong>—each delivering a distinct set of advantages.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Unmatched Strength & Economy – Negative Angle (Double-Sided) Inserts:</strong> The performance of our Negative angle inserts (<strong>CN.., DN.., KN.., RN.., SN.., TN.., VN.., WN..</strong>) is defined by their immense strength and exceptional economic value. Featuring a 0° clearance angle, these inserts are double-sided, effectively doubling the number of available cutting edges per insert and drastically reducing the cost-per-edge. This design creates an incredibly strong and robust cutting edge, making them the undisputed choice for <strong>heavy cutting roughing</strong> and high-volume material removal.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Superior Precision & Surface Finish – Positive Angle (Single-Sided) Inserts:</strong> The performance of our Positive angle inserts (<strong>CC.., DC.., RC.., WB.., TB.., TC.., TP.., VB.., VC..</strong>) is characterized by their ability to produce exceptional surface finishes and maintain tight dimensional tolerances with low cutting forces. These single-sided inserts feature a positive clearance angle, which creates a much sharper cutting edge.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Ultimate Material Versatility – Advanced Grade & Coating Performance:</strong> Our portfolio includes a comprehensive range of advanced materials: <strong>PCD (Polycrystalline Diamond)</strong> for high-speed finishing of non-ferrous alloys, <strong>CBN (Cubic Boron Nitride)</strong> for hardened steels (HRC46-60+), <strong>Cermet Grades (ZN60, ZN90)</strong> for high-speed finishing, and <strong>Coated Carbide Grades</strong> with CVD and PVD coating technologies.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3 text-sm text-gray-700">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Double-Sided Design:</strong> Negative inserts provide twice the cutting edges for exceptional economy</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Superior Strength:</strong> 0° clearance angle creates incredibly strong cutting edges for heavy roughing</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Precision Finishing:</strong> Positive inserts deliver exceptional surface finishes with low cutting forces</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Advanced Materials:</strong> PCD, CBN, Cermet, and coated carbide grades for all applications</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>ISO Compatibility:</strong> Global standard geometries ensure predictable performance</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                          <span className="text-gray-900 text-right">{product.series}</span>
                        </div>
                      )}
                      {product.inserts && (
                        <div className="flex justify-between items-start">
                          <span className="font-medium text-gray-700 shrink-0">Inserts:</span>
                          <span className="text-gray-900 text-right text-xs break-words ml-2">{product.inserts}</span>
                        </div>
                      )}
                      {product.application && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.application}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultTurningImages[0]}
                    alt="Turning Inserts Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                  </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultTurningImages[imageIndex % defaultTurningImages.length]
                  : defaultTurningImages[index % defaultTurningImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Turning Inserts Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                  </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "ISO Geometry & Rake Angle":
                      return <Layers className="h-6 w-6 text-blue-600 mr-3" />
                    case "Insert Grade & Coating":
                      return <Shield className="h-6 w-6 text-green-600 mr-3" />
                    case "Chipbreaker Geometry":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                    </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios & Applicable Machining</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Heavy Cutting Roughing:</strong> Using strong negative inserts (SNMG, CNMM, RNMM) in energy, shipbuilding, and heavy equipment manufacturing</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>General Purpose Turning:</strong> Versatile inserts like WNMG, CNMG, and DNMG in automotive, industrial machinery, and general job shops</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Precision Finishing:</strong> Using positive inserts (CCMT, DCMT, VCGT) and Cermet grades for tight tolerances and mirror-like finishes</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Hard Turning:</strong> Specialized application using CBN inserts to machine hardened steel components (HRC &gt; 45)</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Non-Ferrous & Exotic Materials:</strong> Using sharp PCD-tipped positive inserts for aluminum, copper, titanium, and superalloys</span>
                  </li>
                </ul>
              </div>

              {/* Machining Operations */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Machining Operations
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>External Longitudinal Turning:</strong> Primary operation for cylindrical component machining with various insert geometries</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Facing Operations:</strong> Creating flat surfaces perpendicular to the axis using square and triangular inserts</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Profiling & Copy Turning:</strong> Complex geometry machining using rhombic and round inserts for versatile cutting angles</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Taper Turning:</strong> Angled surface machining utilizing insert geometry and tool positioning for precise angles</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Shoulder Turning:</strong> Step machining operations using strong corner geometries for reliable cutting performance</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Primary Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Enable Efficient Material Removal:</strong> Provide strong, reliable cutting edges that can withstand high temperatures and pressures for maximum metal removal rates</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Generate Precise Geometries:</strong> Produce components that meet stringent dimensional and geometric tolerances through precision-ground inserts</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Achieve Specified Surface Finishes:</strong> Create surface finishes from functional rough texture to mirror-like polish</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  Performance Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Optimize Material Versatility:</strong> Offer specific, optimized grade and geometry combinations for virtually any material from soft aluminum to hardened steel</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Maximize Economic Efficiency:</strong> Provide cost-effective solutions through double-sided designs, long tool life, and high productivity rates</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Reduce Manufacturing Cycle Times:</strong> Enable faster cutting speeds and higher feed rates while maintaining quality standards</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/lathe-turning-inserts/turning-inserts" />
          </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional Turning Insert Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal turning inserts for specific material and application requirements. From heavy-duty roughing to precision finishing, we provide comprehensive turning solutions for all materials and operations.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same lathe-turning-inserts directory
                const allTurningCategories = [
                  {
                    title: "Back Turning Inserts",
                    image: "/images/back-turning-insert.png",
                    description: "Specialized inserts for back turning operations",
                    url: "/standard-tools/lathe-turning-inserts/back-turning-inserts",
                  },
                  {
                    title: "Drilling Inserts",
                    image: "/images/drilling-insert.png",
                    description: "Indexable drilling insert systems",
                    url: "/standard-tools/lathe-turning-inserts/drilling-inserts",
                  },
                  {
                    title: "Grooving Cut-off Inserts",
                    image: "/images/grooving-insert.png",
                    description: "Grooving and cut-off insert solutions",
                    url: "/standard-tools/lathe-turning-inserts/grooving-cut-off-turning-inserts",
                  },
                  {
                    title: "Milling Inserts",
                    image: "/images/milling-insert.png",
                    description: "Milling inserts for turning centers",
                    url: "/standard-tools/lathe-turning-inserts/milling-inserts",
                  },
                  {
                    title: "Threading Inserts",
                    image: "/images/threading-insert.png",
                    description: "Threading insert systems",
                    url: "/standard-tools/lathe-turning-inserts/threading-inserts",
                  },
                ]
                
                return allTurningCategories.slice(0, 5).map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Lathe Turning Inserts" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
}