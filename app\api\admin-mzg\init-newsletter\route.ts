import { NextRequest, NextResponse } from 'next/server'
import { sql } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    // 创建newsletter表（PostgreSQL版本）
    await sql`
      CREATE TABLE IF NOT EXISTS newsletter (
        id SERIAL PRIMARY KEY,
        subscribe_mail VARCHAR(255) NOT NULL UNIQUE,
        ip_address VARCHAR(45),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT true
      )
    `

    // 创建IP提交次数追踪表
    await sql`
      CREATE TABLE IF NOT EXISTS newsletter_ip_tracking (
        id SERIAL PRIMARY KEY,
        ip_address VARCHAR(45) NOT NULL UNIQUE,
        submission_count INTEGER DEFAULT 1,
        last_submission TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        first_submission_in_window TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `

    // 为现有数据库添加 first_submission_in_window 列（如果不存在）
    try {
      await sql`
        ALTER TABLE newsletter_ip_tracking 
        ADD COLUMN IF NOT EXISTS first_submission_in_window TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      `
    } catch (error) {
      // 如果列已存在，忽略错误
      console.log('Column first_submission_in_window already exists or failed to add:', error)
    }

    // 创建索引提高查询性能
    await sql`CREATE INDEX IF NOT EXISTS idx_newsletter_email ON newsletter(subscribe_mail)`
    await sql`CREATE INDEX IF NOT EXISTS idx_newsletter_ip ON newsletter(ip_address)`
    await sql`CREATE INDEX IF NOT EXISTS idx_newsletter_created_at ON newsletter(created_at)`
    await sql`CREATE INDEX IF NOT EXISTS idx_ip_tracking_ip ON newsletter_ip_tracking(ip_address)`

    // 检查表是否创建成功
    const tableExists = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('newsletter', 'newsletter_ip_tracking')
    `

    return NextResponse.json({ 
      success: true, 
      message: `Newsletter数据表初始化成功！共确认 ${tableExists.length} 个表`,
      tables: tableExists.map((t: any) => t.table_name),
      note: '表已在Neon数据库中创建完成'
    })

  } catch (error) {
    console.error('Newsletter表初始化失败:', error)
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : '初始化失败' 
    }, { status: 500 })
  }
} 