"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function MorseTaperPage() {
  // Morse Taper相关的默认图片
  const defaultMorseTaperImages = [
    "/images/c71-1.png",
    "/images/c72-1.png",
    "/images/c73-1.png",
    "/images/c73-2.png",
    "/images/c74-1.png",
    "/images/c74-2.png",
    "/images/c75-1.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/milling-tool-holder/morse-taper");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认Morse Taper图片
          setGalleryImages(defaultMorseTaperImages);
        }
      } else {
        // API请求失败，使用默认Morse Taper图片
        setGalleryImages(defaultMorseTaperImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认Morse Taper图片
      setGalleryImages(defaultMorseTaperImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认Morse Taper图片，避免显示无关图片
    setGalleryImages(defaultMorseTaperImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data based on the Morse Taper Tool Holder system
  const products = [
    {
      id: "morse-taper-001",
      name: "BT-MTA Milling Machine Tool Holder",
      image: "/images/c71-1.png",
      description: "Offers high precision with a 0.015mm concentricity, even with extended drills. It is suitable for flat tail Morse taper drills, designated as MTA (TANG type, DIN 228-B).",
      series: "BT-MTA Series",
      interface: "BT",
      standard: "DIN 228-B (TANG type)",
      concentricity: "0.015mm",
      application: "Flat tail Morse taper drills with high precision requirements",
      pageNumber: "C71",
    },
    {
      id: "morse-taper-002",
      name: "NT-MTA Milling Machine Tool Holder",
      image: "/images/c72-1.png",
      description: "Features hardened and precision ground inner and outer diameters, ensuring a 0.005mm roundness. It is an MTA (TANG type, DIN 228-B) tool holder.",
      series: "NT-MTA Series",
      interface: "NT",
      standard: "DIN 228-B (TANG type)",
      concentricity: "0.005mm roundness",
      material: "Hardened and precision ground",
      application: "High-precision flat tail Morse taper operations",
      pageNumber: "C72",
    },
    {
      id: "morse-taper-003",
      name: "DAT-MTA Milling Machine Tool Holder",
      image: "/images/c73-1.png",
      description: "Provides high precision with a 0.015mm concentricity, making it suitable for flat tail Morse taper drills.",
      series: "DAT-MTA Series",
      interface: "DAT",
      standard: "DIN 228-B (TANG type)",
      concentricity: "0.015mm",
      application: "High-precision flat tail Morse taper drilling operations",
      pageNumber: "C73",
    },
    {
      id: "morse-taper-004",
      name: "HSK-MTA Milling Machine Tool Holder",
      image: "/images/c73-2.png",
      description: "Its rotation is balance-tested, maintaining a 0.005mm roundness. It is an MTA (TANG type, DIN 228-B) tool holder, suitable for flat tail Morse taper drills.",
      series: "HSK-MTA Series",
      interface: "HSK",
      standard: "DIN 228-B (TANG type)",
      concentricity: "0.005mm roundness",
      balance: "Balance-tested rotation",
      application: "High-speed flat tail Morse taper drilling with superior balance",
      pageNumber: "C73",
    },
    {
      id: "morse-taper-005",
      name: "MT Milling Machine Tool Holder",
      image: "/images/c74-1.png",
      description: "Designed to adapt between various Morse taper sizes.",
      series: "MT Adapter Series",
      interface: "Morse Taper",
      material: "High-grade tool steel",
      application: "Adaptation between various Morse taper sizes",
      pageNumber: "C74",
    },
    {
      id: "morse-taper-006",
      name: "R8-MT Milling Machine Tool Holder",
      image: "/images/c74-2.png",
      description: "A sleeve used to adapt an R8 spindle to accommodate Morse taper drills.",
      series: "R8-MT Adapter Series",
      interface: "R8 to Morse Taper",
      material: "Precision machined steel",
      application: "R8 spindle adaptation for Morse taper tools",
      pageNumber: "C74",
    },
    {
      id: "morse-taper-007",
      name: "BT-MTB Milling Machine Tool Holder",
      image: "/images/c75-1.png",
      description: "Delivers high precision with a 0.015mm concentricity, suitable for pull rod type (DIN 228-A) Morse taper drills.",
      series: "BT-MTB Series",
      interface: "BT",
      standard: "DIN 228-A (Pull rod type)",
      concentricity: "0.015mm",
      application: "Pull rod type Morse taper drilling operations",
      pageNumber: "C75",
    },
  ]

  // Performance features based on the provided content
  const performanceFeatures = [
    {
      icon: "Target",
      title: "Exceptional Precision and Concentricity",
      description: "Ground taper-on-taper contact creates near-perfect axial alignment. System consistently achieves excellent concentricity of ≤ 0.015mm, critical for straight, accurately sized holes and prolonged tool life.",
    },
    {
      icon: "Shield",
      title: "Secure Self-Locking and Positive Drive",
      description: "Shallow angle of Morse Taper creates powerful, self-locking friction fit. MTA (Tang Type) provides positive mechanical lock against rotation, MTB (Pull Rod Type) actively pulls tool into taper.",
    },
    {
      icon: "Zap",
      title: "High-Speed Capability in Modern Formats",
      description: "HSK-MTA models are dynamically balanced to elite standard of G2.5 at 25,000 RPM. Combines accuracy of Morse Taper with productivity of high RPMs in modern CNC machining centers.",
    },
  ]

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Taper System Standards & Precision",
      description: "MTA (Tang Type) conforms to DIN 228-B standard for tools with flat tang. MTB (Pull Rod Type) conforms to DIN 228-A standard for tools with internal drawbar thread. Concentricity: ≤ 0.015mm. Roundness: ≤ 0.005mm on select high-precision models.",
    },
    {
      title: "Dynamic Balance & Material Construction",
      description: "Up to G2.5 at 25,000 RPM on high-speed models (HSK). High-grade tool steel construction, fully hardened and precision ground. Superior durability and construction ensures exceptional hardness, wear resistance, and dimensional stability.",
    },
    {
      title: "Shank Interface Types & System Components",
      description: "Available in comprehensive range: BT, NT, DAT, HSK, and R8. Ecosystem includes Morse Taper Sleeves (e.g., MT2 to MT3 adapter) for adapting between different Morse Taper sizes. Essential to confirm required pull rod thread specification for compatibility.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Morse Taper Tool Holder Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG Morse Taper Tool Holder System
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  MZG present a comprehensive and detailed introduction to the Morse Taper Tool Holder. This system is a foundational and time-honored standard in the machining world, renowned for its exceptional precision, self-locking capability, and robust design. Specifically engineered for holding tools with a Morse Taper shank—most notably drills and reamers—these holders provide an incredibly reliable and accurate interface for a wide range of machining centers and conventional machines.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/millingcutter1.png"
                    alt="MZG Professional Morse Taper Tool Holder System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">System Performance Analysis</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of a Morse Taper Tool Holder is defined by its innate precision, the security of its taper-lock mechanism, and its durable construction, which guarantees consistent results in demanding applications. The defining characteristic of the Morse Taper system is its high-precision workflow.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The ground taper-on-taper contact between the holder's bore and the tool's shank creates a near-perfect axial alignment. This system consistently achieves an excellent concentricity of ≤ 0.015mm, critical for straight, accurately sized holes and prolonged tool life. Certain high-end models achieve remarkable roundness of ≤ 0.005mm.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The shallow angle of the Morse Taper creates a powerful, self-locking friction fit when the tool is seated. MTA (Tang Type) provides positive mechanical lock against rotation, essential during high-torque drilling. MTB (Pull Rod Type) utilizes drawbar that actively pulls tool into taper.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      All Morse Taper tool holders are manufactured from high-grade tool steel that has undergone complete quenching and grinding process. HSK-MTA models are dynamically balanced to elite standard of G2.5 at 25,000 RPM, combining accuracy of Morse Taper with productivity of high RPMs.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Concentricity: ≤ 0.015mm (≤ 0.005mm select models)</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Standards: DIN 228-A (MTB), DIN 228-B (MTA)</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Balance Grade: G2.5 at 25,000 RPM (HSK models)</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Interface Types: BT, NT, DAT, HSK, R8</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Material: High-grade tool steel, fully hardened</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                          <span className="text-gray-900 text-right">{product.series}</span>
                        </div>
                      )}
                      {product.application && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.application}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
                    </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultMorseTaperImages[0]}
                    alt="Morse Taper Tool Holder Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultMorseTaperImages[imageIndex % defaultMorseTaperImages.length]
                  : defaultMorseTaperImages[index % defaultMorseTaperImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Morse Taper Tool Holder Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                  </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Taper System Standards & Precision":
                      return <Target className="h-6 w-6 text-blue-600 mr-3" />
                    case "Dynamic Balance & Material Construction":
                      return <Zap className="h-6 w-6 text-green-600 mr-3" />
                    case "Shank Interface Types & System Components":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                    </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios &amp; Applicable Machining</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Precision Drilling:</strong> Primary application. Ideal for holding Morse Taper shank drills of all sizes, especially large-diameter drills where taper provides secure drive that resists high torque involved</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Reaming and Tapping:</strong> Excellent concentricity makes these holders perfect for holding high-precision reamers to achieve tight hole tolerances and for holding taper shank taps</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Adapting Legacy Tooling:</strong> Critical application in modern shops. Allow valuable, high-quality, specialized Morse Taper shank tools from older machines to be used seamlessly in modern CNC machining centers</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Conventional Machining:</strong> Native tooling for many larger conventional machines like radial arm drills and large manual milling machines. Adapters like R8-MT sleeve extend capability to Bridgeport-style mills</span>
                  </li>
                </ul>
              </div>

              {/* Machining Operations */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Main Functions
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide High-Precision Interface:</strong> Core function to securely and accurately hold tools with Morse Taper shank, guaranteeing exceptional concentricity (≤ 0.015mm) for superior hole quality</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Deliver Secure, Self-Locking Grip:</strong> Functions to create powerful friction lock that prevents tool movement, with MTA (tang) and MTB (pull rod) designs offering distinct methods of rotational and axial security</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Primary Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide High-Precision Interface for Tapered Shank Tools:</strong> Core function to securely and accurately hold tools with Morse Taper shank, guaranteeing exceptional concentricity (≤ 0.015mm) for superior hole quality</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Deliver Secure, Self-Locking Grip:</strong> Functions to create powerful friction lock that prevents tool movement, with MTA (tang) and MTB (pull rod) designs offering distinct methods of rotational and axial security</span>
                  </li>
                </ul>
                  </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  System Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Bridge Gap Between Legacy Tooling and Modern Machinery:</strong> Key function to enable use of classic, reliable Morse Taper shank drills and tools on latest generation of high-speed CNC machines</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Offer System of Size Adaptation:</strong> Through range of Morse Taper sleeves, system functions to adapt tools of one MT size to holder or spindle of another MT size, providing internal system flexibility</span>
                  </li>
                </ul>
                </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/milling-tool-holder/morse-taper" />
            </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional Morse Taper Tool Holder Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal Morse Taper tool holders for precision drilling, reaming, and tapping applications. From exceptional concentricity to self-locking capability, we provide the most reliable and accurate interface solutions for your machining needs.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same milling-tool-holder directory
                const allToolHolderCategories = [
                  {
                    title: "SK High-Speed Tool Holders",
                    image: "/images/C05-1.png",
                    description: "High-speed precision tool holders",
                    url: "/standard-tools/milling-tool-holder/sk-high-speed",
                  },
                  {
                    title: "HM Hydraulic Tool Holders",
                    image: "/images/C09-1.png",
                    description: "Precision hydraulic clamping systems",
                    url: "/standard-tools/milling-tool-holder/hm-hydraulic",
                  },
                  {
                    title: "SR Shrink Fit Tool Holders",
                    image: "/images/C13-1.png",
                    description: "Thermal expansion precision clamping",
                    url: "/standard-tools/milling-tool-holder/sr-shrink-fit",
                  },
                  {
                    title: "ER Tool Holders",
                    image: "/images/c41-1.png",
                    description: "Versatile collet chuck systems",
                    url: "/standard-tools/milling-tool-holder/er-tool-holder",
                  },
                  {
                    title: "OZ Tool Holders",
                    image: "/images/c53-1.png",
                    description: "Heavy-duty cutting tool holders",
                    url: "/standard-tools/milling-tool-holder/oz-tool-holder",
                  },
                ];
                
                return allToolHolderCategories.slice(0, 5).map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Tool Holders" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
} 