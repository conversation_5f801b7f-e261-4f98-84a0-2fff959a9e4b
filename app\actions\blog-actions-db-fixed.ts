"use server"

import { revalidatePath } from "next/cache"
import { z } from "zod"
import { sql, snakeToCamel } from "@/lib/db"

// 定义博客文章数据验证模式
const blogPostSchema = z.object({
  id: z.number().optional(),
  title: z.string().min(5, { message: "Title must be at least 5 characters." }),
  slug: z.string().min(3, { message: "Slug must be at least 3 characters." }),
  excerpt: z.string().min(10, { message: "Excerpt must be at least 10 characters." }),
  content: z.string().min(50, { message: "Content must be at least 50 characters." }),
  categoryId: z.number({ message: "Category is required." }),
  author: z.string().min(2, { message: "Author name must be at least 2 characters." }),
  imageUrl: z.string().optional().nullable(),
  featuredImageAlt: z.string().optional().nullable(),
  published: z.boolean().default(false),
  publishedAt: z.string().optional().nullable(),
  metaTitle: z.string().optional().nullable(),
  metaDescription: z.string().optional().nullable(),
  tagNames: z.array(z.string()).optional(),
})

// 定义分类数据验证模式
const categorySchema = z.object({
  id: z.number().optional(),
  name: z.string().min(2, { message: "Category name must be at least 2 characters." }),
  slug: z.string().min(2, { message: "Category slug must be at least 2 characters." }),
  description: z.string().optional().nullable(),
  color: z.string().default("#3B82F6"),
})

// 定义标签数据验证模式
const tagSchema = z.object({
  id: z.number().optional(),
  name: z.string().min(1, { message: "Tag name is required." }),
  slug: z.string().min(1, { message: "Tag slug is required." }),
})

export type BlogCategory = z.infer<typeof categorySchema> & {
  id: number
  createdAt: string
  updatedAt: string
}

export type BlogTag = z.infer<typeof tagSchema> & {
  id: number
  createdAt: string
}

export type BlogPost = {
  id: number
  title: string
  slug: string
  excerpt: string
  content: string
  categoryId: number
  author: string
  imageUrl?: string
  featuredImageAlt?: string
  published: boolean
  publishedAt?: string
  metaTitle?: string
  metaDescription?: string
  viewCount: number
  createdAt: string
  updatedAt: string
  category?: BlogCategory
  tags?: BlogTag[]
}

// 获取所有博客文章
export async function getAllBlogPosts(): Promise<BlogPost[]> {
  try {
    // 首先检查表是否存在
    const tableExists = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'blog_posts'
      )
    `
    
    if (!tableExists[0].exists) {
      console.warn("blog_posts table does not exist, returning empty array")
      return []
    }

    const postsResult = await sql`
      SELECT 
        bp.*,
        bc.id as category_id,
        bc.name as category_name,
        bc.slug as category_slug,
        bc.color as category_color
      FROM blog_posts bp
      LEFT JOIN blog_categories bc ON bp.category_id = bc.id
      ORDER BY bp.created_at DESC
    `

    const posts: BlogPost[] = []

    for (const row of postsResult) {
      const post = snakeToCamel(row) as any
      
      // 获取文章标签
      const tagsResult = await sql`
        SELECT bt.* 
        FROM blog_tags bt
        JOIN blog_post_tags bpt ON bt.id = bpt.tag_id
        WHERE bpt.post_id = ${post.id}
      `
      
      const tags = tagsResult.map(tag => snakeToCamel(tag) as BlogTag)

      posts.push({
        id: post.id,
        title: post.title,
        slug: post.slug,
        excerpt: post.excerpt,
        content: post.content,
        categoryId: post.categoryId,
        author: post.author,
        imageUrl: post.imageUrl,
        featuredImageAlt: post.featuredImageAlt,
        published: post.published,
        publishedAt: post.publishedAt?.toISOString(),
        metaTitle: post.metaTitle,
        metaDescription: post.metaDescription,
        viewCount: post.viewCount || 0,
        createdAt: post.createdAt.toISOString(),
        updatedAt: post.updatedAt.toISOString(),
        category: post.categoryName ? {
          id: post.categoryId,
          name: post.categoryName,
          slug: post.categorySlug,
          color: post.categoryColor,
          createdAt: '',
          updatedAt: ''
        } : undefined,
        tags: tags
      })
    }

    return posts
  } catch (error) {
    console.error("Error reading blog posts:", error)
    return []
  }
}

// 获取单篇博客文章
export async function getBlogPost(slug: string): Promise<BlogPost | null> {
  try {
    const postResult = await sql`
      SELECT 
        bp.*,
        bc.id as category_id,
        bc.name as category_name,
        bc.slug as category_slug,
        bc.color as category_color
      FROM blog_posts bp
      LEFT JOIN blog_categories bc ON bp.category_id = bc.id
      WHERE bp.slug = ${slug}
    `

    if (!postResult.length) {
      return null
    }

    const row = postResult[0]
    const post = snakeToCamel(row) as any

    // 获取文章标签
    const tagsResult = await sql`
      SELECT bt.* 
      FROM blog_tags bt
      JOIN blog_post_tags bpt ON bt.id = bpt.tag_id
      WHERE bpt.post_id = ${post.id}
    `
    
    const tags = tagsResult.map(tag => snakeToCamel(tag) as BlogTag)

    return {
      id: post.id,
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt,
      content: post.content,
      categoryId: post.categoryId,
      author: post.author,
      imageUrl: post.imageUrl,
      featuredImageAlt: post.featuredImageAlt,
      published: post.published,
      publishedAt: post.publishedAt?.toISOString(),
      metaTitle: post.metaTitle,
      metaDescription: post.metaDescription,
      viewCount: post.viewCount || 0,
      createdAt: post.createdAt.toISOString(),
      updatedAt: post.updatedAt.toISOString(),
      category: post.categoryName ? {
        id: post.categoryId,
        name: post.categoryName,
        slug: post.categorySlug,
        color: post.categoryColor,
        createdAt: '',
        updatedAt: ''
      } : undefined,
      tags: tags
    }
  } catch (error) {
    console.error("Error getting blog post:", error)
    return null
  }
}

// 保存博客文章
export async function saveBlogPost(
  formData: FormData,
): Promise<{ success: boolean; message: string; post?: BlogPost }> {
  try {
    // 从FormData中提取数据，处理null值
    const title = formData.get("title")
    const slug = formData.get("slug")
    const excerpt = formData.get("excerpt")
    const content = formData.get("content")
    const categoryIdStr = formData.get("categoryId")
    const author = formData.get("author")
    const imageUrl = formData.get("imageUrl")
    const featuredImageAlt = formData.get("featuredImageAlt")
    const published = formData.get("published") === "true"
    const metaTitle = formData.get("metaTitle")
    const metaDescription = formData.get("metaDescription")
    const tagsStr = formData.get("tags")

    // 验证必填字段
    if (!title || !slug || !excerpt || !content || !categoryIdStr || !author) {
      return {
        success: false,
        message: "Please fill in all required fields (title, slug, excerpt, content, category, author).",
      }
    }

    const categoryId = parseInt(categoryIdStr as string)
    if (isNaN(categoryId)) {
      return {
        success: false,
        message: "Invalid category selected.",
      }
    }

    const rawData = {
      title: title as string,
      slug: slug as string,
      excerpt: excerpt as string,
      content: content as string,
      categoryId,
      author: author as string,
      imageUrl: imageUrl ? (imageUrl as string) : null,
      featuredImageAlt: featuredImageAlt ? (featuredImageAlt as string) : null,
      published,
      publishedAt: published ? new Date().toISOString() : null,
      metaTitle: metaTitle ? (metaTitle as string) : null,
      metaDescription: metaDescription ? (metaDescription as string) : null,
      tagNames: tagsStr
        ? String(tagsStr)
            .split(",")
            .map((tag) => tag.trim())
            .filter(tag => tag.length > 0)
        : [],
    }

    // 验证表单数据
    const validatedData = blogPostSchema.parse(rawData)
    const id = formData.get("id") ? parseInt(formData.get("id") as string) : null

    // 检查是否已存在相同slug的文章（用于新增）
    if (!id) {
      const existingPost = await sql`
        SELECT id FROM blog_posts WHERE slug = ${validatedData.slug}
      `
      if (existingPost.length > 0) {
        return {
          success: false,
          message: "A post with this slug already exists. Please choose a different slug.",
        }
      }
    }

    let postId: number

    if (id) {
      // 更新现有文章
      await sql`
        UPDATE blog_posts
        SET 
          title = ${validatedData.title},
          slug = ${validatedData.slug},
          excerpt = ${validatedData.excerpt},
          content = ${validatedData.content},
          category_id = ${validatedData.categoryId},
          author = ${validatedData.author},
          image_url = ${validatedData.imageUrl},
          featured_image_alt = ${validatedData.featuredImageAlt},
          published = ${validatedData.published},
          published_at = ${validatedData.publishedAt},
          meta_title = ${validatedData.metaTitle},
          meta_description = ${validatedData.metaDescription}
        WHERE id = ${id}
      `
      postId = id
    } else {
      // 创建新文章
      const result = await sql`
        INSERT INTO blog_posts (
          title, slug, excerpt, content, category_id, author,
          image_url, featured_image_alt, published, published_at,
          meta_title, meta_description
        ) VALUES (
          ${validatedData.title},
          ${validatedData.slug},
          ${validatedData.excerpt},
          ${validatedData.content},
          ${validatedData.categoryId},
          ${validatedData.author},
          ${validatedData.imageUrl},
          ${validatedData.featuredImageAlt},
          ${validatedData.published},
          ${validatedData.publishedAt},
          ${validatedData.metaTitle},
          ${validatedData.metaDescription}
        ) RETURNING id
      `
      postId = result[0].id
    }

    // 处理标签
    if (validatedData.tagNames && validatedData.tagNames.length > 0) {
      // 删除现有标签关联
      await sql`DELETE FROM blog_post_tags WHERE post_id = ${postId}`

      // 为每个标签创建或获取ID，然后关联
      for (const tagName of validatedData.tagNames) {
        const tagSlug = tagName.toLowerCase().replace(/\s+/g, '-')
        
        // 尝试获取或创建标签
        let tagResult = await sql`
          SELECT id FROM blog_tags WHERE slug = ${tagSlug}
        `
        
        let tagId: number
        if (tagResult.length === 0) {
          const newTag = await sql`
            INSERT INTO blog_tags (name, slug) VALUES (${tagName}, ${tagSlug})
            RETURNING id
          `
          tagId = newTag[0].id
        } else {
          tagId = tagResult[0].id
        }

        // 创建文章-标签关联
        await sql`
          INSERT INTO blog_post_tags (post_id, tag_id) VALUES (${postId}, ${tagId})
          ON CONFLICT DO NOTHING
        `
      }
    }

    // 重新验证路径，更新缓存
    revalidatePath("/admin-mzg/blog")
    revalidatePath("/mzgblog")

    // 获取完整的文章数据
    const savedPost = await getBlogPost(validatedData.slug)

    return {
      success: true,
      message: id ? "Blog post updated successfully" : "Blog post created successfully",
      post: savedPost || undefined,
    }
  } catch (error) {
    console.error("Error saving blog post:", error)
    if (error instanceof z.ZodError) {
      return { success: false, message: `Validation error: ${error.errors[0].message}` }
    }
    return { success: false, message: "Failed to save blog post" }
  }
}

// 删除博客文章
export async function deleteBlogPost(id: number): Promise<{ success: boolean; message: string }> {
  try {
    await sql`DELETE FROM blog_posts WHERE id = ${id}`

    // 重新验证路径，更新缓存
    revalidatePath("/admin-mzg/blog")
    revalidatePath("/mzgblog")

    return { success: true, message: "Blog post deleted successfully" }
  } catch (error) {
    console.error("Error deleting blog post:", error)
    return { success: false, message: "Failed to delete blog post" }
  }
}

// 增加文章浏览量
export async function incrementViewCount(slug: string): Promise<void> {
  try {
    await sql`
      UPDATE blog_posts 
      SET view_count = view_count + 1 
      WHERE slug = ${slug}
    `
  } catch (error) {
    console.error("Error incrementing view count:", error)
  }
}

// 读取所有分类
export async function getAllCategories(): Promise<BlogCategory[]> {
  try {
    // 首先检查表是否存在
    const tableExists = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'blog_categories'
      )
    `
    
    if (!tableExists[0].exists) {
      console.warn("blog_categories table does not exist, returning empty array")
      return []
    }

    const result = await sql`
      SELECT * FROM blog_categories ORDER BY name ASC
    `

    return result.map(row => ({
      id: row.id,
      name: row.name,
      slug: row.slug,
      description: row.description,
      color: row.color,
      createdAt: row.created_at.toISOString(),
      updatedAt: row.updated_at.toISOString(),
    })) as BlogCategory[]
  } catch (error) {
    console.error("Error reading categories:", error)
    // 返回空数组而不是抛出错误，避免页面崩溃
    return []
  }
}

// 保存分类
export async function saveCategory(formData: FormData): Promise<{ success: boolean; message: string }> {
  try {
    const rawData = {
      name: formData.get("name"),
      slug: formData.get("slug"),
      description: formData.get("description") || null,
      color: formData.get("color") || "#3B82F6",
    }

    const validatedData = categorySchema.parse(rawData)
    const id = formData.get("id") ? parseInt(formData.get("id") as string) : null

    // 检查是否已存在相同slug的分类（用于新增）
    if (!id) {
      const existing = await sql`
        SELECT id FROM blog_categories WHERE slug = ${validatedData.slug}
      `
      if (existing.length > 0) {
        return {
          success: false,
          message: "A category with this slug already exists. Please choose a different slug.",
        }
      }
    }

    if (id) {
      // 更新现有分类
      await sql`
        UPDATE blog_categories
        SET name = ${validatedData.name}, slug = ${validatedData.slug}, 
            description = ${validatedData.description}, color = ${validatedData.color}
        WHERE id = ${id}
      `
    } else {
      // 创建新分类
      await sql`
        INSERT INTO blog_categories (name, slug, description, color)
        VALUES (${validatedData.name}, ${validatedData.slug}, ${validatedData.description}, ${validatedData.color})
      `
    }

    revalidatePath("/admin-mzg/blog/categories")

    return {
      success: true,
      message: id ? "Category updated successfully" : "Category created successfully",
    }
  } catch (error) {
    console.error("Error saving category:", error)
    if (error instanceof z.ZodError) {
      return { success: false, message: `Validation error: ${error.errors[0].message}` }
    }
    return { success: false, message: "Failed to save category" }
  }
}

// 删除分类
export async function deleteCategory(id: number): Promise<{ success: boolean; message: string }> {
  try {
    // 检查是否有文章使用此分类
    const posts = await sql`
      SELECT COUNT(*) as count FROM blog_posts WHERE category_id = ${id}
    `

    if (posts[0].count > 0) {
      return {
        success: false,
        message: "Cannot delete category that is being used by blog posts",
      }
    }

    await sql`DELETE FROM blog_categories WHERE id = ${id}`

    revalidatePath("/admin-mzg/blog/categories")

    return { success: true, message: "Category deleted successfully" }
  } catch (error) {
    console.error("Error deleting category:", error)
    return { success: false, message: "Failed to delete category" }
  }
}

// 获取所有标签
export async function getAllTags(): Promise<BlogTag[]> {
  try {
    const result = await sql`
      SELECT * FROM blog_tags ORDER BY name ASC
    `

    return result.map(row => ({
      id: row.id,
      name: row.name,
      slug: row.slug,
      createdAt: row.created_at.toISOString(),
    })) as BlogTag[]
  } catch (error) {
    console.error("Error reading tags:", error)
    return []
  }
}

// 删除标签
export async function deleteTag(id: number): Promise<{ success: boolean; message: string }> {
  try {
    await sql`DELETE FROM blog_tags WHERE id = ${id}`
    revalidatePath("/admin-mzg/blog/tags")
    return { success: true, message: "Tag deleted successfully" }
  } catch (error) {
    console.error("Error deleting tag:", error)
    return { success: false, message: "Failed to delete tag" }
  }
}