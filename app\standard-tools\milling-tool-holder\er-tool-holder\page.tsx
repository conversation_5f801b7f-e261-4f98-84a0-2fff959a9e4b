"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function ERToolHolderPage() {
  // ER Tool Holder相关的默认图片
  const defaultERImages = [
    "/images/c41-1.png",
    "/images/c42-1.png", 
    "/images/c43-1.png",
    "/images/c44-1.png",
    "/images/c45-1.png",
    "/images/c46-1.png",
    "/images/c47-1.png",
    "/images/c48-1.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/milling-tool-holder/er-tool-holder");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认ER图片
          setGalleryImages(defaultERImages);
        }
      } else {
        // API请求失败，使用默认ER图片
        setGalleryImages(defaultERImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认ER图片
      setGalleryImages(defaultERImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认ER图片，避免显示无关图片
    setGalleryImages(defaultERImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data for ER Tool Holders
  const products = [
    {
      id: 1,
      name: "ISO-ER Milling Machine Tool Holder",
      image: "/images/c41-1.png",
      series: "ISO-ER",
      application: "A high-speed tool holder for engraving machines, featuring precision ground threads, external hex nuts for ER16/ER20 collets, and balanced to G2.5, 25000RPM.",
      pageNumber: "C41"
    },
    {
      id: 2,
      name: "BT-GER Milling Machine Tool Holder",
      image: "/images/c42-1.png",
      series: "BT-GER",
      application: "A high-speed tool holder for carving and milling machines, equipped with a non-eccentric, full balanced round nut for reduced noise and minimal wind resistance.",
      pageNumber: "C42"
    },
    {
      id: 3,
      name: "BT-ER Milling Machine Tool Holder",
      image: "/images/c43-1.png",
      series: "BT-ER",
      application: "Features precision ground threads, with external hex nuts for ER16/ER20 and a standard nut for other sizes (optional strong nuts available); dynamically balanced to G6.3, 10000RPM (higher speeds on request).",
      pageNumber: "C43"
    },
    {
      id: 4,
      name: "BT-ER-M Milling Machine Tool Holder",
      image: "/images/c44-1.png",
      series: "BT-ER-M",
      application: "Utilizes small diameter nuts to prevent interference, features precision ground threads, and is balanced to G6.3, 10000RPM.",
      pageNumber: "C44"
    },
    {
      id: 5,
      name: "BT-ER Collet Chuck Group",
      image: "/images/c45-1.png",
      series: "BT-ER Group",
      application: "A comprehensive set of ER collet chuck tool holders, designed for various clamping ranges.",
      pageNumber: "C45"
    },
    {
      id: 6,
      name: "DAT-ER Milling Machine Tool Holder",
      image: "/images/c46-1.png",
      series: "DAT-ER",
      application: "Equipped with precision ground threads, featuring external hex nuts for ER16/ER20 and a standard nut for other sizes (optional strong nuts available); dynamically balanced to G6.3, 10000RPM.",
      pageNumber: "C46"
    },
    {
      id: 7,
      name: "DAT-ER Milling Machine Tool Holder (M Type)",
      image: "/images/c46-2.png",
      series: "DAT-ER-M",
      application: "An M-type ER tool holder with small diameter nuts to avoid interference, suitable for milling.",
      pageNumber: "C46"
    },
    {
      id: 8,
      name: "HSK-ER Milling Machine Tool Holder",
      image: "/images/c47-1.png",
      series: "HSK-ER",
      application: "The tool holder's rotation undergoes balance testing, capable of achieving G2.5 at 25000RPM; designed to securely clamp various diameter tools.",
      pageNumber: "C47"
    },
    {
      id: 9,
      name: "NT-ER Milling Machine Tool Holder",
      image: "/images/c47-2.png",
      series: "NT-ER",
      application: "The tool holder's rotation is balance-tested, achieving G2.5 at 25000RPM; suitable for clamping a wide range of tool diameters.",
      pageNumber: "C47"
    },
    {
      id: 10,
      name: "MT-ER Milling Machine Tool Holder",
      image: "/images/c48-1.png",
      series: "MT-ER",
      application: "The tool holder's rotation is balance-tested, achieving G2.5 at 25000RPM; capable of clamping various diameter tools.",
      pageNumber: "C48"
    },
    {
      id: 11,
      name: "R8-ER Milling Machine Tool Holder",
      image: "/images/c48-2.png",
      series: "R8-ER",
      application: "The tool holder's rotation is balance-tested, achieving G2.5 at 25000RPM; designed to clamp various diameter tools.",
      pageNumber: "C48"
    },
    {
      id: 12,
      name: "C-ER-M Straight Shank ER Collet Chuck Holder",
      image: "/images/c50-1.png",
      series: "C-ER-M",
      application: "A straight shank ER collet extension rod, where the M-type utilizes small diameter nuts to prevent interference.",
      pageNumber: "C50"
    },
    {
      id: 13,
      name: "C-ER-A Straight Shank ER Collet Chuck Holder",
      image: "/images/c51-1.png",
      series: "C-ER-A",
      application: "A straight shank ER collet extension rod.",
      pageNumber: "C51"
    },
    {
      id: 14,
      name: "ER Extension (For Lathe)",
      image: "/images/c50-2.png",
      series: "ER Extension",
      application: "An ER extension rod specifically designed for lathe applications.",
      pageNumber: "C50"
    }
  ]

  // Performance features based on the provided content
  const performanceFeatures = [
    {
      icon: "Target",
      title: "Unmatched Versatility and Clamping Range",
      description: "Core strength lies in collet-based design. Single ER tool holder can securely clamp wide array of tool shank diameters by swapping appropriately sized ER collet, making it incredibly cost-effective solution.",
    },
    {
      icon: "Zap", 
      title: "Reliable Precision for Diverse Tasks",
      description: "Excellent concentricity crucial for good surface finishes. High-performance models precision-balanced to G2.5 at 25,000-30,000 RPM for High-Speed Machining and engraving applications.",
    },
    {
      icon: "Shield",
      title: "Specialized Designs for Niche Applications",
      description: "Non-eccentric full-circle balanced nuts minimize wind resistance and noise. Slim M-Type nuts designed for tight spaces and deep cavities. Keyless groove design for high rotational speeds.",
    },
  ]

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Clamping System & Compatibility",
      description: "ER collet system featuring double-angled collet compressed by clamping nut into precision-ground taper. Available in full range: ER11, ER16, ER20, ER25, ER32, ER40, ER50. Compatible with virtually every machine spindle type: BT, DAT, HSK, NT, MT, R8, ISO, C-Type.",
    },
    {
      title: "Dynamic Balance & Speed Performance",
      description: "High-Speed Grade: G2.5 at 25,000-30,000 RPM (HSK-ER, BT-GER, ISO-ER). General Purpose Grade: G6.3 at 10,000 RPM (standard BT-ER, DAT-ER, NT-ER). Higher balance grades (G2.5 @ 40,000 RPM) available via special order.",
    },
    {
      title: "Critical Operating Procedures",
      description: "Tool shank must be inserted into collet beyond effective clamping length to ensure proper clamping and prevent damage. Using correct specified wrench crucial to avoid damage to nut and holder. Each collet offers clamping range (typically 1mm for standard collets).",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  ER Tool Holder Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG ER Tool Holder System
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  MZG have synthesized the provided information to craft a comprehensive, expert-level overview of the ER Tool Holder system. This system is arguably the most versatile and widely adopted tool holding solution in the modern machine shop, representing the pinnacle of versatility and convenience. Built around the industry-standard ER collet, this tool holder is a cornerstone of any modern milling, drilling, or engraving operation.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/millingcutter1.png"
                    alt="MZG Professional ER Tool Holder System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Target":
                    return <Target className="h-6 w-6 text-blue-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  case "Shield":
                    return <Shield className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">System Performance Analysis</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of an ER Tool Holder is defined by its exceptional versatility, reliable precision, and its ability to be configured for a vast spectrum of machining applications, from general-purpose work to high-speed finishing. The core strength of the ER system lies in its collet-based design.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      A single ER tool holder can securely clamp a wide array of tool shank diameters simply by swapping out the appropriately sized ER collet. This flexibility allows workshops to hold end mills, drills, reamers, taps, and other shanked tools with minimal inventory of holders, making it an incredibly cost-effective and efficient solution.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      ER tool holders provide excellent concentricity, which is crucial for achieving good surface finishes, ensuring even tool wear, and maintaining dimensional accuracy. High-performance models such as the HSK-ER and BT-GER series are precision-balanced to G2.5 at 25,000 RPM (or even up to 30,000 RPM), making them ideal for High-Speed Machining (HSM) and engraving.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The system has evolved to meet specific manufacturing challenges with specialized designs for niche applications. High-Speed Nuts utilize non-eccentric, full-circle balanced nut design. Slim "M-Type" Nuts feature small diameter for tight spaces. Keyless Groove Design better suited for high rotational speeds.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3 text-sm text-gray-700">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Dynamic Balance: G2.5 (High-Speed), G6.3 (General Purpose)</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Maximum Speed: Up to 30,000 RPM</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Collet Series: ER11, ER16, ER20, ER25, ER32, ER40, ER50</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Interface Types: BT, DAT, HSK, NT, MT, R8, ISO, C-Type</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Special Features: M-Type nuts, Non-eccentric design</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                          <span className="text-gray-900 text-right">{product.series}</span>
                        </div>
                      )}
                      {product.application && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.application}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultERImages[0]}
                    alt="ER Tool Holder Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                  </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultERImages[imageIndex % defaultERImages.length]
                  : defaultERImages[index % defaultERImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`ER Tool Holder Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                  </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Clamping System & Compatibility":
                      return <Target className="h-6 w-6 text-blue-600 mr-3" />
                    case "Dynamic Balance & Speed Performance":
                      return <Zap className="h-6 w-6 text-green-600 mr-3" />
                    case "Critical Operating Procedures":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                    </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                    </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios &amp; Applicable Machining</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>General Purpose Milling and Drilling:</strong> Most common application, go-to choice for holding end mills for roughing and finishing, and for holding drills with high precision</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Speed Finishing and Engraving:</strong> High-balance models (HSK-ER, ISO-ER, BT-GER) excel in mold & die making, artistic engraving, and finishing operations where surface quality is critical</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Machining in Confined Areas:</strong> M-Type holders with slim nuts essential for 5-axis machining and reaching features in deep pockets without holder colliding with workpiece</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Extended Reach Machining:</strong> C-ER straight shank holders widely used as extension rods to machine features that are otherwise inaccessible with standard-length holders</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Lathe and Mill-Turn Applications:</strong> Straight shank and other ER extensions heavily used on lathes and mill-turn centers for holding live tooling</span>
                  </li>
                </ul>
              </div>

              {/* Machining Operations */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Main Functions
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Universal, Flexible Clamping:</strong> Most critical function to securely hold vast range of tool shank diameters using standardized, interchangeable collet system</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Ensure Reliable Machining Precision:</strong> Functions to provide excellent concentricity and stability for wide variety of operations, from heavy drilling to fine engraving</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Offer Maximum Machine Compatibility:</strong> Designed to interface with nearly any milling machine, lathe, or engraving machine spindle through extensive offering of shank types</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Adapt to Specialized Machining Needs:</strong> Through variants like M-Type nuts and high-speed designs, functions as modular platform optimized for specific, challenging applications</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Cost-Effective Solution:</strong> Minimal inventory of holders required due to wide clamping range with each collet offering typically 1mm range for enhanced versatility</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Primary Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Universal, Flexible Clamping:</strong> Most critical function to securely hold vast range of tool shank diameters using standardized, interchangeable collet system</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Ensure Reliable Machining Precision:</strong> Functions to provide excellent concentricity and stability for wide variety of operations</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Offer Maximum Machine Compatibility:</strong> Designed to interface with nearly any milling machine, lathe, or engraving machine spindle in the world</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  Performance Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Adapt to Specialized Machining Needs:</strong> Through variants like M-Type nuts and high-speed designs, functions as modular platform optimized for specific applications</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Cost-Effective Workshop Solution:</strong> Minimal inventory of holders required, making it incredibly cost-effective and efficient solution for modern machine shops</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Industry Standard Solution:</strong> Serves as industry-standard solution for broad spectrum of machining operations, balancing performance, flexibility, and cost</span>
                    </li>
                  </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/milling-tool-holder/er-tool-holder" />
            </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional ER Tool Holder Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal ER tool holders for your versatile machining applications. From general-purpose to high-speed precision work, we provide the most comprehensive and cost-effective clamping solutions.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same milling-tool-holder directory
                const allToolHolderCategories = [
                  {
                    title: "SK High-Speed Tool Holders",
                    image: "/images/C05-1.png",
                    description: "High-speed precision tool holders",
                    url: "/standard-tools/milling-tool-holder/sk-high-speed",
                  },
                  {
                    title: "HM Hydraulic Tool Holders",
                    image: "/images/C09-1.png",
                    description: "Precision hydraulic clamping systems",
                    url: "/standard-tools/milling-tool-holder/hm-hydraulic",
                  },
                  {
                    title: "SR Shrink Fit Tool Holders",
                    image: "/images/C13-1.png",
                    description: "Thermal expansion precision clamping",
                    url: "/standard-tools/milling-tool-holder/sr-shrink-fit",
                  },
                  {
                    title: "ADS Pull-Back Tool Holders",
                    image: "/images/C29-1.png",
                    description: "Ultra-precision pull-back clamping",
                    url: "/standard-tools/milling-tool-holder/ads-pull-back",
                  },
                  {
                    title: "Power Tool Holders",
                    image: "/images/c34-1.png",
                    description: "Heavy-duty strong clamping systems",
                    url: "/standard-tools/milling-tool-holder/power-tool-holder",
                  },
                ];
                
                return allToolHolderCategories.slice(0, 5).map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Tool Holders" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
}