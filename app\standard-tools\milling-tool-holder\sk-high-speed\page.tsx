"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function SKHighSpeedPage() {
  // SK High-Speed相关的默认图片
  const defaultSKImages = [
    "/images/C05-1.png",
    "/images/C05-2.png", 
    "/images/C06-1.png",
    "/images/C07-1.png",
    "/images/C08-1.png",
    "/images/C08-2.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/milling-tool-holder/sk-high-speed");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认SK图片
          setGalleryImages(defaultSKImages);
        }
      } else {
        // API请求失败，使用默认SK图片
        setGalleryImages(defaultSKImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认SK图片
      setGalleryImages(defaultSKImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认SK图片，避免显示无关图片
    setGalleryImages(defaultSKImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data for SK High-Speed Tool Holders
  const products = [
    {
      id: "sk-001",
      name: "ISO-GSK Milling Machine Tool Holder",
      image: "/images/C05-1.png",
      description: "Designed for high-speed engraving and milling machines, featuring a 4° SK collet for higher repeat clamping accuracy and full circle nuts for low noise and minimal wind resistance.",
      series: "ISO-GSK",
      application: "High-speed engraving and milling machines",
      speedRange: "15,000-25,000 RPM",
      pageNumber: "C05",
    },
    {
      id: "sk-002",
      name: "NBT-GSK Milling Machine Tool Holder",
      image: "/images/C05-2.png",
      description: "Optimized for high-speed carving and milling machines, equipped with non-eccentric, full balanced round nuts for reduced noise and wind resistance.",
      series: "NBT-GSK",
      application: "High-speed carving and milling machines",
      speedRange: "15,000-25,000 RPM",
      pageNumber: "C05",
    },
    {
      id: "sk-003",
      name: "BT-GSK Milling Machine Tool Holder",
      image: "/images/C06-1.png",
      description: "Ideal for high-speed, high-precision cutting, enhancing tool life and ensuring stable cutting performance with reduced vibration; requires the tool to extend beyond the collet's effective clamping length to prevent damage.",
      series: "BT-GSK",
      application: "High-speed, high-precision cutting applications",
      speedRange: "15,000-30,000 RPM",
      pageNumber: "C06",
    },
    {
      id: "sk-004",
      name: "BT-SK Milling Machine Tool Holder",
      image: "/images/C07-1.png",
      description: "Provides high repeat clamping accuracy utilizing a 4° SK collet.",
      series: "BT-SK",
      application: "Precision milling and engraving operations",
      speedRange: "15,000-25,000 RPM",
      pageNumber: "C07",
    },
    {
      id: "sk-005",
      name: "C-GSK Milling Machine Tool Holder",
      image: "/images/C08-1.png",
      description: "Suited for high-speed, high-precision cutting, incorporating 4° SK collets and full circle nuts for stable performance.",
      series: "C-GSK",
      application: "High-speed, high-precision cutting operations",
      speedRange: "15,000-25,000 RPM",
      pageNumber: "C08",
    },
    {
      id: "sk-006",
      name: "C-SK Milling Machine Tool Holder",
      image: "/images/C08-2.png",
      description: "Delivers high repeat clamping accuracy through its 4° SK collets, contributing to stable cutting.",
      series: "C-SK",
      application: "Stable cutting operations with high precision",
      speedRange: "15,000-25,000 RPM",
      pageNumber: "C08",
    },
  ]

  // Performance features based on the provided content
  const performanceFeatures = [
    {
      icon: "Shield",
      title: "Exceptional High-Speed Capability",
      description: "Precision-balanced to G2.5 grade, enabling operation at speeds up to 40,000 RPM with minimal vibration and superior stability for high-speed machining applications.",
    },
    {
      icon: "Zap", 
      title: "Superior Clamping Force and Rigidity",
      description: "SK collets with 4° taper provide significantly larger contact area than 8° ER collets, resulting in powerful, uniform clamping force and exceptional rigidity for reduced tool deflection.",
    },
    {
      icon: "Target",
      title: "Outstanding Precision and Concentricity",
      description: "Advanced manufacturing tolerances ensure exceptional concentricity and low runout, critical for achieving superior surface finishes and uniform cutting edge wear distribution.",
    },
  ]

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Collet System & Dynamic Balance",
      description: "Utilizes SK collets with 4° taper for high-precision clamping. Precision balanced to G2.5 standard with operational range typically 15,000-25,000 RPM, with specific models calibrated for up to 40,000 RPM. Available in SK6, SK10, SK16, and SK25 series.",
    },
    {
      title: "Shank Interface & Nut Design",
      description: "Available in BT (JIS B 6339), NBT (keyless), ISO, and C-Type configurations. Features non-eccentric, fully balanced round nut with keyless slot design that significantly reduces noise and minimizes wind resistance at high speeds.",
    },
    {
      title: "Performance Specifications",
      description: "Clamping range from 1.8mm to 25.00mm depending on collet series. Full-circle, non-eccentric nut design for minimal wind resistance. Superior vibration dampening and reduced noise operation for improved working environment.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  SK High-Speed Tool Holder Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG SK High-Speed High-Precision Tool Holder System
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  As a senior industrial tooling expert at MZG Tool Machine Company, I am pleased to provide a comprehensive and detailed introduction to the SK High-Speed High-Precision Tool Holder system. This tool holder family represents a pinnacle of engineering designed for the most demanding machining operations, where speed, accuracy, and surface finish are paramount.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/millingcutter1.png"
                    alt="MZG Professional SK High-Speed Tool Holder System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">System Performance Analysis</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of SK High-Speed High-Precision Tool Holders is defined by their exceptional stability, precision, and efficiency, particularly in high-RPM environments. The hallmark of this system is its suitability for High-Speed Machining (HSM). Models like the NBT-GSK are dynamically balanced to a precision grade of G2.5, allowing them to operate smoothly and safely at speeds up to 40,000 RPM.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The core of the SK system's performance lies in its use of SK collets with 4-degree taper angle. Compared to the more common 8-degree taper of ER collets, this shallower angle provides a significantly larger contact area between the collet and the tool holder body. This results in a powerful, uniform clamping force around the entire shank of the cutting tool, drastically reducing tool deflection.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The combination of the 4-degree taper and high manufacturing tolerances ensures exceptional concentricity and low runout. This means the cutting tool rotates in near-perfect alignment with the spindle's axis, critical for achieving superior surface finishes and ensuring each cutting edge engages the workpiece evenly.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The specialized GSK series features a full-circle, non-eccentric nut design. This symmetrical, aerodynamic nut minimizes wind resistance and the "whistling" sound often associated with high-speed rotation, creating a quieter and less fatiguing working environment while ensuring smoother cutting action.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3 text-sm text-gray-700">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Taper Angle: 4° (SK Type)</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Speed Range: 15,000-40,000 RPM</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Balance Grade: G2.5 precision</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Clamping Range: 1.8-25.00mm</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span>Interface Types: BT, NBT, ISO, C-Type</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {products.map((product) => (
              <div
                key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
              >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                  <Image
                    src={product.image || "/placeholder.svg"}
                    alt={product.name}
                    fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                  />
                </div>
                                    <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                          <span className="text-gray-900 text-right">{product.series}</span>
                        </div>
                      )}
                      {product.speedRange && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Speed:</span>
                          <span className="text-gray-900 text-right">{product.speedRange}</span>
                    </div>
                      )}
                      {product.application && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.application}</p>
                    </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultSKImages[0]}
                    alt="SK High-Speed Tool Holder Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                    </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultSKImages[imageIndex % defaultSKImages.length]
                  : defaultSKImages[index % defaultSKImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`SK High-Speed Tool Holder Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                    </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Collet System & Dynamic Balance":
                      return <Layers className="h-6 w-6 text-blue-600 mr-3" />
                    case "Shank Interface & Nut Design":
                      return <Shield className="h-6 w-6 text-green-600 mr-3" />
                    case "Performance Specifications":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                    </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios & Applicable Machining</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Mold and Die Manufacturing:</strong> Primary application for creating complex mold cavities and cores with excellent surface finishes</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Aerospace Industry:</strong> Machining of aluminum alloys, titanium, and exotic materials for complex structural components</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Speed Engraving:</strong> ISO-GSK and NBT-GSK models for high-speed spindles on engraving machines</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                                         <span><strong>Hard Milling:</strong> Exceptional rigidity allows effective milling of hardened steels (&gt;45 HRC)</span>
                      </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Medical Device Manufacturing:</strong> High precision essential for medical implants and surgical instruments</span>
                      </li>
                    </ul>
                  </div>

              {/* Machining Operations */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Machining Operations
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Precision Finishing:</strong> Low-vibration and high-concentricity for tight dimensional tolerances</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Speed Engraving:</strong> Intricate designs, lettering, and detailed 3D contours</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Complex Multi-Axis Toolpaths:</strong> Stability for demanding CNC programming requirements</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Surface Finishing:</strong> Mirror-like surface quality with superior dimensional accuracy</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-RPM Cutting:</strong> Optimal performance at speeds up to 40,000 RPM</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Primary Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Secure and Concentric Tool Clamping:</strong> Grips cutting tools with immense force and near-perfect alignment to rotation axis</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Enabling High-Speed Machining:</strong> Precision dynamic balancing facilitates high rotational speeds for increased productivity</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Vibration Attenuation:</strong> Absorbs and minimizes vibrations for stable machining and superior part quality</span>
                  </li>
                </ul>
                </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  Performance Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Efficient Torque Transmission:</strong> Reliably transmits rotational power without slippage or rigidity loss</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Reduced Noise Operation:</strong> Full-circle nut design minimizes wind resistance and whistling sounds</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Extended Tool Life:</strong> Superior clamping and vibration control contribute to longer tool performance</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/milling-tool-holder/sk-high-speed" />
          </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional SK High-Speed Tool Holder Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal SK High-Speed tool holders for high-precision machining, high-speed engraving, and demanding CNC applications. From 15,000 RPM to 40,000 RPM operations, we provide comprehensive high-speed machining solutions.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same milling-tool-holder directory
                const allToolHolderCategories = [
                  {
                    title: "ER Tool Holders",
                    image: "/images/C01-1.png",
                    description: "Versatile collet chuck systems",
                    url: "/standard-tools/milling-tool-holder/er-tool-holder",
                  },
                  {
                    title: "Hydraulic Tool Holders",
                    image: "/images/C02-1.png",
                    description: "High-precision hydraulic clamping",
                    url: "/standard-tools/milling-tool-holder/hm-hydraulic",
                  },
                  {
                    title: "Shrink Fit Tool Holders",
                    image: "/images/C03-1.png",
                    description: "Maximum rigidity and precision",
                    url: "/standard-tools/milling-tool-holder/sr-shrink-fit",
                  },
                  {
                    title: "Side Lock Tool Holders",
                    image: "/images/C04-1.png",
                    description: "Side clamping mechanism",
                    url: "/standard-tools/milling-tool-holder/side-lock",
                  },
                  {
                    title: "Face Milling Tool Holders",
                    image: "/images/C09-1.png",
                    description: "Heavy-duty face milling applications",
                    url: "/standard-tools/milling-tool-holder/face-milling",
                  },
                ];
                
                return allToolHolderCategories.slice(0, 5).map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Tool Holders" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
} 