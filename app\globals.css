@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 13%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 13%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 13%;

    --primary: 0 79% 63%;
    --primary-foreground: 0 0% 98%;

    --secondary: 220 100% 50%;
    --secondary-foreground: 0 0% 98%;

    --muted: 220 20% 96%;
    --muted-foreground: 0 0% 45%;

    --accent: 220 40% 96%;
    --accent-foreground: 220 70% 30%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 13% 89%;
    --input: 220 13% 89%;
    --ring: 0 79% 63%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 渐变动画效果 */
@keyframes gradient-x {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes gradient-xy {
  0%, 100% {
    background-position: 0% 0%;
  }
  25% {
    background-position: 100% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  75% {
    background-position: 0% 100%;
  }
}

.animate-gradient-x {
  background-size: 400% 400%;
  animation: gradient-x 8s ease infinite;
}

.animate-gradient-xy {
  background-size: 400% 400%;
  animation: gradient-xy 12s ease infinite;
}

/* ==========================================
   Stagewise 样式隔离和保护
   ========================================== */

/* 主应用容器保护 */
.main-app-container {
  isolation: isolate;
  contain: layout style;
}

/* Stagewise 容器隔离 */
#stagewise-root {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  pointer-events: none !important;
  z-index: 999999 !important;
  isolation: isolate !important;
  contain: layout style paint !important;
  transform: translateZ(0) !important;
  will-change: transform !important;
}

#stagewise-root * {
  box-sizing: border-box !important;
  isolation: isolate !important;
}

/* Stagewise 工具栏内容可交互 */
#stagewise-root > div {
  pointer-events: auto !important;
}

/* Stagewise 控制按钮样式 */
#stagewise-toggle {
  position: fixed !important;
  bottom: 20px !important;
  right: 20px !important;
  z-index: 999998 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  transition: all 0.2s ease !important;
}

#stagewise-toggle:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0,0,0,0.2) !important;
}

/* 防止 Stagewise 样式污染主应用 */
body:not(#stagewise-root) {
  isolation: isolate;
}

/* 确保主要页面元素不受影响 */
main, header, footer, nav, section, article {
  isolation: isolate;
}
