import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { formatDate } from "@/lib/utils"
import { getBlogPost, incrementViewCount } from "@/app/actions/blog-actions-db"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Share2, Calendar, User, Tag, Eye } from "lucide-react"
import type { Metadata } from "next"

interface BlogPostPageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const post = await getBlogPost(params.slug)

  if (!post) {
    return {
      title: "Post Not Found | MZG Tools",
    }
  }

  return {
    title: post.metaTitle || `${post.title} | MZG Tools Blog`,
    description: post.metaDescription || post.excerpt,
    openGraph: {
      title: post.title,
      description: post.excerpt,
      images: post.imageUrl ? [post.imageUrl] : [],
      type: 'article',
      publishedTime: post.publishedAt || post.createdAt,
      authors: [post.author]
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.excerpt,
      images: post.imageUrl ? [post.imageUrl] : []
    }
  }
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const post = await getBlogPost(params.slug)

  if (!post || !post.published) {
    notFound()
  }

  // 增加浏览量（异步执行，不阻塞页面渲染）
  incrementViewCount(params.slug).catch(console.error)

  // 生成结构化数据
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "headline": post.title,
    "description": post.excerpt,
    "image": post.imageUrl,
    "datePublished": post.publishedAt || post.createdAt,
    "dateModified": post.updatedAt,
    "author": {
      "@type": "Person",
      "name": post.author
    },
    "publisher": {
      "@type": "Organization",
      "name": "MZG Tools",
      "logo": {
        "@type": "ImageObject",
        "url": "https://mzg-tools.com/logo.png"
      }
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://mzg-tools.com/mzgblog/${post.slug}`
    }
  }

  return (
    <>
      {/* JSON-LD结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      
      <main className="min-h-screen pb-16">
        {/* Hero Banner */}
        <div className="relative w-full h-[400px] md:h-[500px]">
          <Image
            src={post.imageUrl || "/placeholder.svg?height=800&width=1200&query=industrial tool"}
            alt={post.featuredImageAlt || post.title}
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-black/50 flex flex-col justify-end p-6 md:p-12">
            <div className="max-w-5xl mx-auto w-full">
              {post.category && (
                <div 
                  className="inline-block text-white px-4 py-1 text-sm font-medium uppercase mb-4 rounded"
                  style={{ backgroundColor: post.category.color }}
                >
                  {post.category.name}
                </div>
              )}
              <h1 className="text-3xl md:text-5xl font-bold text-white mb-6">{post.title}</h1>
            </div>
          </div>
        </div>

        {/* Content Container */}
        <div className="max-w-5xl mx-auto px-4 md:px-8">
          {/* Meta Information */}
          <div className="flex flex-wrap items-center gap-4 py-6 border-b border-gray-200 text-gray-600">
            <div className="flex items-center gap-2">
              <Calendar size={16} />
              <span>{formatDate(post.createdAt)}</span>
            </div>
            <div className="flex items-center gap-2">
              <User size={16} />
              <span>{post.author}</span>
            </div>
            {post.category && (
              <div className="flex items-center gap-2">
                <Tag size={16} />
                <span>{post.category.name}</span>
              </div>
            )}
            <div className="flex items-center gap-2">
              <Eye size={16} />
              <span>{post.viewCount || 0} views</span>
            </div>
            <div className="ml-auto">
              <ShareButton post={post} />
            </div>
          </div>

          {/* Article Content */}
          <article className="prose prose-lg max-w-none py-8">
            <div 
              dangerouslySetInnerHTML={{ __html: post.content }} 
              className="prose-headings:text-gray-900 prose-p:text-gray-700 prose-li:text-gray-700"
            />
          </article>

          {/* Tags */}
          {post.tags && post.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-8">
              {post.tags.map((tag) => (
                <Link
                  key={tag.id}
                  href={`/mzgblog?tag=${tag.slug}`}
                  className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-3 py-1 rounded-full text-sm transition-colors"
                >
                  #{tag.name}
                </Link>
              ))}
            </div>
          )}

          {/* Back to Blog */}
          <div className="mt-12 border-t border-gray-200 pt-8">
            <Link href="/mzgblog">
              <Button variant="outline" className="flex items-center gap-2">
                <ArrowLeft size={16} />
                Back to Blog
              </Button>
            </Link>
          </div>
        </div>
      </main>
    </>
  )
}

// 分享按钮组件
function ShareButton({ post }: { post: any }) {
  const shareUrl = `https://mzg-tools.com/mzgblog/${post.slug}`
  const shareText = post.title

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: post.title,
        text: post.excerpt,
        url: shareUrl,
      }).catch(console.error)
    } else {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(shareUrl).then(() => {
        alert('Link copied to clipboard!')
      }).catch(() => {
        // 降级方案：打开分享菜单
        const dropdown = document.getElementById('share-dropdown')
        if (dropdown) {
          dropdown.classList.toggle('hidden')
        }
      })
    }
  }

  const socialShare = (platform: string) => {
    const urls = {
      twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`,
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`
    }
    
    window.open(urls[platform as keyof typeof urls], '_blank', 'width=600,height=400')
  }

  return (
    <div className="relative">
      <Button variant="outline" size="sm" onClick={handleShare} className="flex items-center gap-2">
        <Share2 size={16} />
        Share
      </Button>
      
      {/* 降级分享菜单 */}
      <div id="share-dropdown" className="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border z-10">
        <div className="py-1">
          <button
            onClick={() => socialShare('twitter')}
            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
          >
            Share on Twitter
          </button>
          <button
            onClick={() => socialShare('linkedin')}
            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
          >
            Share on LinkedIn
          </button>
          <button
            onClick={() => socialShare('facebook')}
            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
          >
            Share on Facebook
          </button>
          <button
            onClick={() => navigator.clipboard.writeText(shareUrl)}
            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
          >
            Copy Link
          </button>
        </div>
      </div>
    </div>
  )
}