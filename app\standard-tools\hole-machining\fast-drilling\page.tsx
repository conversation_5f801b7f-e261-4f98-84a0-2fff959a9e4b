"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function FastDrillingPage() {
  // Fast Drilling 相关的默认图片
  const defaultFastDrillingImages = [
    "/images/k05-1.png",
    "/images/k07-1.png", 
    "/images/k09-1.png",
    "/images/k11-1.png",
    "/images/k13-1.png",
    "/images/k16-1.png",
    "/images/k18-1.png",
    "/images/k19-1.png",
    "/images/k20-1.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/hole-machining/fast-drilling");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认Fast Drilling图片
          setGalleryImages(defaultFastDrillingImages);
        }
      } else {
        // API请求失败，使用默认Fast Drilling图片
        setGalleryImages(defaultFastDrillingImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认Fast Drilling图片
      setGalleryImages(defaultFastDrillingImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认Fast Drilling图片，避免显示无关图片
    setGalleryImages(defaultFastDrillingImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data based on the MZG Fast Drilling System
  const products = [
    {
      id: "fast-drill-001",
      name: "WC Type Fast Drilling Bit (U-Drill, Through Hole)",
      image: "/images/k05-1.png",
      description: "High-efficiency indexable drill for through-holes",
      series: "U-Drill WC Series",
      diameter: "Ø14~Ø65mm",
      inserts: "WCMX",
      application: "For through hole drilling; cutting diameter Ø14~Ø65",
      features: "Internal coolant, optimized geometry",
      pageNumber: "K05",
    },
    {
      id: "fast-drill-002",
      name: "SP Type Fast Drilling Bit (U-Drill, Flat Bottom Hole)",
      image: "/images/k07-1.png",
      description: "Specialized drill for flat-bottom holes",
      series: "U-Drill SP Series",
      diameter: "Ø13~Ø60mm",
      inserts: "SPMG",
      application: "For flat bottom hole drilling; cutting diameter Ø13~Ø60",
      features: "Precise flat bottom, internal cooling",
      pageNumber: "K07",
    },
    {
      id: "fast-drill-003",
      name: "SO Type Fast Drilling Bit (U-Drill, Flat Bottom Hole)",
      image: "/images/k09-1.png",
      description: "Low resistance drill for flat-bottom holes",
      series: "U-Drill SO Series",
      diameter: "Ø10~Ø50mm",
      inserts: "SOMT",
      application: "For flat bottom hole drilling; cutting diameter Ø10~Ø50; low resistance",
      features: "Low resistance, deep hole capability",
      pageNumber: "K09",
    },
    {
      id: "fast-drill-004",
      name: "XO Type Fast Drilling Bit (U-Drill, Flat Bottom Hole)",
      image: "/images/k11-1.png",
      description: "Advanced low resistance flat-bottom drill",
      series: "U-Drill XO Series",
      diameter: "Ø13~Ø60mm",
      inserts: "XOMT,SPMT",
      application: "For flat bottom hole drilling; cutting diameter Ø13~Ø60; low resistance",
      features: "Enhanced chip control, internal coolant",
      pageNumber: "K11",
    },
    {
      id: "fast-drill-005",
      name: "WD Type Fast Drilling Bit (U-Drill, Flat Bottom Hole)",
      image: "/images/k13-1.png",
      description: "Heavy rough machining specialist",
      series: "U-Drill WD Series",
      diameter: "Ø13~Ø65mm",
      inserts: "WDXT",
      application: "For flat bottom hole drilling; cutting diameter Ø13~Ø65; for heavy rough machining",
      features: "Optimized for heavy roughing operations",
      pageNumber: "K13",
    },
    {
      id: "fast-drill-006",
      name: "TAF Discard Quick Drill (U-Drill, Flat Bottom Hole)",
      image: "/images/k16-1.png",
      description: "Quick drill for flat-bottom holes",
      series: "TAF Quick Drill Series",
      diameter: "Ø12~Ø56mm",
      inserts: "GCMT,GPMT",
      application: "For flat bottom hole drilling; cutting diameter Ø12~Ø56",
      features: "High-feed application, internal cooling",
      pageNumber: "K16",
    },
    {
      id: "fast-drill-007",
      name: "Spade Drill / Spade Drill Bar",
      image: "/images/k18-1.png",
      description: "Deep hole drilling specialist for fast drilling",
      series: "Spade Drill Series",
      diameter: "Ø9~Ø65mm",
      depths: "Up to 5xD ratio",
      inserts: "CZDP..",
      application: "For fast deep hole drilling (through hole); cutting diameter Ø9~Ø65; self-centering, suitable for deep holes within 5xD ratio; high precision",
      features: "Self-centering, one-shot drilling capability, center water hole for high-pressure coolant",
      specialNotes: "Upgraded replacement for traditional twist drills, suitable for CNC and conventional equipment. HSS and carbide inserts available for different applications.",
      pageNumber: "K18",
    },
    {
      id: "fast-drill-008",
      name: "SP Type Centering Fast Drilling Bit",
      image: "/images/k19-1.png",
      description: "High-precision centering and positioning drill",
      series: "Centering Drill SP Series",
      inserts: "SPMG",
      application: "Precision centering and positioning operations",
      features: "High-precision positioning, chamfering capability",
      pageNumber: "K19",
    },
    {
      id: "fast-drill-009",
      name: "WC Type Centering Fast Drilling Bit",
      image: "/images/k19-2.png",
      description: "High-precision centering and positioning drill",
      series: "Centering Drill WC Series",
      inserts: "WCMX",
      application: "Precision centering and positioning operations",
      features: "High-precision positioning, chamfering capability",
      pageNumber: "K19",
    },
    {
      id: "fast-drill-010",
      name: "VMD Large Drill Bit",
      image: "/images/k20-1.png",
      description: "Large diameter drilling solution with center guide",
      series: "VMD Large Drill Series",
      diameter: "Up to Ø105mm+",
      structure: "Modular with center guide",
      inserts: "WCMX",
      application: "Large diameter hole machining for oil, coal and other machine industries",
      features: "Center guide drill structure for better centering, replaceable clamps, adjustable external clamp (0-5mm), modular structure, center water hole",
      specialNotes: "Machining φ105mm depth 550mm possible. Improved security with chip breaker design. Automatic continuous feed without tool back-off.",
      pageNumber: "K20",
    },
  ]

  // Performance features
  const performanceFeatures = [
    {
      icon: "Zap",
      title: "Extreme Drilling Efficiency",
      description: "Dramatically increase metal removal rates through advanced indexable insert technology with optimized geometries supporting high speeds and feeds compared to traditional solid drills.",
    },
    {
      icon: "Target", 
      title: "Superior Chip Control & Evacuation",
      description: "Advanced chip-breaking geometries with central coolant holes ensure efficient chip evacuation from deep holes, preventing chip packing and eliminating hazardous stringers.",
    },
    {
      icon: "Shield",
      title: "Outstanding Cost-Effectiveness",
      description: "Indexable design eliminates tool regrinding costs - simply replace low-cost inserts in seconds while maintaining consistent performance and minimizing machine downtime.",
    },
  ]

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Indexable Insert Technology",
      description: "Comprehensive range of insert types including WCMX (general-purpose, through-hole), SPMG (flat-bottom), SOMT/XOMT (low-resistance), WDXT (flat-bottom with geometries for finishing to roughing), and GPMT/GCMT (sharp, wear-resistant) with diameter ranges from Ø10mm to Ø65mm.",
    },
    {
      title: "Spade Drill System",
      description: "Specialized for deep-hole applications with L/D ratios exceeding 5xD. Features self-centering point geometry and balanced dual-edge design ensuring exceptional hole straightness. Available in both high-performance carbide and tough HSS inserts with central coolant holes.",
    },
    {
      title: "VMD Large Drill System",
      description: "Modular design for extremely large diameters (Ø45mm to Ø180mm) featuring central guide drill for precision centering, replaceable outer insert clamps, and adjustable diameter capability (0-5mm). Enables cost-effective replacement of wear-prone components only.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Fast Drilling System Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG Fast Drilling System
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  MZG present a detailed introduction to our comprehensive Fast Drilling System. This system represents a paradigm shift from conventional drilling, moving away from slow, solid HSS or brazed carbide drills towards a highly efficient, indexable insert-based methodology. It is engineered to dramatically increase metal removal rates, reduce operational costs, and enhance safety and precision across a vast spectrum of hole-making applications, from shallow holes in general manufacturing to deep, large-diameter bores in heavy industry.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/Hole Machining Systems.png"
                    alt="MZG Professional Fast Drilling System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">System Performance Analysis</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of our Fast Drilling System is defined by its exceptional speed, versatility, and intelligent design, which translates directly into superior productivity and hole quality. The core of the system, our U-Drills (WC, SP, SO, XO, WD, TAF series), are built for speed. By utilizing small, tough carbide inserts with advanced geometries and coatings, these drills can operate at significantly higher speeds and feeds than traditional twist drills.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      A critical performance feature across the entire system is advanced chip control. Inserts are designed with sophisticated chip-breaking geometries that produce small, manageable chips. This, combined with the universal feature of central coolant holes in the drill bodies, ensures efficient chip evacuation even from deep holes. This prevents chip packing, reduces cutting forces, and eliminates the long, hazardous "stringers" associated with twist drills.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      For deep-hole applications where the length-to-diameter (L/D) ratio exceeds 5xD, the Spade Drill is the superior performance choice. Its self-centering point geometry and balanced, dual-edge design ensure exceptional hole straightness and stability over long reaches. For machining extremely large diameters, the VMD system delivers unmatched performance with its modular design and replaceable components.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Diameter Range: Ø9~Ø180mm</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Insert Types: WCMX, SPMG, SOMT, XOMT, WDXT, GCMT/GPMT</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Depth Capability: Up to 5xD ratio</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Cooling System: Internal coolant holes</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Applications: Through-hole & Flat-bottom</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                          <span className="text-gray-900 text-right">{product.series}</span>
                        </div>
                      )}
                      {product.application && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.application}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultFastDrillingImages[0]}
                    alt="Fast Drilling System Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                  </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultFastDrillingImages[imageIndex % defaultFastDrillingImages.length]
                  : defaultFastDrillingImages[index % defaultFastDrillingImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Fast Drilling System Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                  </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Indexable Insert Technology":
                      return <Layers className="h-6 w-6 text-blue-600 mr-3" />
                    case "Spade Drill System":
                      return <Drill className="h-6 w-6 text-green-600 mr-3" />
                    case "VMD Large Drill System":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                    </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios & Applicable Machining</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Production CNC Machining:</strong> Primary application for U-Drills and carbide Spade Drills in automotive, aerospace, and general manufacturing where cycle time is critical</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Heavy Industry:</strong> VMD Large Drill Bit for petroleum, mining, and energy sectors machining large bores in valve bodies and hydraulic cylinders</span>
                  </li>
                                     <li className="flex items-start">
                     <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                     <span><strong>Deep Hole Drilling:</strong> Spade Drill applications for L/D &gt; 5x in mold making and hydraulic manifold production ensuring straightness and reliable chip evacuation</span>
                   </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Conventional Machining:</strong> HSS insert Spade Drills for less rigid machines like radial and vertical drills providing toughness where carbide may chip</span>
                  </li>
                </ul>
              </div>

              {/* Applicable Machining */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Applicable Machining
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Speed Through-Hole Drilling:</strong> WC Type U-Drills for maximum speed and economy in through-hole applications with superior chip evacuation</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Flat-Bottom Hole Drilling:</strong> SP, SO, XO, WD, and TAF types optimized for creating precise flat-bottom blind holes with excellent surface finish</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Large Diameter Boring:</strong> VMD system for extremely large diameters with modular design allowing cost-effective component replacement</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Precision Centering:</strong> Specialized centering drills for high-precision positioning and chamfering operations in critical applications</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Primary Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Drastically Increase Metal Removal Rates:</strong> Complete drilling operations in a fraction of the time required by traditional methods, directly boosting machine throughput</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Reduce Per-Hole Tooling Cost:</strong> Utilize small, inexpensive, replaceable inserts eliminating high cost of purchasing, regrinding, and recoating solid drills</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Enhance Process Security:</strong> Advanced chip-breaker geometries and through-coolant create reliable, safe machining free from dangerous tangled chips</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  Performance Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Improve Hole Quality:</strong> Produce straighter, more accurately sized holes with better surface finishes through self-centering geometries and balanced cutting forces</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Scalable Solutions:</strong> Single cohesive platform solving virtually any hole-making challenge from 10mm holes to 180mm bores in massive forgings</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Maximize Operational Efficiency:</strong> Continuous cutting without retraction, one-shot drilling capability, and modular designs minimize downtime and inventory</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/hole-machining/fast-drilling" />
          </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional Fast Drilling Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal fast drilling systems for specific hole-making applications, from high-speed through-holes to precision flat-bottom drilling. We provide comprehensive solutions for maximum efficiency across steel, stainless steel, aluminum, and superalloys.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same hole-machining directory
                const allHoleMachiningCategories = [
                  {
                    title: "Drill Bit",
                    image: "/images/k05-1.png",
                    description: "Standard drill bits for various hole machining needs",
                    url: "/standard-tools/hole-machining/drill-bit",
                  },
                  {
                    title: "Rough Boring",
                    image: "/images/k18-1.png",
                    description: "Rough boring tools for large diameter hole machining",
                    url: "/standard-tools/hole-machining/rough-boring",
                  },
                  {
                    title: "Fine Boring",
                    image: "/images/k20-1.png",
                    description: "Fine boring tools for high precision hole machining",
                    url: "/standard-tools/hole-machining/fine-boring",
                  },
                  {
                    title: "Boring Machining",
                    image: "/images/k07-1.png",
                    description: "Comprehensive boring machining solutions",
                    url: "/standard-tools/hole-machining/boring-machining",
                  },
                  {
                    title: "Drill Bit Reamer",
                    image: "/images/k11-1.png",
                    description: "Precision reaming tools for hole finishing",
                    url: "/standard-tools/hole-machining/drill-bit-reamer",
                  },
                ];
                
                return allHoleMachiningCategories.slice(0, 5).map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Hole Machining" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
} 