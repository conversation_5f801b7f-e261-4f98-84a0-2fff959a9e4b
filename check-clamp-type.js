const { neon } = require('@neondatabase/serverless');
const sql = neon(process.env.DATABASE_URL);

async function checkPost() {
  try {
    console.log('Checking clamp-type-right-angle post...');
    
    const result = await sql`
      SELECT slug, title, content_images, content_images_count, 
             LEFT(content, 200) as content_preview
      FROM blog_posts 
      WHERE slug = 'clamp-type-right-angle-'
    `;
    
    if (result.length === 0) {
      console.log('❌ Post not found');
      return;
    }
    
    const post = result[0];
    console.log('✅ Post found:');
    console.log('Title:', post.title);
    console.log('Content Images Count:', post.content_images_count);
    console.log('Content Preview:', post.content_preview);
    
    if (post.content_images && post.content_images.length > 0) {
      console.log('\n📸 Content Images Details:');
      try {
        const images = typeof post.content_images === 'string' 
          ? JSON.parse(post.content_images) 
          : post.content_images;
        
        images.forEach((img, index) => {
          console.log(`  ${index + 1}. URL: ${img.url}`);
          console.log(`     Alt: ${img.alt}`);
          console.log(`     Position: ${img.position || 'not set'}`);
        });
      } catch (error) {
        console.log('❌ Error parsing content images:', error.message);
        console.log('Raw content_images:', post.content_images);
      }
    } else {
      console.log('❌ No content images found');
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

checkPost();
