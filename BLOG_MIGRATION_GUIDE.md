# 博客系统数据库迁移指南

本指南将帮助您将现有的基于文件的博客系统迁移到数据库版本。

## 📋 功能特性

### 数据库版本的新特性
- ✅ **数据库存储**: 使用PostgreSQL数据库替代JSON文件存储
- ✅ **完整的CRUD操作**: 创建、读取、更新、删除博客文章
- ✅ **分类管理**: 可视化分类管理界面
- ✅ **标签系统**: 自动标签创建和关联
- ✅ **SEO优化**: 元标题、描述、结构化数据
- ✅ **浏览量统计**: 自动追踪文章浏览次数
- ✅ **发布控制**: 草稿/发布状态管理
- ✅ **图片管理**: 特色图片和Alt文本支持

## 🚀 迁移步骤

### 1. 数据库初始化

首先，初始化博客数据库表：

```bash
# 访问初始化API
curl -X POST http://localhost:3000/api/admin-mzg/init-blog-db

# 或者使用浏览器访问检查状态
http://localhost:3000/api/admin-mzg/init-blog-db
```

### 2. 文件结构

新的博客系统包含以下文件：

```
app/
├── actions/
│   └── blog-actions-db.ts          # 数据库版本的博客操作
├── admin-mzg/
│   └── blog/
│       ├── page.tsx                # 博客管理主页
│       ├── new/
│       │   └── page.tsx            # 新建文章页面
│       ├── edit/
│       │   └── [id]/page.tsx       # 编辑文章页面
│       └── categories/
│           └── page.tsx            # 分类管理页面
├── api/
│   └── admin-mzg/
│       └── init-blog-db/
│           └── route.ts            # 数据库初始化API
├── mzgblog/
│   ├── page-db.tsx                 # 数据库版本的博客列表页
│   └── [slug]/
│       └── page-db.tsx             # 数据库版本的博客详情页
└── scripts/
    └── migrate-blog-to-db.sql      # 数据库迁移脚本
```

### 3. 替换现有文件

要启用数据库版本，需要替换以下文件：

```bash
# 备份原文件
mv app/mzgblog/page.tsx app/mzgblog/page-file.tsx.bak
mv app/mzgblog/[slug]/page.tsx app/mzgblog/[slug]/page-file.tsx.bak
mv app/actions/blog-actions.ts app/actions/blog-actions-file.ts.bak

# 使用数据库版本
mv app/mzgblog/page-db.tsx app/mzgblog/page.tsx
mv app/mzgblog/[slug]/page-db.tsx app/mzgblog/[slug]/page.tsx
mv app/actions/blog-actions-db.ts app/actions/blog-actions.ts
```

## 📊 数据库表结构

### blog_categories (博客分类)
```sql
- id (SERIAL PRIMARY KEY)
- name (VARCHAR(100)) - 分类名称
- slug (VARCHAR(100)) - URL友好的标识符
- description (TEXT) - 分类描述
- color (VARCHAR(7)) - 分类颜色代码
- created_at, updated_at (TIMESTAMP)
```

### blog_posts (博客文章)
```sql
- id (SERIAL PRIMARY KEY)
- title (VARCHAR(255)) - 文章标题
- slug (VARCHAR(255)) - URL友好的标识符
- excerpt (TEXT) - 文章摘要
- content (TEXT) - 文章内容
- category_id (INTEGER) - 分类ID
- author (VARCHAR(100)) - 作者
- image_url (VARCHAR(500)) - 特色图片URL
- featured_image_alt (VARCHAR(255)) - 图片Alt文本
- published (BOOLEAN) - 发布状态
- published_at (TIMESTAMP) - 发布时间
- view_count (INTEGER) - 浏览次数
- meta_title (VARCHAR(255)) - SEO标题
- meta_description (TEXT) - SEO描述
- created_at, updated_at (TIMESTAMP)
```

### blog_tags (博客标签)
```sql
- id (SERIAL PRIMARY KEY)
- name (VARCHAR(50)) - 标签名称
- slug (VARCHAR(50)) - URL友好的标识符
- created_at (TIMESTAMP)
```

### blog_post_tags (文章标签关联)
```sql
- post_id (INTEGER) - 文章ID
- tag_id (INTEGER) - 标签ID
- PRIMARY KEY (post_id, tag_id)
```

## 🎯 管理界面使用指南

### 访问管理后台
```
http://localhost:3000/admin-mzg/blog
```

### 主要功能

#### 1. 文章管理
- **查看所有文章**: 列表显示所有文章，包括状态、浏览量等信息
- **搜索文章**: 支持按标题、作者、分类搜索
- **新建文章**: 完整的文章编辑器，支持SEO设置
- **编辑文章**: 修改现有文章，查看统计信息
- **删除文章**: 安全删除确认

#### 2. 分类管理
- **创建分类**: 设置名称、描述、颜色
- **编辑分类**: 修改分类信息
- **删除分类**: 检查是否有文章使用该分类

#### 3. 文章编辑器功能
- **基本信息**: 标题、摘要、内容
- **分类和标签**: 选择分类，添加标签（逗号分隔）
- **SEO设置**: 元标题、元描述
- **特色图片**: 图片URL和Alt文本
- **发布控制**: 草稿/发布状态切换

## 🔧 API接口

### 博客文章操作
```typescript
// 获取所有文章
getAllBlogPosts(): Promise<BlogPost[]>

// 获取单篇文章
getBlogPost(slug: string): Promise<BlogPost | null>

// 保存文章
saveBlogPost(formData: FormData): Promise<{success: boolean, message: string, post?: BlogPost}>

// 删除文章
deleteBlogPost(id: number): Promise<{success: boolean, message: string}>

// 增加浏览量
incrementViewCount(slug: string): Promise<void>
```

### 分类操作
```typescript
// 获取所有分类
getAllCategories(): Promise<BlogCategory[]>

// 保存分类
saveCategory(formData: FormData): Promise<{success: boolean, message: string}>

// 删除分类
deleteCategory(id: number): Promise<{success: boolean, message: string}>
```

### 标签操作
```typescript
// 获取所有标签
getAllTags(): Promise<BlogTag[]>

// 删除标签
deleteTag(id: number): Promise<{success: boolean, message: string}>
```

## 🎨 前端特性

### SEO优化
- **结构化数据**: 自动生成JSON-LD格式的结构化数据
- **Open Graph**: 社交媒体分享优化
- **Twitter Cards**: Twitter分享卡片
- **元标签**: 自动生成或自定义元标题和描述

### 用户体验
- **响应式设计**: 移动端友好
- **加载状态**: 骨架屏和加载指示器
- **搜索功能**: 实时搜索文章
- **分页支持**: 大量文章时的分页显示
- **社交分享**: 一键分享到社交媒体

### 性能优化
- **图片优化**: Next.js Image组件优化
- **增量静态生成**: ISR支持
- **缓存策略**: 数据库查询缓存
- **懒加载**: 图片和内容懒加载

## 🚨 注意事项

1. **数据备份**: 在迁移前请备份现有的JSON文件数据
2. **环境变量**: 确保`DATABASE_URL`正确配置
3. **权限检查**: 确保数据库用户有创建表的权限
4. **依赖安装**: 确保所有必需的包已安装
5. **测试环境**: 建议先在测试环境中验证迁移

## 📝 数据迁移脚本示例

如果需要从JSON文件迁移到数据库，可以创建以下迁移脚本：

```typescript
// scripts/migrate-json-to-db.ts
import { getAllBlogPosts as getFileData } from './blog-actions-file'
import { saveBlogPost, saveCategory } from './blog-actions-db'

async function migrateData() {
  // 1. 迁移分类
  // 2. 迁移文章
  // 3. 处理标签
}
```

## 🎉 完成

完成迁移后，您将拥有一个功能完整的数据库驱动的博客系统，具备现代化的管理界面和优化的用户体验。