"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function SideLockPage() {
  // Side Lock相关的默认图片
  const defaultSideLockImages = [
    "/images/c78-1.png",
    "/images/c79-1.png",
    "/images/c80-1.png",
    "/images/c81-1.png",
    "/images/c82-1.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/milling-tool-holder/side-lock");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认Side Lock图片
          setGalleryImages(defaultSideLockImages);
        }
      } else {
        // API请求失败，使用默认Side Lock图片
        setGalleryImages(defaultSideLockImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认Side Lock图片
      setGalleryImages(defaultSideLockImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认Side Lock图片，避免显示无关图片
    setGalleryImages(defaultSideLockImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data based on the Side Lock Tool Holder system
  const products = [
    {
      id: "side-lock-001",
      name: "BT-SLN Side Lock End Mill Holder",
      image: "/images/c78-1.png",
      description: "Features an H5 inner hole tolerance, side locking screws, and central cooling. Dynamically balanced to G6.3, 8000RPM (higher speeds on request).",
      series: "BT-SLN Series",
      tolerance: "Inner Bore: H5",
      balance: "G6.3 @ 8000 RPM",
      cooling: "Central Cooling",
      locking: "Side Locking Screws",
      application: "Features an H5 inner hole tolerance, side locking screws, and central cooling",
      pageNumber: "C78",
    },
    {
      id: "side-lock-002",
      name: "NT-SLN Milling Machine Tool Holder",
      image: "/images/c79-1.png",
      description: "Features an H5 inner hole tolerance, side locking screws, and central cooling. Dynamically balanced to G6.3, 8000RPM (higher speeds on request).",
      series: "NT-SLN Series",
      tolerance: "Inner Bore: H5",
      balance: "G6.3 @ 8000 RPM",
      cooling: "Central Cooling",
      locking: "Side Locking Screws",
      application: "Features an H5 inner hole tolerance, side locking screws, and central cooling",
      pageNumber: "C79",
    },
    {
      id: "side-lock-003",
      name: "DAT-SLN Milling Machine Tool Holder",
      image: "/images/c80-1.png",
      description: "Features an H5 inner hole tolerance. The shank diameter's dynamic balance complies with DIN-1835-B (h6~h7 tolerance); includes side locking screws. Dynamically balanced to G6.3, 8000RPM (higher speeds on request).",
      series: "DAT-SLN Series",
      tolerance: "Inner Bore: H5, Shank: h6~h7",
      balance: "G6.3 @ 8000 RPM",
      standard: "DIN-1835-B",
      locking: "Side Locking Screws",
      application: "Features an H5 inner hole tolerance with DIN-1835-B compliant dynamic balance",
      pageNumber: "C80",
    },
    {
      id: "side-lock-004",
      name: "HSK-SLN Milling Machine Tool Holder",
      image: "/images/c81-1.png",
      description: "Features an H5 inner hole tolerance. The shank diameter's dynamic balance complies with DIN-1835-B (h6~h7 tolerance); includes side locking screws. Dynamically balanced to G6.3, 8000RPM (higher speeds on request).",
      series: "HSK-SLN Series",
      tolerance: "Inner Bore: H5, Shank: h6~h7",
      balance: "G6.3 @ 8000 RPM",
      standard: "DIN-1835-B",
      locking: "Side Locking Screws",
      application: "Features an H5 inner hole tolerance with DIN-1835-B compliant dynamic balance",
      pageNumber: "C81",
    },
    {
      id: "side-lock-005",
      name: "Small Diameter Side Fixed Extended Connecting Rod",
      image: "/images/c82-1.png",
      description: "Extension rods featuring side locking, available for various diameters.",
      series: "Extension Series",
      locking: "Side Locking",
      feature: "Various Diameters Available",
      application: "Extension rods featuring side locking, available for various diameters",
      pageNumber: "C82",
    },
  ]

  // Performance features based on the provided content
  const performanceFeatures = [
    {
      icon: "Shield",
      title: "Uncompromising Torque Transmission",
      description: "Core performance feature is direct mechanical lock. By tightening side screws directly onto Weldon flat of end mill's shank, holder creates positive, physical engagement eliminating any possibility of tool slipping rotationally.",
    },
    {
      icon: "Target",
      title: "Absolute Pull-Out Prevention",
      description: "Mechanical lock on Weldon flat makes tool pull-out physical impossibility under normal cutting forces. This unparalleled security makes Side Lock holder safest and most reliable choice for heavy-duty operations.",
    },
    {
      icon: "Zap",
      title: "Simplicity and Reliability",
      description: "With no hydraulic chambers, collets, or heating elements, Side Lock holder is epitome of mechanical reliability. Impervious to temperature fluctuations or hydraulic pressure, exceptionally durable and easy to use.",
    },
  ]

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Clamping Mechanism & Precision Tolerances",
      description: "Utilizes one or two side locking screws that press directly onto Weldon flat of cutting tool shank. Inner Hole Tolerance: H5, ensuring close and precise fit on tool shank. Shank Diameter Tolerance (on holder): h6, for high-precision fit into machine spindle.",
    },
    {
      title: "Dynamic Balance & Coolant Delivery",
      description: "Standard G6.3 at 8,000 RPM. Higher balance grades and speeds can be customized upon request to meet specific high-speed machining requirements. Select models, particularly in BT series, support central cooling (DIN69871-AD), allowing for high-pressure through-tool coolant delivery.",
    },
    {
      title: "Shank Interface Types & Critical Ordering Information",
      description: "Extensive range ensures compatibility with virtually any milling machine: BT (BT-SLN) for general-purpose and high-performance CNC machines, NT (NT-SLN) for traditional heavy-duty manual or CNC mills, DAT (DAT-SLN) for high-precision dual-contact spindles, HSK (HSK-SLN) for modern high-speed machining centers, Straight Shank (C-SLN) for use as extensions or in custom setups. For NT-style holders, essential to confirm required pull rod (drawbar) thread specification (metric or imperial) to ensure compatibility with machine.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Side Lock Tool Holder Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG Side Lock Tool Holder System
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  MZG present a comprehensive and detailed introduction to the Side Lock Tool Holder system. Often regarded as the benchmark for secure, positive-drive tool holding, the Side Lock holder is the workhorse of the machine shop. Its design is elegantly simple yet incredibly effective, engineered to provide an unshakeable mechanical lock that excels in applications involving high torque and heavy cutting forces. This system is the go-to choice when absolute prevention of tool rotation and pull-out is non-negotiable.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/millingcutter1.png"
                    alt="MZG Professional Side Lock Tool Holder System"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">System Performance Analysis</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of a Side Lock Tool Holder is defined by its raw clamping power, reliability, and its ability to transmit maximum torque from the spindle to the cutting tool. The core performance feature is direct mechanical lock.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      By tightening one or two side screws directly onto Weldon flat of an end mill's shank, the holder creates positive, physical engagement. This eliminates any possibility of tool slipping rotationally within holder, allowing for highest levels of torque transmission.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The mechanical lock on Weldon flat makes tool pull-out physical impossibility under normal cutting forces. This unparalleled security makes Side Lock holder safest and most reliable choice for heavy-duty, high-stock-removal operations.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      While celebrated for its strength, our modern Side Lock holders are engineered for performance. They are dynamically balanced to standard of G6.3 at 8,000 RPM, ensuring smooth operation and protecting machine spindle during high-speed use.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Clamping: Direct mechanical lock on Weldon flat</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Tolerances: Inner Hole H5, Shank h6</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Dynamic Balance: G6.3 @ 8,000 RPM</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Interfaces: BT, NT, DAT, HSK, Straight Shank</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span className="text-sm text-gray-700">Application: Heavy roughing, high torque operations</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                          <span className="text-gray-900 text-right">{product.series}</span>
                        </div>
                      )}
                      {product.locking && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Locking:</span>
                          <span className="text-gray-900 text-right">{product.locking}</span>
                        </div>
                      )}
                      {product.application && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.application}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultSideLockImages[0]}
                    alt="Side Lock Tool Holder Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                  </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultSideLockImages[imageIndex % defaultSideLockImages.length]
                  : defaultSideLockImages[index % defaultSideLockImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Side Lock Tool Holder Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                  </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Clamping Mechanism & Precision Tolerances":
                      return <Target className="h-6 w-6 text-blue-600 mr-3" />
                    case "Dynamic Balance & Coolant Delivery":
                      return <Zap className="h-6 w-6 text-green-600 mr-3" />
                    case "Shank Interface Types & Critical Ordering Information":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                    </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios &amp; Applicable Machining</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Heavy Roughing and High Metal Removal:</strong> This is the primary application. Any operation that involves "hogging" out large amounts of material, such as roughing molds, dies, and large structural components, benefits from the Side Lock's secure grip</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Slotting and Shoulder Milling:</strong> The holder's resistance to rotational and axial forces makes it ideal for deep slotting and aggressive shoulder milling where cutting forces are high and variable</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Conventional and Heavy-Duty Machining:</strong> The NT-SLN series is perfect for large, conventional milling machines where torque and power are more critical than ultra-high RPM</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Drilling with Flatted Shanks:</strong> While primarily for milling, it is an excellent choice for holding large-diameter drills that feature a Weldon flat, providing a much more secure grip than a standard drill chuck</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Cost-Effective General-Purpose Milling:</strong> For job shops and general manufacturing, the Side Lock holder represents a durable, long-lasting, and cost-effective solution for a wide range of everyday milling tasks</span>
                  </li>
                </ul>
              </div>

              {/* Machining Operations */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Main Functions
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Positive Mechanical Lock:</strong> Its primary function is to physically prevent the cutting tool from rotating within the holder, enabling maximum torque transmission</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Eliminate Tool Pull-Out:</strong> It functions to create an axial lock that makes it impossible for the tool to be pulled out of the holder by cutting forces, ensuring process security</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Core Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Positive Mechanical Lock:</strong> Its primary function is to physically prevent the cutting tool from rotating within the holder, enabling maximum torque transmission</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Eliminate Tool Pull-Out:</strong> It functions to create an axial lock that makes it impossible for the tool to be pulled out of the holder by cutting forces, ensuring process security</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  System Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Deliver Maximum Rigidity and Stability:</strong> The robust, thick-walled design serves to dampen vibration and provide a highly rigid connection between the tool and the machine spindle</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Offer Simple, Reliable, and Universal Solution:</strong> Through its straightforward design and availability across all major shank interfaces (BT, NT, HSK, DAT), its function is to provide a powerful and dependable tool holding solution for any milling application, on any machine</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/milling-tool-holder/side-lock" />
          </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional Side Lock Tool Holder Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal side lock tool holders for your heavy-duty milling operations. From uncompromising torque transmission to absolute pull-out prevention, we provide the workhorse solution for demanding machining applications.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same milling-tool-holder directory
                const allToolHolderCategories = [
                  {
                    title: "Face Milling Tool Holders",
                    image: "/images/c65-1.png",
                    description: "Critical foundation for face milling operations",
                    url: "/standard-tools/milling-tool-holder/face-milling",
                  },
                  {
                    title: "SK High-Speed Tool Holders",
                    image: "/images/C05-1.png",
                    description: "High-speed precision tool holders",
                    url: "/standard-tools/milling-tool-holder/sk-high-speed",
                  },
                  {
                    title: "HM Hydraulic Tool Holders",
                    image: "/images/C09-1.png",
                    description: "Precision hydraulic clamping systems",
                    url: "/standard-tools/milling-tool-holder/hm-hydraulic",
                  },
                  {
                    title: "ER Tool Holders",
                    image: "/images/c41-1.png",
                    description: "Versatile collet chuck systems",
                    url: "/standard-tools/milling-tool-holder/er-tool-holder",
                  },
                  {
                    title: "Morse Taper Tool Holders",
                    image: "/images/c71-1.png",
                    description: "Foundational precision tool holders",
                    url: "/standard-tools/milling-tool-holder/morse-taper",
                  },
                ];
                
                return allToolHolderCategories.slice(0, 5).map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Tool Holders" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
} 