"use client"

import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ProductCard from "@/components/product-card"
import FAQSectionEn from "@/components/faq-section-en"
import { useState, useEffect } from "react"

export default function ScrewedModularToolHoldersPage() {
  // Screwed Modular Tool Holders相关的默认图片
  const defaultScrewedModularImages = [
    "/images/D12-1.png",
    "/images/D14-2.png",
    "/images/D16-2.png",
    "/images/D17-2.png",
    "/images/D25-1.png",
    "/images/D26-1.png",
    "/images/D30-2.png",
    "/images/D35-2.png",
    "/images/D41-2.png",
    "/images/D05-2.png",
    "/images/D53-1.png",
    "/images/D54-1.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);
  
  // State to track hydration completion
  const [isHydrated, setIsHydrated] = useState(false);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/clamp-type-milling/screwed-modular-tool-holders");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认图片
          setGalleryImages(defaultScrewedModularImages);
        }
      } else {
        // API请求失败，使用默认图片
        setGalleryImages(defaultScrewedModularImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认图片
      setGalleryImages(defaultScrewedModularImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Hydration effect
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认图片，避免显示无关图片
    setGalleryImages(defaultScrewedModularImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data based on the Screwed / Modular Tool Holders system (保持原有产品数据)
  const products = [
    {
      id: "smth-001",
      name: "M6NTE90 Right angle locking toolholder",
      image: "/images/D12-1.png",
      description: "Double-sided hexagonal insert, modular design",
      series: "M6NTE90 Series",
      insertType: "M6NGU0604.., M6NGU0905..",
      application: "Double-sided hexagonal insert, modular design",
      pageNumber: "D12",
    },
    {
      id: "smth-002",
      name: "3PTE90 Right angle locking toolholder",
      image: "/images/D14-2.png",
      description: "Modular high-efficiency powerful milling",
      series: "3PTE90 Series",
      insertType: "3PKT1004.., 3PKT1505.., 3PKT1906..",
      application: "Modular high-efficiency powerful milling",
      pageNumber: "D14",
    },
    {
      id: "smth-003",
      name: "AHUM Shoulder/Cutting End Mills",
      image: "/images/D16-2.png",
      description: "Modular right angle shoulder milling",
      series: "AHUM Series",
      insertType: "JDMT1003.., JDMT1505..",
      application: "Modular right angle shoulder milling",
      pageNumber: "D16",
    },
    {
      id: "smth-004",
      name: "ASMM Super Excellent Mini ASM-Modular type",
      image: "/images/D17-2.png",
      description: "Modular small diameter precision milling",
      series: "ASMM Series",
      insertType: "JDMT0702.., EDMT0702..",
      application: "Modular small diameter precision milling",
      pageNumber: "D17",
    },
    {
      id: "smth-005",
      name: "R390 Shoulder/Cutting End Mills",
      image: "/images/D25-1.png",
      description: "Modular high-precision end mill head",
      series: "R390 Series",
      insertType: "R390-11T3..",
      application: "Modular high-precision end mill head",
      pageNumber: "D25",
    },
    {
      id: "smth-006",
      name: "RT Shoulder/Cutting End Mills",
      image: "/images/D26-1.png",
      description: "Modular high-precision end mill head",
      series: "RT Series",
      insertType: "RT0702.., RT1003..",
      application: "Modular high-precision end mill head",
      pageNumber: "D26",
    },
    {
      id: "smth-007",
      name: "TE90 Lock type milling head",
      image: "/images/D30-2.png",
      description: "Modular precision milling head",
      series: "TE90 Series",
      insertType: "AXMT (0602, 0903)",
      application: "Modular precision milling head",
      pageNumber: "D30",
    },
    {
      id: "smth-008",
      name: "EMRM Corner Rounding End Mills",
      image: "/images/D35-2.png",
      description: "Round insert with high strength, modular design",
      series: "EMRM Series",
      insertType: "RPMW0802.., RPMW1003.., RPMT1204..",
      application: "Round insert with high strength, modular design",
      pageNumber: "D35",
    },
    {
      id: "smth-009",
      name: "TRSM Corner Rounding End Mills",
      image: "/images/D41-2.png",
      description: "Modular ball nose end mill",
      series: "TRSM Series",
      insertType: "RDMT.., RDKW..",
      application: "Modular ball nose end mill",
      pageNumber: "D41",
    },
    {
      id: "smth-010",
      name: "4NKT Modular Cutter head",
      image: "/images/D05-2.png",
      description: "Double-sided four-corner milling",
      series: "4NKT Series",
      insertType: "4NK(H)T 0603..",
      application: "Double-sided four-corner milling",
      pageNumber: "D05",
    },
    {
      id: "smth-011",
      name: "TJU Drilling & Milling Cutter",
      image: "/images/D53-1.png",
      description: "Drilling and milling dual-purpose, wide application range",
      series: "TJU Series",
      insertType: "CCMT, CPMT",
      application: "Drilling and milling dual-purpose, wide application range",
      pageNumber: "D53",
    },
    {
      id: "smth-012",
      name: "ASJ Drilling and milling cutter",
      image: "/images/D54-1.png",
      description: "From drilling to end milling processing in bottom-free state",
      series: "ASJ Series",
      insertType: "ADMT.., APMT..",
      application: "From drilling to end milling processing in bottom-free state",
      pageNumber: "D54",
    },
  ]

  // Performance features
  const performanceFeatures = [
    {
      icon: "Shield",
      title: "High-Precision Shoulder Milling & Superior Surface Finishes",
      description: "R390, ASMM, RT, and TE90 heads deliver exceptional precision with tapered design for high rigidity. ASMM (Super Excellent Mini) achieves rotation and feed rates up to 3 times higher than general cutters while maintaining blade edge runout accuracy within incredible 5μm, critical for high-tolerance mold, die, and aerospace components.",
    },
    {
      icon: "Zap", 
      title: "Versatile Roughing & Profiling with Economic Efficiency",
      description: "AHUM, TRSM, and EMRM series utilize strong positive angle blades (15°) and economical round inserts (RPMW, RDMT) for smooth cutting action with excellent chip evacuation. Tapered body design ensures high rigidity while multiple cutting edges provide significant cost-per-edge advantage for roughing, pocket milling, and complex copy milling.",
    },
    {
      icon: "Target",
      title: "Multi-Functional Drilling & Milling Performance Integration",
      description: "TJU and ASJ cutter heads break conventional boundaries with hybrid design capability. TJU offers versatile drilling and milling with small cutting resistance, while ASJ specializes in deep roughing with seamless transition from drilling to milling in 'bottom-free' state, drastically reducing cycle times by eliminating tool changes.",
    },
  ]

  // Helper function to render icons
  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case "Shield":
        return <Shield className="h-8 w-8 text-red-600" />
      case "Zap":
        return <Zap className="h-8 w-8 text-red-600" />
      case "Target":
        return <Target className="h-8 w-8 text-red-600" />
      default:
        return <Tool className="h-8 w-8 text-red-600" />
    }
  }

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Modular Screwed Connection & Cutter Head Geometries",
      description: "High-precision, fine-pitch thread provides secure, rigid, and highly concentric connection ensuring maximum torque transmission and minimal runout. System supports Right Angle Shoulder Milling Heads (M6NTE90, 3PTE90, R390, TE90), Corner Rounding Bull-Nose Heads (EMRM, TRSM), Drilling & Milling Heads (TJU, ASJ), and Face Milling Heads (4NKT) for comprehensive machining versatility.",
    },
    {
      title: "Insert Systems & Secure Clamping Technology",
      description: "Comprehensive range of industry-standard inserts including M6NGU, 3PKT, JDMT, R390, RT-series, AXMT, RPMW, RDMT, 4NKT, and CCMT/CPMT for optimization based on material, operation, and cost. Each cutter head employs robust screw-down clamping system (M2.5x6, M4x10 with T8/T15 wrenches) preventing movement under heavy cutting loads.",
    },
    {
      title: "Tool Holder Design & Internal Cooling Systems",
      description: "Reusable shanks manufactured from high-quality alloy steel for durability and rigidity, available in various lengths and shank diameters. Many tool holders feature internal cooling holes for high-pressure coolant delivery directly to cutting zone, with M6NTE90 and 3PTE90 exemplifying advanced cooling integration for enhanced performance and tool life.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Professional Modular Tool System
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  MZG Screwed / Modular Tool Holder Systems
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  This cutting-edge tooling philosophy represents a paradigm shift in manufacturing efficiency, moving away from monolithic, single-purpose tools towards a highly adaptable, interchangeable platform. The system's architecture is elegantly simple yet profoundly effective: a high-precision threaded connection joins a reusable, robust tool holder shank with a wide variety of specialized, replaceable cutter heads. This modularity is the key to unlocking significant reductions in production costs, minimizing tool change downtime, and delivering exceptional performance across a vast spectrum of milling operations, from heavy roughing to high-precision finishing.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/D-SM-ALL.JPG"
                    alt="MZG Professional Screwed Modular Tool Holder Systems"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
              <div
                key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
              </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Performance</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of our Screwed/Modular system is multifaceted, delivering specialized excellence through its diverse cutter head offerings while providing overarching system-level benefits.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>High-Precision Shoulder Milling Performance:</strong> For applications demanding flawless 90° shoulders and superior surface finishes, our system excels. The performance of heads like the <strong>R390, ASMM, RT, and TE90</strong> is defined by their precision. The <strong>R390</strong> series, with its tapered design for high rigidity and compatibility with precision-ground inserts, is a master of finishing. The <strong>ASMM (Super Excellent Mini)</strong> takes this to another level, delivering powerful cutting with rotation and feed rates up to 3 times higher than general cutters, all while maintaining blade edge runout accuracy within an incredible 5μm.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Versatile and Economical Roughing & Profiling Performance:</strong> The system demonstrates exceptional strength in general-purpose and profile milling. The performance of the <strong>AHUM, TRSM, and EMRM</strong> series is rooted in their robust design and insert economy. Utilizing strong positive angle blades (e.g., 15° for EMRM/TRSM) and round inserts (<strong>RPMW, RDMT</strong>), these heads deliver a smooth cutting action with excellent chip evacuation, making them ideal for roughing, pocket milling, and complex copy milling.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Multi-Functional Drilling & Milling Performance:</strong> Our modular system breaks conventional boundaries by integrating multiple operations into a single tool. The performance of the <strong>TJU and ASJ</strong> cutter heads lies in their unique hybrid design. The <strong>TJU</strong> is a versatile workhorse, capable of both drilling and milling with small cutting resistance. The <strong>ASJ</strong> is a specialist for deep roughing, engineered to transition seamlessly from drilling to milling in a "bottom-free" state, drastically reducing cycle times by eliminating tool changes.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3 text-sm text-gray-700">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Runout Accuracy:</strong> Blade edge runout within 5μm for ASMM series</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Feed Rate:</strong> Up to 3x higher than general cutters</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Insert Economy:</strong> Multiple cutting edges per round insert</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Cutting Angle:</strong> 15° positive angle for smooth cutting</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Modularity:</strong> Wide variety of specialized cutter heads</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {!isHydrated ? (
                // Server-side rendering placeholder
                <div className="col-span-full flex justify-center items-center h-64">
                  <div className="animate-pulse text-gray-400">Loading products...</div>
                </div>
              ) : (
                products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                    <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                        <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                      <div className="space-y-2 text-xs">
                      {product.series && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Series:</span>
                            <span className="text-gray-900 text-right">{product.series}</span>
                        </div>
                      )}
                        {(product as any).threadSpec && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Thread:</span>
                            <span className="text-gray-900 text-right">{(product as any).threadSpec}</span>
                        </div>
                      )}
                        {(product as any).shankDiameter && (
                        <div className="flex justify-between">
                            <span className="font-medium text-gray-700">Shank:</span>
                            <span className="text-gray-900 text-right">{(product as any).shankDiameter}</span>
                        </div>
                      )}
                        {(product as any).insertType && (
                        <div className="flex justify-between">
                            <span className="font-medium text-gray-700">Insert:</span>
                            <span className="text-gray-900 text-right">{(product as any).insertType}</span>
                        </div>
                      )}
                        {(product as any).cutterType && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Type:</span>
                            <span className="text-gray-900 text-right">{(product as any).cutterType}</span>
                        </div>
                      )}
                        {(product as any).features && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Features:</span>
                            <span className="text-gray-900 text-right">{(product as any).features}</span>
                        </div>
                      )}
                        {product.description && (
                        <div className="pt-2 border-t border-gray-100">
                            <p className="text-xs text-gray-600">{product.description}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                ))
              )}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultScrewedModularImages[0]}
                    alt="Screwed Modular Tool Holders Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                    </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultScrewedModularImages[imageIndex % defaultScrewedModularImages.length]
                  : defaultScrewedModularImages[index % defaultScrewedModularImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Screwed Modular Tool Holders Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                    </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Modular Screwed Connection & Cutter Head Geometries":
                      return <Layers className="h-6 w-6 text-blue-600 mr-3" />
                    case "Insert Systems & Secure Clamping Technology":
                      return <Shield className="h-6 w-6 text-green-600 mr-3" />
                    case "Tool Holder Design & Internal Cooling Systems":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                    </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios & Processing</h2>
            </div>
              <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Right Angle Shoulder Fine Milling:</strong> Primary application for heads like R390 and ASMM, producing highly accurate shoulders and walls with exceptional surface finishes</span>
                  </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Pocket Milling & Slot/Plunge Milling:</strong> Utilizing versatility of AHUM, TRSM, and multi-functional TJU/ASJ heads to efficiently machine pockets and slots</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Copy Milling & 3D Profiling:</strong> Using smooth cutting action of corner rounding heads (EMRM, TRSM) to machine complex, contoured surfaces in mold and die applications</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Helical Interpolation (Drilling/Milling):</strong> Highly efficient method using TJU or ASJ heads to create large-diameter holes by milling in helical path</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Cost-Sensitive & High-Variety Environments:</strong> Ideal for job shops where ability to quickly switch from 90° shoulder mill to corner rounding tool provides immense flexibility</span>
                    </li>
                  </ul>
                </div>

              {/* Main Functions */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Main Functions
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Universal and Flexible Tooling Platform:</strong> Primary function allows single tool holder to accept wide variety of different cutting heads, dramatically increasing application flexibility</span>
                  </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Deliver High-Precision and High-Efficiency Finishing:</strong> Provide specialized cutter heads achieving exceptional accuracy, high feed rates, and superior surface finishes for demanding components</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Perform Versatile and Economical Roughing:</strong> Offer robust and economical cutter head solutions for heavy material removal, pocketing, and complex 3D contouring</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Combine Multiple Machining Operations:</strong> Integrate functions like drilling and milling into single tool, streamlining processes and reducing required tools</span>
                    </li>
                    <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Significantly Reduce Overall Tooling Costs:</strong> Lower investment in tooling inventory by separating long-lasting reusable shank from consumable, application-specific cutter head</span>
                    </li>
                  </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/clamp-type-milling/screwed-modular-tool-holders" />
            </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Flexible Modular Tooling Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal screwed modular tool holders for maximum flexibility and cost efficiency. From high-precision shoulder milling to multi-functional drilling and milling operations, we provide comprehensive modular solutions that adapt to your changing production needs while reducing tooling inventory costs.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same clamp-type-milling directory
                const allClampTypeCategories = [
                  {
                    title: "Face Milling Cutters",
                    image: "/images/D04-1.png",
                    description: "High-performance indexable face milling solutions",
                    url: "/standard-tools/clamp-type-milling/face-milling-cutters",
                  },
                  {
                    title: "Right Angle Face Milling Cutters",
                    image: "/images/D03-1.png",
                    description: "Precise 90-degree shoulder machining solutions",
                    url: "/standard-tools/clamp-type-milling/right-angle-square-shoulder",
                  },
                  {
                    title: "Ball End Milling Cutters",
                    image: "/images/D46-1.png",
                    description: "3D contouring and surface finishing solutions",
                    url: "/standard-tools/clamp-type-milling/ball-end-milling-cutters",
                  },
                  {
                    title: "High Feed Milling Cutter",
                    image: "/images/D07-2.png",
                    description: "Maximum productivity milling solutions",
                  url: "/standard-tools/clamp-type-milling/high-feed-milling-cutter",
                },
                {
                    title: "Chamfering Cutters",
                    image: "/images/D55-1.png",
                    description: "Precision chamfering and edge breaking solutions",
                    url: "/standard-tools/clamp-type-milling/chamfering-cutters",
                  },
                ];
                
                return allClampTypeCategories.map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Clamp Type Milling" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
}