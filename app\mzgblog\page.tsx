import Image from "next/image"
import Link from "next/link"
import type { Metadata } from "next"
import { getAllBlogPosts, getAllCategories } from "@/app/actions/blog-actions-db"
import { formatDate } from "@/lib/utils"
import Header from "@/components/header"
import Footer from "@/components/footer"

export const metadata: Metadata = {
  title: "Blog | MZG Tools",
  description: "The latest news and insights from MZG Tools",
}

// Safe image URL function to handle invalid URLs
function getSafeImageUrl(imageUrl?: string): string {
  const defaultImage = "/placeholder.svg?height=400&width=600&query=industrial tool"
  
  if (!imageUrl) {
    return defaultImage
  }
  
  // Check if it's a relative path starting with /
  if (imageUrl.startsWith('/')) {
    return imageUrl
  }
  
  // For absolute URLs, try to validate them
  try {
    new URL(imageUrl)
    return imageUrl
  } catch {
    return defaultImage
  }
}

export default async function BlogPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  const posts = await getAllBlogPosts()
  const categories = await getAllCategories()
  const resolvedSearchParams = await searchParams

  // 只显示已发布的文章
  const publishedPosts = posts.filter((post) => post.published !== false)

  // 处理分类过滤
  const categoryFilter = resolvedSearchParams.category as string | undefined
  const tagFilter = resolvedSearchParams.tag as string | undefined

  let filteredPosts = publishedPosts

  if (categoryFilter) {
    filteredPosts = filteredPosts.filter((post) => 
      post.category?.slug.toLowerCase() === categoryFilter.toLowerCase()
    )
  }

  if (tagFilter) {
    filteredPosts = filteredPosts.filter((post) =>
      post.tags?.some((tag) => tag.slug.toLowerCase() === tagFilter.toLowerCase()),
    )
  }

  // 按日期排序（最新的在前）
  filteredPosts.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())

  return (
    <>
      <Header />
      <main className="min-h-screen bg-white">
        {/* Hero Banner */}
        <div className="relative w-full h-[300px] md:h-[400px]">
          <Image src="/images/blog/blog-banner.png" alt="MZG Blog" fill className="object-cover" priority />
          <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
            <div className="text-center">
              <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">MZG BLOG</h1>
              <p className="text-xl text-gray-200">The latest news and insights from MZG Tools</p>
            </div>
          </div>
        </div>

        {/* Category Navigation */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="container mx-auto px-4 py-4 overflow-x-auto">
            <div className="flex items-center mb-4">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-2xl font-bold">Categories</h2>
            </div>
            <div className="flex space-x-6 min-w-max">
              <Link
                href="/mzgblog"
                className={`text-sm font-medium py-2 px-4 rounded-lg transition-all duration-300 ${
                  !categoryFilter 
                    ? "bg-red-600 text-white shadow-md" 
                    : "bg-gray-100 text-gray-700 hover:bg-red-100 hover:text-red-600"
                }`}
              >
                All Posts
              </Link>
              {Array.isArray(categories) && categories.map((category) => (
                <Link
                  key={category.id}
                  href={`/mzgblog?category=${category.slug}`}
                  className={`text-sm font-medium py-2 px-4 rounded-lg transition-all duration-300 ${
                    categoryFilter === category.slug
                      ? "text-white shadow-md"
                      : "bg-gray-100 text-gray-700 hover:bg-red-100 hover:text-red-600"
                  }`}
                  style={categoryFilter === category.slug ? { backgroundColor: category.color } : {}}
                >
                  {category.name}
                </Link>
              ))}
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Blog Posts Grid */}
        <div className="container mx-auto px-4 py-12">
          <div className="flex items-center mb-8">
            <div className="w-12 h-1 bg-red-600 mr-4"></div>
            <h2 className="text-3xl font-bold">
              {categoryFilter 
                ? `${categories.find(c => c.slug === categoryFilter)?.name || 'Category'} Posts`
                : 'Latest Posts'
              }
            </h2>
          </div>

          {filteredPosts.length === 0 ? (
            <div className="text-center py-12 bg-white rounded-lg shadow-sm border border-gray-100">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">No posts found</h2>
              <p className="text-gray-600">
                {categoryFilter || tagFilter
                  ? "Try selecting a different category or removing filters"
                  : "Check back soon for new content"}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {Array.isArray(filteredPosts) && filteredPosts.map((post) => (
                <Link key={post.id} href={`/mzgblog/${post.slug}`} className="group">
                  <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-lg h-full flex flex-col">
                  <div className="relative h-48">
                    <Image
                      src={getSafeImageUrl(post.imageUrl)}
                      alt={post.title}
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                    <div className="absolute top-4 left-4">
                      {post.category && (
                        <span 
                          className="text-white text-xs font-medium px-2 py-1 rounded"
                          style={{ backgroundColor: post.category.color }}
                        >
                          {post.category.name}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="p-5 flex-1 flex flex-col">
                    <div className="text-gray-500 text-sm mb-2">
                      {formatDate(post.createdAt)}
                    </div>
                    <h2 className="text-xl font-bold mb-3 group-hover:text-red-600 transition-colors leading-tight">
                      {post.title}
                    </h2>
                    <p className="text-gray-600 mb-4 flex-1 text-sm leading-relaxed">{post.excerpt}</p>
                    
                    {/* Tags */}
                    {post.tags && Array.isArray(post.tags) && post.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mb-4">
                        {post.tags.slice(0, 3).map((tag) => (
                          <span
                            key={tag.id}
                            className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded"
                          >
                            #{tag.name}
                          </span>
                        ))}
                        {post.tags.length > 3 && (
                          <span className="text-gray-400 text-xs">
                            +{post.tags.length - 3} more
                          </span>
                        )}
                      </div>
                    )}
                    
                    <div className="text-red-600 font-medium text-sm flex items-center mt-auto">
                      Read More
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4 ml-1 transition-transform group-hover:translate-x-1"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}
        </div>
      </main>
      
      <Footer />
    </>
  )
}