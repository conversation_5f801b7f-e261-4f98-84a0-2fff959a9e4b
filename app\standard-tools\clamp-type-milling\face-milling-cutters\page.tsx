"use client"

import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Download, ChevronRight, Info, PenToolIcon as Tool, Settings, Layers, Zap, Shield, Target, Drill, Wrench, Cog, CircleDot, Crosshair } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import FAQSectionEn from "@/components/faq-section-en"
import ProductCard from "@/components/product-card"
import { useState, useEffect } from "react"

export default function FaceMillingCuttersPage() {
  // Face Milling Cutters相关的默认图片
  const defaultFaceMillingImages = [
    "/images/D116-1.png",
    "/images/D117-1.png",
    "/images/D114-1.png",
    "/images/D112-1.png",
    "/images/90-degree-face-mill.png",
    "/images/45-degree-face-mill.png", 
    "/images/round-insert-face-mill.png",
    "/images/fine-pitch-face-mill.png",
    "/images/coarse-pitch-face-mill.png",
    "/images/wiper-insert-face-mill.png",
    "/images/high-feed-face-mill.png",
    "/images/aluminum-face-mill.png"
  ];

  // Gallery images for rotation - will be loaded from API
  const [galleryImages, setGalleryImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  // State for rotating images
  const [currentMainImage, setCurrentMainImage] = useState(0);

  // Load gallery images from API
  const loadGalleryImages = async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch("/api/admin-mzg/product-gallery?pagePath=/standard-tools/clamp-type-milling/face-milling-cutters");
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.images.length > 0) {
          const imageUrls = data.images.map((img: any) => img.imageUrl);
          setGalleryImages(imageUrls);
        } else {
          // API返回成功但没有图片，使用默认Face Milling图片
          setGalleryImages(defaultFaceMillingImages);
        }
      } else {
        // API请求失败，使用默认Face Milling图片
        setGalleryImages(defaultFaceMillingImages);
      }
    } catch (error) {
      console.error("加载图片失败:", error);
      // 网络错误或其他异常，使用默认Face Milling图片
      setGalleryImages(defaultFaceMillingImages);
    } finally {
      setIsLoadingImages(false);
    }
  };

  // Auto-rotate effect
  useEffect(() => {
    // 首先设置默认Face Milling图片，避免显示无关图片
    setGalleryImages(defaultFaceMillingImages);
    setIsLoadingImages(false);
    
    // 然后异步加载API图片
    loadGalleryImages();
  }, []);

  // 单独的useEffect处理图片轮播
  useEffect(() => {
    if (galleryImages.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentMainImage((prev) => (prev + 1) % galleryImages.length);
    }, 20000); // 每20秒轮换一次

    return () => clearInterval(interval);
  }, [galleryImages.length]);

  // Product data based on existing content - 保留原有产品数据
  const products = [
    {
      id: "face-mill-001",
      name: "SE/SD/SP Series Face Milling Cutter",
      image: "/images/D116-1.png",
      description: "High-rigidity carbide shim face milling cutter with different positive angle inserts",
      diameter: "Ø50~Ø300mm",
      insertType: "SEKN/SDKN/SPKN Series",
      application: "Uses different positive angle inserts (11°, 15°, 20°) with high-rigidity carbide shims",
      features: "Multiple positive angle options, high-rigidity carbide shims",
      pageNumber: "D116",
    },
    {
      id: "face-mill-002",
      name: "FP75° Face Milling Cutter",
      image: "/images/D117-1.png",
      description: "High-efficiency face milling cutter with 11° positive angle insert",
      diameter: "Ø63~Ø250mm",
      insertType: "SPKN1203",
      application: "Uses 11° positive angle insert with high-rigidity carbide shims",
      features: "11° positive angle design, high-rigidity carbide shims",
      pageNumber: "D117",
    },
    {
      id: "face-mill-003",
      name: "45° Planar End Milling Cutter Head",
      image: "/images/D114-1.png",
      description: "Lightweight aluminum shell cutter head for high-efficiency 45° machining",
      diameter: "Ø40~Ø200mm",
      insertType: "SE..1204..",
      application: "Aluminum shell, lightweight design, high-efficiency 45° cutting. Suitable for steel, cast iron, and aluminum materials",
      features: "Lightweight aluminum shell, 45° high-gloss efficiency, multi-material compatibility",
      pageNumber: "D114",
    },
    {
      id: "face-mill-004",
      name: "ASX445/R245 Cutter",
      image: "/images/D112-1.png",
      description: "Large rake angle cutter with reduced cutting load and enhanced cutting rigidity",
      diameter: "Ø80~Ø315mm",
      insertType: "SEMT13T3, SEET-12T3",
      application: "Large rake angle inserts provide reduced cutting load and better cutting rigidity",
      features: "Large rake angle design, reduced cutting load, enhanced cutting rigidity",
      pageNumber: "D112",
    },

  ]

  // Performance features
  const performanceFeatures = [
    {
      icon: "Shield",
      title: "Economical and Versatile Planar Milling Performance",
      description: "SE, SD, and SP series face milling cutters provide reliable and consistent performance for general plane milling with outstanding economy and up to four indexable cutting edges.",
    },
    {
      icon: "Zap", 
      title: "High-Efficiency and Robust Performance",
      description: "FP75°, ASX445, and R245 cutters deliver uncompromising performance with high-rigidity carbide shims for aggressive, high-feed machining operations.",
    },
    {
      icon: "Target",
      title: "Specialized High-Gloss Finishing Performance",
      description: "45° Planar Aluminum Face Milling Heads (AL-KM & AL-AP) with lightweight aluminum shell and fine axial adjustment for mirror finish on non-ferrous materials.",
    },
  ]

  // Helper function to render icons
  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case "Shield":
        return <Shield className="h-8 w-8 text-red-600" />
      case "Zap":
        return <Zap className="h-8 w-8 text-red-600" />
      case "Target":
        return <Target className="h-8 w-8 text-red-600" />
      default:
        return <Tool className="h-8 w-8 text-red-600" />
    }
  }

  // Technical specifications
  const technicalSpecs = [
    {
      title: "Cutter Body Design",
      description: "Cutter bodies available from Ø50mm up to Ø300mm cutting diameters, manufactured from high-strength steel for durability, with specialized lightweight aluminum alloy bodies for high-speed applications.",
    },
    {
      title: "Approach / Lead Angle",
      description: "Standard approach angles include 45° for general-purpose balance of cutting forces and high feed rates, 75° for strength and efficiency combination, and 90° for true square shoulder milling capabilities.",
    },
    {
      title: "Insert Clamping System",
      description: "Robust screw-down clamp type mechanism with high-tensile Torx screws. High-performance cutters feature solid carbide shims between insert and cutter body for rigid support and protection.",
    },
  ]

  return (
    <>
      <Header />
      <div className="bg-white">
        {/* Hero Section */}
        <div className="relative bg-white text-gray-900 border-b border-gray-200">
          <div className="relative container mx-auto px-4 py-6 md:py-10">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="max-w-4xl">
                <div className="inline-block bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium mb-4">
                  Clamp Type Milling Expert Guide
                </div>
                <h1 className="text-[28px] font-bold mb-6 leading-tight">
                  Clamp-Type Face Milling Cutters
                </h1>
                <p className="text-sm mb-8 text-gray-600 leading-relaxed">
                  MZG Clamp Type Face Milling Cutters. These tools are the quintessential solution for all planar milling operations, from heavy-duty roughing to high-gloss finishing. The core design principle is the "clamp type" mechanism, where precision-engineered indexable inserts are securely fastened onto a robust cutter body. This system is the bedrock of modern manufacturing efficiency, providing unparalleled economy through the use of multi-edged, replaceable inserts, and immense versatility to adapt to virtually any material or application.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Request Quote
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-100 border-gray-300 hover:text-gray-900 transition-all duration-300"
                  >
                    Download Catalog <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="w-[563px] h-[400px] flex items-center justify-center">
                  <Image
                    src="/images/millingcutter1.png"
                    alt="MZG Professional Clamp-Type Face Milling Cutters"
                    width={563}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
        </div>

        {/* Performance Features */}
        <div className="container mx-auto px-4 py-5">
          <div className="grid md:grid-cols-3 gap-8">
            {performanceFeatures.map((feature, index) => {
              const getIcon = (iconName: string) => {
                switch (iconName) {
                  case "Shield":
                    return <Shield className="h-6 w-6 text-blue-600 mr-3" />
                  case "Zap":
                    return <Zap className="h-6 w-6 text-green-600 mr-3" />
                  case "Target":
                    return <Target className="h-6 w-6 text-purple-600 mr-3" />
                  default:
                    return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                }
              }
              
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    {getIcon(feature.icon)}
                    {feature.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed text-sm">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          {/* Product Performance Section */}
          <div className="mb-16">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Performance</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="prose prose-xs max-w-none">
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      The performance of our face milling systems is stratified to deliver specialized capabilities, showcasing the depth and precision of our engineering. Our portfolio is meticulously designed to offer a specific solution for every face milling challenge, ensuring optimal performance, surface quality, and cost-effectiveness.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Economical and Versatile Planar Milling Performance:</strong> The foundation of our offering is built upon the <strong>SE, SD, and SP series</strong> of face milling cutters. The <strong>SE series</strong> (using SEMT, SEET, SEKT inserts) are the all-rounders, providing reliable and consistent performance for general plane milling. The <strong>SD-45° series</strong> is a champion of cost-effectiveness; its use of a <strong>square SDMB insert</strong> provides up to four indexable cutting edges, dramatically lowering the cost-per-edge.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>High-Efficiency and Robust Performance:</strong> For applications where Metal Removal Rate (MRR) is the primary driver, our <strong>FP75°, ASX445, and R245</strong> cutters deliver uncompromising performance. These tools are engineered for aggressive, high-feed machining with <strong>high-rigidity carbide shims</strong> beneath each insert for stable cutting platform and enhanced system rigidity.
                    </p>
                    <p className="mb-3 text-sm leading-relaxed text-gray-700">
                      <strong>Specialized High-Gloss Finishing Performance:</strong> When surface finish is paramount, particularly on non-ferrous materials, our <strong>45° Planar Aluminum Face Milling Heads (AL-KM & AL-AP)</strong> deliver peerless performance with lightweight aluminum shell and optional <strong>PCD (Polycrystalline Diamond) finishing inserts</strong> for exceptionally high-quality surfaces.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                    <Info className="h-6 w-6 text-blue-600 mr-3" />
                    Key Performance Indicators
                  </h3>
                  <ul className="space-y-3 text-sm text-gray-700">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Multi-Edge Economy:</strong> Up to 4 indexable cutting edges per insert</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>High MRR:</strong> Aggressive high-feed machining capability</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Superior Finish:</strong> Mirror-like surface quality on aluminum</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Material Versatility:</strong> Steel to aluminum applications</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                      <span><strong>Carbide Shim Support:</strong> Enhanced rigidity and protection</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Our Products */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Our Products</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="group bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-200"
                >
                  <div className="relative w-full bg-white" style={{ height: "160px" }}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-contain p-4 transition-transform duration-500 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-5 border-t">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-sm font-bold line-clamp-2 flex-1 mr-2">{product.name}</h3>
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap">{product.pageNumber}</span>
                    </div>
                    <div className="space-y-2 text-xs">
                      {product.diameter && (
                        <div className="flex justify-between">
                          <span className="font-medium text-gray-700">Diameter:</span>
                          <span className="text-gray-900 text-right">{product.diameter}</span>
                        </div>
                      )}
                      {product.application && (
                        <div className="pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-600">{product.application}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product Gallery */}
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Product Gallery</h2>
              {isLoadingImages && (
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  Loading latest images...
                </div>
              )}
            </div>
            <div className="grid grid-cols-6 grid-rows-4 gap-3 h-[300px]">
              {/* Large center-left image - 主要轮播图 */}
              <div className="col-span-2 row-span-4 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center overflow-hidden group">
                {galleryImages.length > 0 ? (
                  <Image
                    src={galleryImages[currentMainImage] || defaultFaceMillingImages[0]}
                    alt="Face Milling Cutters Product"
                    width={480}
                    height={480}
                    quality={100}
                    priority
                    className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    <div className="animate-pulse">Loading...</div>
                  </div>
                )}
              </div>

              {/* Middle section and Right section - 小图片网格 */}
              {Array.from({ length: 8 }, (_, index) => {
                const imageIndex = (currentMainImage + index + 1) % galleryImages.length;
                const imageSrc = galleryImages.length > 0 
                  ? galleryImages[imageIndex] || defaultFaceMillingImages[imageIndex % defaultFaceMillingImages.length]
                  : defaultFaceMillingImages[index % defaultFaceMillingImages.length];
                
                return (
                  <div 
                    key={index}
                    className="col-span-1 row-span-2 bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-center cursor-pointer hover:border-red-300 transition-colors duration-300 overflow-hidden group"
                    onClick={() => galleryImages.length > 0 && setCurrentMainImage((currentMainImage + index + 1) % galleryImages.length)}
                  >
                    <Image
                      src={imageSrc}
                      alt={`Face Milling Cutters Product ${index + 1}`}
                      width={280}
                      height={280}
                      quality={100}
                      className="object-contain w-full h-full transition-all duration-500 group-hover:scale-125"
                    />
                  </div>
                );
              })}
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Technical Parameters</h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {technicalSpecs.map((spec, index) => {
                const getIcon = (title: string) => {
                  switch (title) {
                    case "Cutter Body Design":
                      return <Layers className="h-6 w-6 text-blue-600 mr-3" />
                    case "Approach / Lead Angle":
                      return <Shield className="h-6 w-6 text-green-600 mr-3" />
                    case "Insert Clamping System":
                      return <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    default:
                      return <Tool className="h-6 w-6 text-gray-600 mr-3" />
                  }
                }
                
                return (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                      {getIcon(spec.title)}
                      {spec.title}
                    </h3>
                    <p className="text-gray-700 leading-relaxed text-sm">{spec.description}</p>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Application Scenarios & Processing */}
          <div className="mb-16">
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Application Scenarios & Applicable Machining</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Application Scenarios */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Wrench className="h-6 w-6 text-blue-600 mr-3" />
                  Application Scenarios
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>General Face Milling:</strong> Primary application for creating flat, parallel surfaces on raw stock or castings</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Heavy-Duty Roughing:</strong> Using FP75° and ASX445 cutters to rapidly remove large volumes of material from steel, cast iron, and stainless steel</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Quality Finishing:</strong> Employing specialized aluminum cutters to produce mirror-like surfaces on aluminum, brass, and copper components</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Square Shoulder Milling:</strong> Using 90° cutters to simultaneously mill a flat face and a perpendicular wall</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>End Face Grooving and Chamfering:</strong> Applying multi-functional tools like the SD series to perform secondary operations</span>
                  </li>
                </ul>
              </div>

              {/* Machining Operations */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                  <Settings className="h-6 w-6 text-green-600 mr-3" />
                  Insert Geometries & Systems
                </h3>
                <ul className="space-y-3 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Square Inserts:</strong> SEMT, SEET, SEKT, SEHT, SDMB, SDKN for multiple cutting edges and strong geometry</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Specialized Inserts:</strong> SPMG/SPGX for multi-functional tools and capability to mount PCD inserts for ultimate finishing</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>High-Performance Features:</strong> Carbide shims in FP75°, ASX445, R245 cutters for enhanced rigidity and protection</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Aluminum Optimization:</strong> Lightweight aluminum bodies with fine axial adjustment for micro-level tuning</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Material Versatility:</strong> ISO-standard inserts providing solutions for every material from steel to aluminum</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Functions */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Main Functions</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Target className="h-5 w-5 text-red-600 mr-2" />
                  Primary Functions
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Create Accurate and Flat Surfaces:</strong> Machine planar surfaces to precise specifications of flatness and parallelism</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Maximize Manufacturing Economy:</strong> Significantly reduce tooling costs using economical, multi-edged, replaceable indexable inserts</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-red-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Deliver High Metal Removal Rates:</strong> Provide robust, high-performance tooling for aggressive machining operations</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                  <Zap className="h-5 w-5 text-blue-600 mr-2" />
                  Performance Benefits
                </h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Achieve Superior Surface Quality:</strong> Offer specialized, precision-adjustable systems for exceptionally fine surface finishes</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Provide Ultimate Material Versatility:</strong> Single cutter body machines wide range of materials by changing insert grade</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 mt-2 shrink-0"></div>
                    <span><strong>Economic Efficiency:</strong> Reduced machine downtime and tooling costs through extended tool life and higher speeds</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-5">
            <FAQSectionEn pageUrl="/standard-tools/clamp-type-milling/face-milling-cutters" />
          </div>

          {/* CTA Section */}
          <div className="bg-white py-5">
            <div className="container mx-auto px-4 border border-gray-200 rounded-2xl shadow-sm">
              <div className="mx-auto text-center px-8 py-16">
                <h2 className="text-3xl font-bold mb-4 text-gray-900">Need Professional Face Milling Solutions?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Our technical team can help you select optimal clamp-type face milling cutters for specific planar milling applications. From heavy-duty roughing to high-gloss finishing, we provide comprehensive face milling solutions for all materials and operations.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white transition-all duration-300 shadow-sm hover:shadow-md">
                    Contact Technical Support
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400 transition-all duration-300"
                  >
                    Request Custom Solutions
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Categories */}
          <div>
            <div className="flex items-center mb-8">
              <div className="w-12 h-1 bg-red-600 mr-4"></div>
              <h2 className="text-3xl font-bold">Related Categories</h2>
            </div>
            <div className="grid grid-cols-5 gap-6">
              {(() => {
                // Define all categories in the same clamp-type-milling directory
                const allClampTypeCategories = [
                  {
                    title: "Ball End Milling Cutters",
                    image: "/images/D46-1.png",
                    description: "Spherical end mill for profiling machining",
                    url: "/standard-tools/clamp-type-milling/ball-end-milling-cutters",
                  },
                  {
                    title: "Right Angle End Mill",
                    image: "/images/D32-1.png",
                    description: "Right angle end mill system",
                    url: "/standard-tools/clamp-type-milling/right-angle-end-mill",
                  },
                  {
                    title: "High Feed Milling Cutter",
                    image: "/images/D28-1.png",
                    description: "High efficiency milling solutions",
                    url: "/standard-tools/clamp-type-milling/high-feed-milling-cutter",
                  },
                  {
                    title: "Chamfering Cutters",
                    image: "/images/D42-1.png",
                    description: "Machine clip chamfering cutters",
                    url: "/standard-tools/clamp-type-milling/chamfering-cutters",
                  },
                  {
                    title: "Corn Roughing",
                    image: "/images/D70-1.png",
                    description: "Corn roughing milling cutters",
                    url: "/standard-tools/clamp-type-milling/corn-roughing",
                  },
                ]
                
                return allClampTypeCategories.slice(0, 5).map((category, index) => (
                  <ProductCard key={index} image={category.image} title={category.title} category="Clamp Type Milling" url={category.url} />
                ));
              })()}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
} 