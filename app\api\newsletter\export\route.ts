import { NextRequest, NextResponse } from 'next/server'
import { sql } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    // 获取所有订阅数据
    const newsletters = await sql`
      SELECT id, subscribe_mail, ip_address, created_at, is_active 
      FROM newsletter 
      ORDER BY created_at DESC
    `

    // 格式化数据为CSV
    const headers = ['ID', '邮箱地址', 'IP地址', '订阅时间', '状态']
    const csvData = [
      headers.join(','),
      ...newsletters.map(item => [
        item.id,
        `"${item.subscribe_mail}"`, // 用引号包围邮箱以防止CSV解析问题
        `"${item.ip_address}"`,
        `"${new Date(item.created_at).toLocaleString('zh-CN')}"`,
        item.is_active ? '激活' : '停用'
      ].join(','))
    ].join('\n')

    // 添加BOM以确保中文正确显示
    const bom = '\uFEFF'
    const csvContent = bom + csvData

    // 创建响应
    const response = new NextResponse(csvContent)
    
    // 设置响应头
    response.headers.set('Content-Type', 'text/csv; charset=utf-8')
    response.headers.set('Content-Disposition', `attachment; filename="newsletter-subscriptions-${new Date().toISOString().split('T')[0]}.csv"`)
    response.headers.set('Cache-Control', 'no-cache')

    return response

  } catch (error) {
    console.error('导出Newsletter数据失败:', error)
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : '导出失败' 
    }, { status: 500 })
  }
} 