# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

MZG Tools is a professional industrial milling tools platform built with Next.js 14, featuring product catalogs, custom quotes, and comprehensive admin management with PostgreSQL database integration.

## Quick Commands

```bash
# Development
npm run dev                    # Start development server
npm run build                  # Build for production
npm run start                  # Start production server
npm run lint                   # Run ESLint

# Database  
npm run db:setup              # Initialize database (via init-database.js)
node scripts/init-database.js # Manual database initialization
npm run init-consultation     # Initialize consultation table
npm run migrate-email-fields  # Migrate consultation email fields

# Testing
node test-project.js          # Run comprehensive project tests
node test-faq-db.js          # Test FAQ database functionality

# Common operations
npm install --legacy-peer-deps # Install dependencies (handle peer deps)
```

## Architecture

### Core Structure
- **Next.js 14 App Router** with React 19
- **PostgreSQL** via Neon Database (@neondatabase/serverless)
- **Tailwind CSS** + **shadcn/ui** for styling
- **TypeScript** throughout

### Key Directories
```
app/
├── admin-mzg/           # Admin dashboard (auth required)
│   ├── login/          # Admin login page
│   ├── users/          # Frontend user management
│   ├── manage-users/   # Admin account management
│   ├── products/       # Product management
│   ├── quotes/         # Quote management
│   └── database/       # Database management
├── api/
│   └── admin-mzg/      # Admin API endpoints
├── custom-quote/       # Custom quote form
├── mzgblog/           # Blog pages
└── standard-tools/    # Product catalog

lib/
├── auth-mzg.ts        # Admin authentication
├── database.ts        # Database connection & init
├── auth-service.ts    # User authentication
└── utils.ts          # Utility functions
```

### Database Schema

**Admin System:**
- `admin_users` - Admin accounts (username, role: super_admin/admin)
- `admin_sessions` - Session management
- `users` - Frontend user accounts with full profile
- `user_sessions` - User session tracking
- `user_activity_logs` - User activity tracking

**Business Data:**
- `consultations` - Quote requests with attachments
- `products` - Product catalog
- `product_categories` - Product categorization

### Authentication Flow
1. **Admin Login**: `/admin-mzg/login` → session cookie → protected routes
2. **Users**: Frontend users managed via `/admin-mzg/users`
3. **Roles**: super_admin (full access) vs admin (limited access)

## Environment Setup

Required `.env.local`:
```env
DATABASE_URL=******************************************************************
NEXTAUTH_SECRET=your-secret-key
```

## Key Features

### Admin Dashboard (`/admin-mzg`)
- **User Management**: Full CRUD for frontend users with roles/permissions
- **Admin Management**: Super admin can manage other admin accounts
- **Product Management**: Product catalog with categories
- **Quote Management**: Handle customer quote requests with attachments
- **Database Management**: Visual database browser with import/export

### API Endpoints
```
# Admin accounts
GET/POST   /api/admin-mzg/manage-users
PUT/DELETE /api/admin-mzg/manage-users/[id]

# User management
GET/POST   /api/admin-mzg/users
PUT/DELETE /api/admin-mzg/users/[id]

# Quote management
GET        /api/admin-mzg/quotes
PUT/DELETE /api/admin-mzg/quotes/[id]
GET        /api/admin-mzg/quotes/export
```

### Development Tools
- **Stagewise Toolbar**: AI-powered UI editor (dev mode only)
- **Database Management**: Full visual database management
- **Test Scripts**: Comprehensive testing via `test-project.js`

## Critical Files
- `lib/database.ts` - Database initialization and connection
- `lib/auth-mzg.ts` - Admin authentication middleware
- `app/admin-mzg/login/page.tsx` - Admin login page
- `test-project.js` - Project health check script

## Security Notes
- Session tokens in HTTP-only cookies
- Passwords stored as Base64 (consider bcrypt for production)
- Role-based access control throughout
- Database operations use transactions

## Default Credentials
- **Admin**: admin / mzgtools2024
- **Database**: Auto-initialized on first admin login

## Development Guidelines (Cursor Rules)  

This project follows the Chinese development standards as defined in `.cursorrules`:
- **Code Continuity**: When extending V0.dev generated code, maintain consistency with existing patterns and architecture
- **Technology Stack**: React 19 + Next.js 14 + Tailwind CSS + shadcn/ui components
- **Component Design**: Follow V0.dev component structure and design patterns when adding new features  
- **State Management**: Use React hooks (useState, useContext) for simple state, consider more complex solutions only when necessary
- **Progressive Development**: Start from V0.dev base code and incrementally add features rather than rebuild
- **Responsive Design**: Ensure all new components work across different device sizes
- **Security**: Sanitize user inputs, use environment variables for secrets, follow least privilege principle
- **Performance**: Implement lazy loading, code splitting, and Next.js optimization features

## Modern Stack Features

### React 19 Compatibility
- Uses React 19 with proper type definitions
- Server/Client component separation for optimal performance
- Hydration warnings suppressed in layout.tsx and theme-provider.tsx

### next.config.mjs Features
- ESLint and TypeScript build error tolerance for rapid development  
- Package import optimization for @radix-ui and lucide-react
- Conditional Stagewise toolbar loading (dev-only)
- External image domains configured for Vercel storage and Unsplash

### Development Tools Integration
- **Stagewise Toolbar**: AI-powered UI editor enabled in development mode
- **Legacy Peer Deps**: Use `--legacy-peer-deps` flag for dependency installation
- **Test Suite**: Comprehensive project health checks via `test-project.js`